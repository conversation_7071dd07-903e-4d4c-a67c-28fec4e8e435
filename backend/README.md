# backend

#### 介绍
学习交流分享

#### 软件架构
软件架构说明
```
    Mysql
    Django
    Python
    Nginx
    Redis
    Celery
    Docker
    Jenkins
```

#### 安装教程
手动部署
```
cd /backend
1、创建虚拟环境执行依赖包：pip install -r requirements.txt
2、backend/primaryApp/settings/dev.py或pro.py修改自己的数据库和Redis配置信息
3、数据库迁移：python manage.py makemigrations
4、数据库执行迁移文件：python manage.py migrate
5、脚本初始化执行:
python manage.py shell -c "
import django
from django.apps import apps
from django.db import models

# 动态创建Project模型
if 'projects' not in apps.app_configs:
    class TempProject(models.Model):
        class Meta:
            app_label = 'projects'
    
    apps.app_configs['projects'] = type('TempConfig', (), {
        'label': 'projects',
        'models': {'project': TempProject}
    })

# 正常初始化
django.setup()

from django.contrib.auth import get_user_model
try:
    from apps.projects.models import Project
except RuntimeError:
    # 如果仍然失败，使用动态创建的模型
    Project = apps.get_model('projects', 'Project')

print('=== 初始化开始 ===')

# 创建管理员
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '13888888888', '123456')
    print('管理员创建成功: admin/123456')
else:
    print('管理员已存在')

# 创建示例项目
try:
    leader = User.objects.get(username='admin')
    if not Project.objects.filter(name='示例项目').exists():
        Project.objects.create(name='示例项目', desc='这是一个示例项目', leader=leader)
        print('项目创建成功')
    else:
        print('示例项目已存在')
except Exception as e:
    print('错误:', str(e))

print('=== 初始化完成 ===')
"
6、运行Django服务：python manage.py runserver
```
自动部署
```
1、服务器中安装docker和docker-compose
2、运行 sh deploy.sh
```
#### 使用说明
static配置文件生成
```
python manage.py collectstatic
```
数据库迁移
```
python manage.py makemigrations
```
数据库执行迁移文件
```
python manage.py migrate
```
运行Django服务
```
python manage.py runserver
```
#### 项目目录结构

#### 参与贡献
