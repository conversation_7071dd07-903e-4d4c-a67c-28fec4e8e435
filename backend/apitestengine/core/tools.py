# -*- coding: utf-8 -*-
# @author: HRUN

import base64
import random
import time
from datetime import datetime

import rsa
import hashlib
import base64


from faker import Faker
fk = Faker(locale='zh_CN')


def random_mobile():
    """随机生成手机号"""
    return fk.phone_number()


def random_name():
    """随机生成中文名字"""
    return fk.name()


def random_ssn():
    """随机生成一个省份证号"""
    return fk.ssn()


def random_addr():
    """随机生成一个地址"""
    return fk.address()


def random_city():
    """随机生成一个城市名"""
    return fk.city()


def random_company():
    """随机生成一个公司名"""
    return fk.company()


def random_postcode():
    """随机生成一个邮编"""
    return fk.postcode()


def random_email():
    """随机生成一个邮箱号"""
    return fk.email()


def random_date():
    """随机生成一个日期"""
    return fk.date()


def random_date_time():
    """随机生成一个时间"""
    return fk.date_time()


def random_ipv4():
    """随机生成一个ipv4的地址"""
    return fk.ipv4()


def get_timestamp():
    """生成当前时间戳"""
    return time.time()


def base64_encode(data: str):
    """base64编码"""
    return base64.b64encode(data.encode('utf-8')).decode('utf-8')

def get_time():
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time()))       # 格式:2021-11-11 14:54:16
    return now


def get_datatime(custom_hour=0, custom_minute=0, custom_second=0):
    current_date = time.strftime("%Y-%m-%d", time.localtime(time.time()))

    custom_time = f"{current_date} {custom_hour:02d}:{custom_minute:02d}:{custom_second:02d}"

    return custom_time
def md5_encrypt(data: str):
    """md5加密"""
    from hashlib import md5
    new_md5 = md5()
    new_md5.update(data.encode('utf-8'))
    return new_md5.hexdigest()


def random_number(min_value, max_value) ->int:

    return int(random.uniform(min_value, max_value))



# def rsa_encrypt(msg, server_pub):
#     """
#     rsa加密
#     :param msg: 待加密文本
#     :param server_pub: 密钥
#     :return:
#     """
#     msg = msg.encode('utf-8')
#     pub_key = server_pub.encode("utf-8")
#     public_key_obj = rsa.PublicKey.load_pkcs1_openssl_pem(pub_key)  #
#     cryto_msg = rsa.encrypt(msg, public_key_obj)  # 生成加密文本
#     cipher_base64 = base64.b64encode(cryto_msg)  # 将加密文本转化为 base64 编码
#     return cipher_base64.decode()


# def sha256_encode(data: str) -> str:
#     return hashlib.sha256(data.encode('utf-8')).hexdigest()
#
# def rsa_encrypt(pubkey: str, message: str) -> str:
#     # 加密
#     pub = '-----BEGIN RSA PRIVATE KEY-----\n' + pubkey + '\n-----END RSA PRIVATE KEY-----'
#     rsakey = RSA.importKey(pub)
#     cipher = Cipher_pkcs1_v1_5.new(rsakey)  # 创建用于执行pkcs1_v1_5加密或解密的密码
#     cipher_text = base64.b64encode(cipher.encrypt(message.encode('utf-8')))
#     return cipher_text.decode('utf-8')

