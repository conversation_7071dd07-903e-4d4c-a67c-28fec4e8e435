# -*- coding: utf-8 -*-
# @author: HRUN

import json
import csv
import io
import zipfile
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from django.http import HttpResponse
from django.core.serializers import serialize, deserialize
from django.core.exceptions import ValidationError
import logging

# 修复模型导入路径
from performance.models import PerformanceTask, TaskReport, TaskScence, TaskStep, server
from projects.models import Project, TestEnv

logger = logging.getLogger(__name__)


class ReportExporter:
    """性能测试报告导出器"""
    
    def export_single_report(self, report_id: int) -> HttpResponse:
        """导出单个性能测试报告为Excel格式"""
        try:
            from openpyxl import Workbook
            from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
            from openpyxl.chart import LineChart, Reference, Series
            from openpyxl.chart.marker import Marker
            
            # 获取报告数据
            from performance.models import TaskReport
            report = TaskReport.objects.get(id=report_id)
            
            # 创建工作簿
            wb = Workbook()
            
            # 设置样式
            title_font = Font(name='Arial', size=14, bold=True)
            header_font = Font(name='Arial', size=12, bold=True)
            normal_font = Font(name='Arial', size=11)
            
            header_fill = PatternFill(start_color='E0E0E0', end_color='E0E0E0', fill_type='solid')
            
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            center_aligned = Alignment(horizontal='center', vertical='center')
            left_aligned = Alignment(horizontal='left', vertical='center')
            
            # 创建概览工作表
            overview_sheet = wb.active
            overview_sheet.title = "报告概览"
            
            # 设置标题
            overview_sheet.merge_cells('A1:F1')
            overview_sheet['A1'] = f"性能测试报告: {report.reportName}"
            overview_sheet['A1'].font = title_font
            overview_sheet['A1'].alignment = center_aligned
            
            # 添加基本信息
            overview_sheet['A3'] = "报告名称:"
            overview_sheet['B3'] = report.reportName
            overview_sheet['A4'] = "任务名称:"
            overview_sheet['B4'] = report.task.taskName if report.task else ""
            overview_sheet['A5'] = "测试状态:"
            overview_sheet['B5'] = dict(TaskReport.choices_status).get(report.reportStatus, report.reportStatus)
            overview_sheet['A6'] = "执行人:"
            overview_sheet['B6'] = report.executor or ""
            
            overview_sheet['D3'] = "开始时间:"
            overview_sheet['E3'] = report.startTime.strftime('%Y-%m-%d %H:%M:%S') if report.startTime else ""
            overview_sheet['D4'] = "结束时间:"
            overview_sheet['E4'] = report.endTime.strftime('%Y-%m-%d %H:%M:%S') if report.endTime else ""
            overview_sheet['D5'] = "运行时长:"
            
            # 计算运行时长
            duration = ""
            if report.startTime and report.endTime:
                duration_seconds = (report.endTime - report.startTime).total_seconds()
                hours = int(duration_seconds // 3600)
                minutes = int((duration_seconds % 3600) // 60)
                seconds = int(duration_seconds % 60)
                duration = f"{hours}小时 {minutes}分钟 {seconds}秒"
            overview_sheet['E5'] = duration
            
            # 添加性能指标
            overview_sheet.merge_cells('A8:F8')
            overview_sheet['A8'] = "性能指标概览"
            overview_sheet['A8'].font = header_font
            overview_sheet['A8'].alignment = center_aligned
            
            headers = ["指标", "值", "指标", "值"]
            for col, header in enumerate(headers, start=1):
                cell = overview_sheet.cell(row=9, column=col)
                cell.value = header
                cell.font = header_font
                cell.fill = header_fill
                cell.border = thin_border
                cell.alignment = center_aligned
            
            metrics = [
                ("平均TPS", report.avgTps or 0, "平均响应时间", f"{report.avgResponseTime or 0} ms"),
                ("总请求数", report.totalRequests or 0, "成功请求数", report.successRequests or 0),
                ("失败请求数", report.failedRequests or 0, "错误率", f"{report.errorRate or 0}%"),
                ("平均CPU使用率", f"{report.avgCpu or 0}%", "平均内存使用率", f"{report.avgMemory or 0}%"),
            ]
            
            for row, (label1, value1, label2, value2) in enumerate(metrics, start=10):
                overview_sheet.cell(row=row, column=1).value = label1
                overview_sheet.cell(row=row, column=1).alignment = left_aligned
                overview_sheet.cell(row=row, column=1).border = thin_border
                
                overview_sheet.cell(row=row, column=2).value = value1
                overview_sheet.cell(row=row, column=2).alignment = center_aligned
                overview_sheet.cell(row=row, column=2).border = thin_border
                
                overview_sheet.cell(row=row, column=3).value = label2
                overview_sheet.cell(row=row, column=3).alignment = left_aligned
                overview_sheet.cell(row=row, column=3).border = thin_border
                
                overview_sheet.cell(row=row, column=4).value = value2
                overview_sheet.cell(row=row, column=4).alignment = center_aligned
                overview_sheet.cell(row=row, column=4).border = thin_border
            
            # 添加结果分析
            overview_sheet.merge_cells('A15:F15')
            overview_sheet['A15'] = "测试结果分析"
            overview_sheet['A15'].font = header_font
            overview_sheet['A15'].alignment = center_aligned
            
            overview_sheet.merge_cells('A16:F20')
            overview_sheet['A16'] = report.resultAnalyse or "无分析结果"
            overview_sheet['A16'].alignment = left_aligned
            
            # 创建详细指标工作表
            if report.reportResult:
                try:
                    report_result = json.loads(report.reportResult)
                    
                    # 如果有详细统计数据
                    if 'detailed_stats' in report_result:
                        metrics_sheet = wb.create_sheet("接口性能指标")
                        
                        # 设置标题
                        metrics_sheet.merge_cells('A1:L1')
                        metrics_sheet['A1'] = "接口性能指标详情"
                        metrics_sheet['A1'].font = title_font
                        metrics_sheet['A1'].alignment = center_aligned
                        
                        # 设置表头
                        headers = [
                            "接口名称", "总请求数", "成功请求数", "失败请求数", "并发用户数",
                            "最大响应时间(ms)", "最小响应时间(ms)", "平均响应时间(ms)",
                            "90%响应时间(ms)", "99%响应时间(ms)", "平均RPS", "错误率(%)"
                        ]
                        
                        for col, header in enumerate(headers, start=1):
                            cell = metrics_sheet.cell(row=2, column=col)
                            cell.value = header
                            cell.font = header_font
                            cell.fill = header_fill
                            cell.border = thin_border
                            cell.alignment = center_aligned
                        
                        # 添加详细指标数据
                        row = 3
                        for name, stats in report_result['detailed_stats'].items():
                            metrics_sheet.cell(row=row, column=1).value = name
                            metrics_sheet.cell(row=row, column=2).value = stats.get('total_requests', 0)
                            metrics_sheet.cell(row=row, column=3).value = stats.get('success_requests', 0)
                            metrics_sheet.cell(row=row, column=4).value = stats.get('failed_requests', 0)
                            metrics_sheet.cell(row=row, column=5).value = stats.get('current_users', 0)
                            metrics_sheet.cell(row=row, column=6).value = stats.get('max_response_time', 0)
                            metrics_sheet.cell(row=row, column=7).value = stats.get('min_response_time', 0)
                            metrics_sheet.cell(row=row, column=8).value = stats.get('avg_response_time', 0)
                            metrics_sheet.cell(row=row, column=9).value = stats.get('p90_response_time', 0)
                            metrics_sheet.cell(row=row, column=10).value = stats.get('p99_response_time', 0)
                            metrics_sheet.cell(row=row, column=11).value = stats.get('current_rps', 0)
                            metrics_sheet.cell(row=row, column=12).value = stats.get('error_rate', 0)
                            
                            for col in range(1, 13):
                                metrics_sheet.cell(row=row, column=col).border = thin_border
                                metrics_sheet.cell(row=row, column=col).alignment = center_aligned
                            
                            row += 1
                        
                        # 调整列宽
                        for col in range(1, 13):
                            metrics_sheet.column_dimensions[chr(64 + col)].width = 15
                    
                    # 如果有历史数据，创建图表
                    if 'stats_history' in report_result and report_result['stats_history']:
                        charts_sheet = wb.create_sheet("性能图表")
                        
                        # 设置标题
                        charts_sheet.merge_cells('A1:J1')
                        charts_sheet['A1'] = "性能测试图表"
                        charts_sheet['A1'].font = title_font
                        charts_sheet['A1'].alignment = center_aligned
                        
                        # 准备图表数据
                        history = report_result['stats_history']
                        timestamps = []
                        response_times = []
                        rps_values = []
                        tps_values = []
                        user_counts = []
                        
                        for entry in history:
                            timestamp = entry.get('timestamp', '')
                            if timestamp:
                                try:
                                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                                    timestamps.append(dt.strftime('%H:%M:%S'))
                                except:
                                    timestamps.append(timestamp)
                            else:
                                timestamps.append('')
                            
                            total = entry.get('total', {})
                            response_times.append(total.get('avg_response_time', 0))
                            rps_values.append(total.get('current_rps', 0))
                            tps_values.append(total.get('current_tps', total.get('current_rps', 0)))
                            user_counts.append(total.get('current_users', 0))
                        
                        # 写入数据用于图表
                        charts_sheet['A3'] = "时间点"
                        charts_sheet['B3'] = "响应时间(ms)"
                        charts_sheet['C3'] = "RPS"
                        charts_sheet['D3'] = "TPS"
                        charts_sheet['E3'] = "用户数"
                        
                        for i, (ts, rt, rps, tps, users) in enumerate(zip(timestamps, response_times, rps_values, tps_values, user_counts)):
                            row = i + 4
                            charts_sheet.cell(row=row, column=1).value = ts
                            charts_sheet.cell(row=row, column=2).value = rt
                            charts_sheet.cell(row=row, column=3).value = rps
                            charts_sheet.cell(row=row, column=4).value = tps
                            charts_sheet.cell(row=row, column=5).value = users
                        
                        # 创建响应时间图表
                        response_time_chart = LineChart()
                        response_time_chart.title = "平均响应时间"
                        response_time_chart.style = 2
                        response_time_chart.x_axis.title = "时间"
                        response_time_chart.y_axis.title = "响应时间(ms)"
                        
                        data = Reference(charts_sheet, min_col=2, min_row=3, max_row=len(timestamps) + 3, max_col=2)
                        cats = Reference(charts_sheet, min_col=1, min_row=4, max_row=len(timestamps) + 3)
                        
                        response_time_chart.add_data(data, titles_from_data=True)
                        response_time_chart.set_categories(cats)
                        
                        # 添加图表到工作表
                        charts_sheet.add_chart(response_time_chart, "A20")
                        
                        # 创建RPS图表
                        rps_chart = LineChart()
                        rps_chart.title = "每秒请求数(RPS)"
                        rps_chart.style = 2
                        rps_chart.x_axis.title = "时间"
                        rps_chart.y_axis.title = "RPS"
                        
                        data = Reference(charts_sheet, min_col=3, min_row=3, max_row=len(timestamps) + 3, max_col=3)
                        
                        rps_chart.add_data(data, titles_from_data=True)
                        rps_chart.set_categories(cats)
                        
                        # 添加图表到工作表
                        charts_sheet.add_chart(rps_chart, "J20")
                        
                        # 创建用户数图表
                        users_chart = LineChart()
                        users_chart.title = "并发用户数"
                        users_chart.style = 2
                        users_chart.x_axis.title = "时间"
                        users_chart.y_axis.title = "用户数"
                        
                        data = Reference(charts_sheet, min_col=5, min_row=3, max_row=len(timestamps) + 3, max_col=5)
                        
                        users_chart.add_data(data, titles_from_data=True)
                        users_chart.set_categories(cats)
                        
                        # 添加图表到工作表
                        charts_sheet.add_chart(users_chart, "A40")
                
                except Exception as e:
                    logger.error(f"处理报告数据失败: {str(e)}")
            
            # 调整列宽
            for sheet in wb.worksheets:
                for col in range(1, 10):
                    sheet.column_dimensions[chr(64 + col)].width = 20
            
            # 保存到内存
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)
            
            # 设置响应
            filename = f"性能报告_{report.reportName}_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
            
            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            return response
            
        except Exception as e:
            logger.error(f"导出报告失败: {str(e)}")
            raise


class PerformanceDataExporter:
    """性能数据导出器"""
    
    def __init__(self):
        self.supported_formats = ['json', 'csv', 'excel', 'zip']
    
    def export_task_data(self, task_ids: List[int], export_format: str = 'json', 
                        include_reports: bool = True, include_config: bool = True) -> Union[Dict, HttpResponse]:
        """导出任务数据"""
        try:
            if export_format not in self.supported_formats:
                raise ValueError(f"不支持的导出格式: {export_format}")
            
            # 获取任务数据
            export_data = self._collect_task_data(task_ids, include_reports, include_config)
            
            if export_format == 'json':
                return self._export_json(export_data)
            elif export_format == 'csv':
                return self._export_csv(export_data)
            elif export_format == 'excel':
                return self._export_excel(export_data)
            elif export_format == 'zip':
                return self._export_zip(export_data)
            
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            raise
    
    def _collect_task_data(self, task_ids: List[int], include_reports: bool, include_config: bool) -> Dict:
        """收集任务数据"""
        try:
            export_data = {
                'export_info': {
                    'export_time': datetime.now().isoformat(),
                    'total_tasks': len(task_ids),
                    'include_reports': include_reports,
                    'include_config': include_config
                },
                'tasks': [],
                'reports': [],
                'scenes': [],
                'steps': []
            }
            
            # 导出任务数据
            tasks = PerformanceTask.objects.filter(id__in=task_ids)
            for task in tasks:
                task_data = {
                    'id': task.id,
                    'taskName': task.taskName,
                    'taskType': task.taskType,
                    'desc': task.desc,
                    'runPattern': task.runPattern,
                    'status': task.status,
                    'distributed_mode': task.distributed_mode,
                    'total_workers': task.total_workers,
                    'worker_distribution': task.worker_distribution,
                    'create_time': task.create_time.isoformat() if task.create_time else None,
                    'project_id': task.project_id
                }
                
                # 添加服务器信息
                if task.master_server:
                    task_data['master_server'] = {
                        'id': task.master_server.id,
                        'name': task.master_server.name,
                        'host_ip': task.master_server.host_ip,
                        'server_type': task.master_server.server_type
                    }
                
                task_data['worker_servers'] = [
                    {
                        'id': server.id,
                        'name': server.name,
                        'host_ip': server.host_ip,
                        'server_type': server.server_type
                    }
                    for server in task.worker_servers.all()
                ]
                
                export_data['tasks'].append(task_data)
                
                # 导出场景数据
                if include_config:
                    scenes = TaskScence.objects.filter(task=task)
                    for scene in scenes:
                        scene_data = {
                            'id': scene.id,
                            'task_id': task.id,
                            'name': scene.name,
                            'weight': scene.weight,
                            'create_time': scene.create_time.isoformat() if scene.create_time else None
                        }
                        export_data['scenes'].append(scene_data)
                        
                        # 导出步骤数据
                        steps = TaskStep.objects.filter(scence=scene)
                        for step in steps:
                            step_data = {
                                'id': step.id,
                                'scene_id': scene.id,
                                'name': step.name,
                                'type': step.type,
                                'protocol': step.protocol,
                                'content': step.content,
                                'timeout': step.timeout,
                                'retry_count': step.retry_count,
                                'custom_payload': step.custom_payload,
                                'ssl_verify': step.ssl_verify,
                                'status': step.status,
                                'create_time': step.create_time.isoformat() if step.create_time else None
                            }
                            export_data['steps'].append(step_data)
            
            # 导出报告数据
            if include_reports:
                reports = TaskReport.objects.filter(task_id__in=task_ids)
                for report in reports:
                    report_data = {
                        'id': report.id,
                        'task_id': report.task_id,
                        'reportName': report.reportName,
                        'reportStatus': report.reportStatus,
                        'avgTps': report.avgTps,
                        'avgResponseTime': report.avgResponseTime,
                        'minResponseTime': report.minResponseTime,
                        'maxResponseTime': report.maxResponseTime,
                        'p50ResponseTime': report.p50ResponseTime,
                        'p95ResponseTime': report.p95ResponseTime,
                        'p99ResponseTime': report.p99ResponseTime,
                        'errorRate': report.errorRate,
                        'totalRequests': report.totalRequests,
                        'successRequests': report.successRequests,
                        'failedRequests': report.failedRequests,
                        'avgCpu': report.avgCpu,
                        'avgMemory': report.avgMemory,
                        'duration': report.duration,
                        'concurrentUsers': report.concurrentUsers if hasattr(report, 'concurrentUsers') else None,
                        'testEnvironment': report.testEnvironment if hasattr(report, 'testEnvironment') else None,
                        'testType': report.testType if hasattr(report, 'testType') else None,
                        'detailedErrors': report.detailedErrors if hasattr(report, 'detailedErrors') else None,
                        'performanceMetrics': report.performanceMetrics if hasattr(report, 'performanceMetrics') else None,
                        'systemMetrics': report.systemMetrics if hasattr(report, 'systemMetrics') else None,
                        'startTime': report.startTime.isoformat() if report.startTime else None,
                        'endTime': report.endTime.isoformat() if report.endTime else None,
                        'create_time': report.create_time.isoformat() if report.create_time else None,
                        'executor': report.executor,
                        'creator': report.creator
                    }
                    export_data['reports'].append(report_data)
            
            return export_data
            
        except Exception as e:
            logger.error(f"收集任务数据失败: {e}")
            raise
    
    def _export_json(self, data: Dict) -> Dict:
        """导出JSON格式"""
        return {
            'content': json.dumps(data, ensure_ascii=False, indent=2),
            'filename': f"performance_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            'content_type': 'application/json'
        }
    
    def _export_csv(self, data: Dict) -> HttpResponse:
        """导出CSV格式"""
        response = HttpResponse(content_type='text/csv; charset=utf-8')
        response['Content-Disposition'] = f'attachment; filename="performance_data_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv"'
        response.write('\ufeff')  # BOM for Excel
        
        writer = csv.writer(response)
        
        # 导出任务数据
        if data['tasks']:
            writer.writerow(['=== 任务数据 ==='])
            headers = list(data['tasks'][0].keys())
            writer.writerow(headers)
            
            for task in data['tasks']:
                row = []
                for header in headers:
                    value = task[header]
                    if isinstance(value, (dict, list)):
                        value = json.dumps(value, ensure_ascii=False)
                    row.append(value)
                writer.writerow(row)
            
            writer.writerow([])  # 空行分隔
        
        # 导出报告数据
        if data['reports']:
            writer.writerow(['=== 报告数据 ==='])
            headers = list(data['reports'][0].keys())
            writer.writerow(headers)
            
            for report in data['reports']:
                row = []
                for header in headers:
                    value = report[header]
                    if isinstance(value, (dict, list)):
                        value = json.dumps(value, ensure_ascii=False)
                    row.append(value)
                writer.writerow(row)
        
        return response
    
    def _export_excel(self, data: Dict) -> HttpResponse:
        """导出Excel格式"""
        output = io.BytesIO()
        
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            # 导出任务数据
            if data['tasks']:
                df_tasks = pd.DataFrame(data['tasks'])
                df_tasks.to_excel(writer, sheet_name='任务数据', index=False)
            
            # 导出报告数据
            if data['reports']:
                df_reports = pd.DataFrame(data['reports'])
                df_reports.to_excel(writer, sheet_name='报告数据', index=False)
            
            # 导出场景数据
            if data['scenes']:
                df_scenes = pd.DataFrame(data['scenes'])
                df_scenes.to_excel(writer, sheet_name='场景数据', index=False)
            
            # 导出步骤数据
            if data['steps']:
                df_steps = pd.DataFrame(data['steps'])
                df_steps.to_excel(writer, sheet_name='步骤数据', index=False)
        
        output.seek(0)
        
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="performance_data_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'
        
        return response
    
    def _export_zip(self, data: Dict) -> HttpResponse:
        """导出ZIP格式"""
        zip_buffer = io.BytesIO()
        
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # 添加JSON文件
            json_content = json.dumps(data, ensure_ascii=False, indent=2)
            zip_file.writestr('performance_data.json', json_content)
            
            # 添加各类数据的CSV文件
            if data['tasks']:
                df_tasks = pd.DataFrame(data['tasks'])
                csv_content = df_tasks.to_csv(index=False, encoding='utf-8-sig')
                zip_file.writestr('tasks.csv', csv_content)
            
            if data['reports']:
                df_reports = pd.DataFrame(data['reports'])
                csv_content = df_reports.to_csv(index=False, encoding='utf-8-sig')
                zip_file.writestr('reports.csv', csv_content)
            
            if data['scenes']:
                df_scenes = pd.DataFrame(data['scenes'])
                csv_content = df_scenes.to_csv(index=False, encoding='utf-8-sig')
                zip_file.writestr('scenes.csv', csv_content)
            
            if data['steps']:
                df_steps = pd.DataFrame(data['steps'])
                csv_content = df_steps.to_csv(index=False, encoding='utf-8-sig')
                zip_file.writestr('steps.csv', csv_content)
            
            # 添加导出信息文件
            export_info = f"""
导出信息
========
导出时间: {data['export_info']['export_time']}
任务数量: {data['export_info']['total_tasks']}
包含报告: {data['export_info']['include_reports']}
包含配置: {data['export_info']['include_config']}

文件说明:
- performance_data.json: 完整的JSON格式数据
- tasks.csv: 任务数据
- reports.csv: 报告数据
- scenes.csv: 场景数据
- steps.csv: 步骤数据
"""
            zip_file.writestr('README.txt', export_info)
        
        zip_buffer.seek(0)
        
        response = HttpResponse(zip_buffer.getvalue(), content_type='application/zip')
        response['Content-Disposition'] = f'attachment; filename="performance_data_{datetime.now().strftime("%Y%m%d_%H%M%S")}.zip"'
        
        return response


class PerformanceDataImporter:
    """性能数据导入器"""
    
    def __init__(self):
        self.supported_formats = ['json', 'csv', 'excel']
    
    def import_data(self, file_content: bytes, file_format: str, project_id: int, 
                   import_options: Dict = None) -> Dict:
        """导入数据"""
        try:
            if file_format not in self.supported_formats:
                raise ValueError(f"不支持的导入格式: {file_format}")
            
            import_options = import_options or {}
            
            # 解析文件内容
            if file_format == 'json':
                data = self._parse_json(file_content)
            elif file_format == 'csv':
                data = self._parse_csv(file_content)
            elif file_format == 'excel':
                data = self._parse_excel(file_content)
            
            # 验证数据
            validation_result = self._validate_data(data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': '数据验证失败',
                    'errors': validation_result['errors']
                }
            
            # 导入数据
            import_result = self._import_to_database(data, project_id, import_options)
            
            return import_result
            
        except Exception as e:
            logger.error(f"导入数据失败: {e}")
            return {
                'success': False,
                'message': f'导入失败: {str(e)}',
                'errors': [str(e)]
            }
    
    def _parse_json(self, file_content: bytes) -> Dict:
        """解析JSON文件"""
        try:
            content = file_content.decode('utf-8')
            return json.loads(content)
        except Exception as e:
            raise ValueError(f"JSON文件解析失败: {e}")
    
    def _parse_csv(self, file_content: bytes) -> Dict:
        """解析CSV文件"""
        try:
            content = file_content.decode('utf-8-sig')
            df = pd.read_csv(io.StringIO(content))
            
            # 将DataFrame转换为字典格式
            data = {
                'tasks': df.to_dict('records'),
                'reports': [],
                'scenes': [],
                'steps': []
            }
            
            return data
        except Exception as e:
            raise ValueError(f"CSV文件解析失败: {e}")
    
    def _parse_excel(self, file_content: bytes) -> Dict:
        """解析Excel文件"""
        try:
            excel_file = pd.ExcelFile(io.BytesIO(file_content))
            
            data = {
                'tasks': [],
                'reports': [],
                'scenes': [],
                'steps': []
            }
            
            # 读取各个工作表
            if '任务数据' in excel_file.sheet_names:
                df_tasks = pd.read_excel(excel_file, sheet_name='任务数据')
                data['tasks'] = df_tasks.to_dict('records')
            
            if '报告数据' in excel_file.sheet_names:
                df_reports = pd.read_excel(excel_file, sheet_name='报告数据')
                data['reports'] = df_reports.to_dict('records')
            
            if '场景数据' in excel_file.sheet_names:
                df_scenes = pd.read_excel(excel_file, sheet_name='场景数据')
                data['scenes'] = df_scenes.to_dict('records')
            
            if '步骤数据' in excel_file.sheet_names:
                df_steps = pd.read_excel(excel_file, sheet_name='步骤数据')
                data['steps'] = df_steps.to_dict('records')
            
            return data
        except Exception as e:
            raise ValueError(f"Excel文件解析失败: {e}")
    
    def _validate_data(self, data: Dict) -> Dict:
        """验证数据"""
        errors = []
        
        # 验证数据结构
        required_keys = ['tasks']
        for key in required_keys:
            if key not in data:
                errors.append(f"缺少必要的数据项: {key}")
        
        # 验证任务数据
        if 'tasks' in data and data['tasks']:
            for i, task in enumerate(data['tasks']):
                if not isinstance(task, dict):
                    errors.append(f"任务数据第{i+1}项格式错误")
                    continue
                
                # 验证必要字段
                required_fields = ['taskName', 'taskType']
                for field in required_fields:
                    if field not in task or not task[field]:
                        errors.append(f"任务数据第{i+1}项缺少字段: {field}")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
    
    def _import_to_database(self, data: Dict, project_id: int, import_options: Dict) -> Dict:
        """导入数据到数据库"""
        try:
            # 验证项目存在
            try:
                project = Project.objects.get(id=project_id)
            except Project.DoesNotExist:
                return {
                    'success': False,
                    'message': '指定的项目不存在',
                    'errors': ['项目ID无效']
                }
            
            imported_counts = {
                'tasks': 0,
                'reports': 0,
                'scenes': 0,
                'steps': 0
            }
            
            # 使用事务确保数据一致性
            from django.db import transaction
            with transaction.atomic():
                # 导入任务
                task_id_mapping = {}
                for task_data in data.get('tasks', []):
                    try:
                        # 移除不需要的字段
                        task_data = task_data.copy()
                        old_id = task_data.pop('id', None)
                        task_data.pop('master_server', None)
                        task_data.pop('worker_servers', None)
                        
                        # 设置项目
                        task_data['project'] = project
                        
                        # 处理时间字段
                        if 'create_time' in task_data and task_data['create_time']:
                            try:
                                task_data['create_time'] = datetime.fromisoformat(task_data['create_time'])
                            except:
                                task_data.pop('create_time')
                        
                        # 检查是否覆盖现有任务
                        existing_task = None
                        if import_options.get('update_existing', False):
                            existing_task = PerformanceTask.objects.filter(
                                taskName=task_data['taskName'],
                                project=project
                            ).first()
                        
                        if existing_task:
                            # 更新现有任务
                            for key, value in task_data.items():
                                setattr(existing_task, key, value)
                            existing_task.save()
                            task = existing_task
                        else:
                            # 创建新任务
                            task = PerformanceTask.objects.create(**task_data)
                        
                        if old_id:
                            task_id_mapping[old_id] = task.id
                        
                        imported_counts['tasks'] += 1
                        
                    except Exception as e:
                        logger.error(f"导入任务失败: {e}")
                        if not import_options.get('skip_errors', False):
                            raise
                
                # 导入场景
                scene_id_mapping = {}
                for scene_data in data.get('scenes', []):
                    try:
                        scene_data = scene_data.copy()
                        old_id = scene_data.pop('id', None)
                        old_task_id = scene_data.pop('task_id', None)
                        
                        # 映射任务ID
                        if old_task_id and old_task_id in task_id_mapping:
                            task = PerformanceTask.objects.get(id=task_id_mapping[old_task_id])
                            scene_data['task'] = task
                            
                            # 处理时间字段
                            if 'create_time' in scene_data and scene_data['create_time']:
                                try:
                                    scene_data['create_time'] = datetime.fromisoformat(scene_data['create_time'])
                                except:
                                    scene_data.pop('create_time')
                            
                            scene = TaskScence.objects.create(**scene_data)
                            
                            if old_id:
                                scene_id_mapping[old_id] = scene.id
                            
                            imported_counts['scenes'] += 1
                        
                    except Exception as e:
                        logger.error(f"导入场景失败: {e}")
                        if not import_options.get('skip_errors', False):
                            raise
                
                # 导入步骤
                for step_data in data.get('steps', []):
                    try:
                        step_data = step_data.copy()
                        step_data.pop('id', None)
                        old_scene_id = step_data.pop('scene_id', None)
                        
                        # 映射场景ID
                        if old_scene_id and old_scene_id in scene_id_mapping:
                            scene = TaskScence.objects.get(id=scene_id_mapping[old_scene_id])
                            step_data['scence'] = scene
                            
                            # 处理时间字段
                            if 'create_time' in step_data and step_data['create_time']:
                                try:
                                    step_data['create_time'] = datetime.fromisoformat(step_data['create_time'])
                                except:
                                    step_data.pop('create_time')
                            
                            TaskStep.objects.create(**step_data)
                            imported_counts['steps'] += 1
                        
                    except Exception as e:
                        logger.error(f"导入步骤失败: {e}")
                        if not import_options.get('skip_errors', False):
                            raise
                
                # 导入报告（如果需要）
                if import_options.get('import_reports', False):
                    for report_data in data.get('reports', []):
                        try:
                            report_data = report_data.copy()
                            report_data.pop('id', None)
                            old_task_id = report_data.pop('task_id', None)
                            
                            # 映射任务ID
                            if old_task_id and old_task_id in task_id_mapping:
                                task = PerformanceTask.objects.get(id=task_id_mapping[old_task_id])
                                report_data['task'] = task
                                
                                # 处理时间字段
                                time_fields = ['startTime', 'endTime', 'create_time']
                                for field in time_fields:
                                    if field in report_data and report_data[field]:
                                        try:
                                            report_data[field] = datetime.fromisoformat(report_data[field])
                                        except:
                                            report_data.pop(field)
                                
                                TaskReport.objects.create(**report_data)
                                imported_counts['reports'] += 1
                            
                        except Exception as e:
                            logger.error(f"导入报告失败: {e}")
                            if not import_options.get('skip_errors', False):
                                raise
            
            return {
                'success': True,
                'message': '数据导入成功',
                'imported_counts': imported_counts
            }
            
        except Exception as e:
            logger.error(f"导入数据到数据库失败: {e}")
            return {
                'success': False,
                'message': f'导入失败: {str(e)}',
                'errors': [str(e)]
            }


# 全局实例
data_exporter = PerformanceDataExporter()
data_importer = PerformanceDataImporter()
report_exporter = ReportExporter()


def get_data_exporter() -> PerformanceDataExporter:
    """获取数据导出器"""
    return data_exporter


def get_data_importer() -> PerformanceDataImporter:
    """获取数据导入器"""
    return data_importer 


def get_report_exporter() -> ReportExporter:
    """获取报告导出器"""
    return report_exporter 