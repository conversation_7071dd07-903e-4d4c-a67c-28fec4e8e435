from django.db import models
import uuid
from utils.models import BaseModel


class server(BaseModel):
    """
    性能测试服务器表
    """
    choices_server_type = [('master', '主节点'), ('worker', '工作节点')]
    choices_server_status = [('active', '活跃'), ('inactive', '不活跃'), ('error', '错误')]

    name = models.CharField(max_length=50, help_text='服务器名称', verbose_name='服务器名称')
    host_ip = models.CharField(max_length=50, help_text='服务器ip', verbose_name='服务器ip')
    host_port = models.IntegerField(verbose_name='端口号', help_text='端口号', null=True, blank=True)
    sys_user_name = models.CharField(max_length=50, help_text='用户名', verbose_name='用户名')
    sys_user_passwd = models.CharField(max_length=128, help_text='密码', verbose_name='密码')
    default_code = models.BooleanField(verbose_name='是否设置为默认服务器', default=False)
    server_type = models.CharField(max_length=10, choices=choices_server_type, default='master', 
                                  help_text='服务器类型', verbose_name='服务器类型')
    server_status = models.CharField(max_length=10, choices=choices_server_status, default='inactive',
                                    help_text='服务器状态', verbose_name='服务器状态')
    locust_master_host = models.CharField(max_length=50, help_text='Locust主节点地址', verbose_name='Locust主节点地址', 
                                         blank=True, null=True)
    locust_master_port = models.IntegerField(verbose_name='Locust主节点端口', help_text='Locust主节点端口', 
                                           default=5557, blank=True, null=True)
    max_workers = models.IntegerField(verbose_name='最大工作进程数', help_text='最大工作进程数', default=4)
    cpu_cores = models.IntegerField(verbose_name='CPU核心数', help_text='CPU核心数', blank=True, null=True)
    memory_gb = models.FloatField(verbose_name='内存大小(GB)', help_text='内存大小(GB)', blank=True, null=True)
    project = models.ForeignKey('projects.Project', on_delete=models.CASCADE, help_text='项目id', verbose_name='项目id',
                                related_name='server')


    def __str__(self):
        return self.name

    class Meta:
        db_table = 'performance_server'
        verbose_name = "性能测试服务器表"
        verbose_name_plural = verbose_name



class PerformanceTask(BaseModel):
    """
    性能测试任务表
    """
    choices_taskType = [('10', '普通任务'),('20', '定时任务')]
    choices_patternMode = [('10', '并发模式'), ('20', '阶梯模式')]
    choices_status = [('1', '执行中'), ('0', '执行完成'), ('99', '执行失败')]
    choices_distributed_mode = [('single', '单机模式'), ('distributed', '分布式模式')]

    project = models.ForeignKey('projects.Project', on_delete=models.CASCADE, help_text='项目id', verbose_name='项目id',
                                related_name='perfTask')
    taskType = models.CharField(max_length=50, help_text='任务类型', verbose_name='任务类型', choices=choices_taskType)
    desc = models.CharField(max_length=200, help_text='任务描述', verbose_name='任务描述', blank=True, null=True)
    taskName = models.CharField(max_length=50, help_text='任务名称', verbose_name='任务名称')
    runPattern = models.CharField(max_length=50, help_text='运行模式', verbose_name='运行模式', choices=choices_patternMode, blank=True, null=True)
    status = models.CharField(max_length=50, help_text='运行状态', verbose_name='运行状态', choices=choices_status, blank=True, null=True)
    distributed_mode = models.CharField(max_length=20, choices=choices_distributed_mode, default='single',
                                       help_text='分布式模式', verbose_name='分布式模式')
    master_server = models.ForeignKey('server', on_delete=models.SET_NULL, null=True, blank=True,
                                     help_text='主服务器', verbose_name='主服务器', related_name='master_tasks')
    worker_servers = models.ManyToManyField('server', blank=True, help_text='工作服务器', verbose_name='工作服务器',
                                           related_name='worker_tasks')
    total_workers = models.IntegerField(verbose_name='总工作进程数', help_text='总工作进程数', default=1)
    worker_distribution = models.JSONField(verbose_name='工作进程分配', help_text='工作进程分配', default=dict, blank=True)


    def __str__(self):
        return self.taskName

    class Meta:
        db_table = 'performance_task'
        verbose_name = "性能任务表"
        verbose_name_plural = verbose_name


class presetting(BaseModel):
    """
    性能测试预置配置表
    """
    choices_taskType = [('10', '普通任务'),('20', '定时任务')]
    choices_logMode = [('0', '关闭'),('10', '开启-全部日志'),('20', '开启-仅成功日志'),('30', '开启-仅失败日志')]
    choices_pressureMode = [('10', '并发模式'),('20', '阶梯模式')]
    choices_timeUnit = [('s', '秒'),('m', '分'),('h', '时')]
    choices_control = [('10', '集合模式'),('20', '单独模式')]
    choices_resource = [('10', '默认'),('20', '自定义')]
    choices_timeType = [('10', '固定'), ('20', '随机')]

    project = models.ForeignKey('projects.Project', on_delete=models.CASCADE, help_text='项目id', verbose_name='项目id',
                                related_name='presetting')
    serverArray = models.ManyToManyField(server,help_text='服务器id', verbose_name='服务器id', blank=True)
    task = models.OneToOneField(PerformanceTask, on_delete=models.SET_NULL, null=True, help_text='性能任务id', verbose_name='性能任务id',
                                related_name='presetting')
    name = models.CharField(max_length=128, help_text='预置配置名称', verbose_name='预置配置名称')
    rule = models.CharField(help_text='定时执行规则', verbose_name='定时任务', max_length=80, default='* * * * *')
    taskType = models.CharField(max_length=50, help_text='任务类型', verbose_name='任务类型', choices=choices_taskType)
    logMode = models.CharField(max_length=50, help_text='日志模式', verbose_name='日志模式', choices=choices_logMode)
    pressureMode = models.CharField(max_length=50, help_text='压测模式', verbose_name='压测模式', choices=choices_pressureMode)
    timeUnit = models.CharField(max_length=50, help_text='时间单位', verbose_name='时间单位', choices=choices_timeUnit)
    control = models.CharField(max_length=50, help_text='控制模式', verbose_name='控制模式', choices=choices_control)
    resource =models.CharField(max_length=50, help_text='机器选择方式', verbose_name='机器选择方式', choices=choices_resource)
    pressureConfig = models.JSONField(help_text='压测配置', verbose_name='压测配置', default=dict)
    isSetting = models.BooleanField(help_text='区分任务配置和预配置', verbose_name='区分预配置和任务配置', default=False)
    thinkTime = models.JSONField(help_text='思考时间', verbose_name='思考时间', default=[], null=True, blank=True)
    thinkTimeType = models.CharField(max_length=50, help_text='思考时间类型', verbose_name='思考时间类型', choices=choices_timeType)
    def __str__(self):
        return self.name

    class Meta:
        db_table = 'performance_presetting'
        verbose_name = "性能预配置表"
        verbose_name_plural = verbose_name



class TaskScence(models.Model):
    """
    性能测试场景表
    """
    task = models.ForeignKey(PerformanceTask, on_delete=models.CASCADE, help_text='性能任务id', verbose_name='性能任务id',
                                related_name='taskScence')
    env = models.ManyToManyField('projects.TestEnv', help_text='环境id', verbose_name='环境id',
                               related_name='taskScence', null=True, blank=True)
    name = models.CharField(max_length=100, verbose_name='场景名称', help_text='场景名称')
    weight = models.IntegerField(verbose_name='权重', help_text='权重', default=1)
    create_time = models.DateTimeField(verbose_name='创建时间', help_text='创建时间', auto_now_add=True, null=True, blank=True)

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'performance_TaskScence'
        verbose_name = "性能场景表"
        verbose_name_plural = verbose_name


class TaskStep(BaseModel):
    """
    性能测试步骤表
    """
    choices_step_type = [
        ('api', 'HTTP接口'),
        ('tcp', 'TCP连接'),
        ('udp', 'UDP连接'),
        ('websocket', 'WebSocket'),
        ('wait', '等待'),
        ('script', '自定义脚本'),
        ('loop', '循环控制器'),
        ('if', '条件控制器')
    ]
    choices_protocol = [
        ('HTTP', 'HTTP'),
        ('HTTPS', 'HTTPS'),
        ('TCP', 'TCP'),
        ('UDP', 'UDP'),
        ('WebSocket', 'WebSocket')
    ]
    
    scence = models.ForeignKey(TaskScence, on_delete=models.CASCADE, help_text='性能场景id', verbose_name='性能场景id')
    name = models.CharField(max_length=50, help_text='步骤控制器名称', verbose_name='步骤控制器名称')
    dlg = models.BooleanField(default=False, help_text='循环控制器是否展开', verbose_name='循环控制器是否展开')
    inputDlg = models.BooleanField(default=False, help_text='自定义脚本是否展开', verbose_name='自定义脚本是否展开')
    type = models.CharField(max_length=50, help_text='步骤控制器类型', verbose_name='步骤控制器类型', choices=choices_step_type)
    protocol = models.CharField(max_length=20, help_text='协议类型', verbose_name='协议类型', 
                               choices=choices_protocol, default='HTTP')
    content = models.JSONField(help_text='步骤控制器内容', verbose_name='步骤控制器内容', default=dict, blank=True)
    script = models.TextField(help_text='步骤控制器脚本', verbose_name='步骤控制器脚本', default='', null=True, blank=True)
    desc = models.CharField('步骤控制器描述', max_length=200, help_text='步骤控制器描述', null=True, blank=True)
    status = models.BooleanField(default=True, help_text='是否启用', verbose_name='是否启用')
    debugResult = models.BooleanField(default=True, help_text='是否正常', verbose_name='是否正常')
    weight = models.IntegerField(null=True, blank=True, help_text='权重', verbose_name='权重')
    
    # 协议特定配置
    timeout = models.IntegerField(verbose_name='超时时间(秒)', help_text='超时时间(秒)', default=10)
    retry_count = models.IntegerField(verbose_name='重试次数', help_text='重试次数', default=0)
    custom_payload = models.TextField(verbose_name='自定义负载', help_text='自定义负载(TCP/UDP)', blank=True, null=True)
    ssl_verify = models.BooleanField(verbose_name='SSL验证', help_text='SSL验证', default=True)

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'performance_TaskStep'
        verbose_name = "性能步骤表"
        verbose_name_plural = verbose_name

class TaskScenceStep(BaseModel):
    """
    性能测试场景步骤表
    """
    step = models.ForeignKey(TaskStep, on_delete=models.CASCADE, help_text='性能步骤id', verbose_name='性能步骤id')
    task = models.ForeignKey(PerformanceTask, on_delete=models.CASCADE, help_text='性能任务id', verbose_name='性能任务id')
    scence = models.ForeignKey(TaskScence, on_delete=models.CASCADE, help_text='性能场景id', verbose_name='性能场景id')
    sort = models.IntegerField(help_text='执行顺序', verbose_name='执行顺序', blank=True)
    parent = models.ForeignKey('self', null=True, blank=True, related_name='children', on_delete=models.CASCADE)

    def __str__(self):
        return str(self.id)

    class Meta:
        db_table = 'performance_taskScenceStep'
        verbose_name = "性能任务场景步骤表"
        verbose_name_plural = verbose_name


class TaskReport(BaseModel):
    """
    性能测试报告表
    """
    choices_status = [('1', '报告-执行中'), ('0', '报告-已完成'), ('99', '报告-运行失败')]

    task = models.ForeignKey(PerformanceTask, on_delete=models.CASCADE, help_text='性能任务id', verbose_name='性能任务id')
    reportName = models.CharField(max_length=50, help_text='报告名称', verbose_name='报告名称')
    desc = models.CharField(max_length=200, help_text='报告描述', verbose_name='报告描述', blank=True, null=True)
    reportStatus = models.CharField(max_length=50, help_text='运行状态', verbose_name='运行状态', choices=choices_status, blank=True, null=True)
    
    # 基础性能指标
    avgTps = models.FloatField(help_text='平均TPS', verbose_name='平均TPS', blank=True, null=True)
    avgCpu = models.FloatField(help_text='平均CPU使用率', verbose_name='平均CPU使用率', blank=True, null=True)
    avgMemory = models.FloatField(help_text='平均内存使用率', verbose_name='平均内存使用率', blank=True, null=True)
    
    # 扩展性能指标
    maxTps = models.FloatField(help_text='最大TPS', verbose_name='最大TPS', blank=True, null=True)
    minTps = models.FloatField(help_text='最小TPS', verbose_name='最小TPS', blank=True, null=True)
    avgResponseTime = models.FloatField(help_text='平均响应时间(ms)', verbose_name='平均响应时间', blank=True, null=True)
    maxResponseTime = models.FloatField(help_text='最大响应时间(ms)', verbose_name='最大响应时间', blank=True, null=True)
    minResponseTime = models.FloatField(help_text='最小响应时间(ms)', verbose_name='最小响应时间', blank=True, null=True)
    p50ResponseTime = models.FloatField(help_text='50%响应时间(ms)', verbose_name='50%响应时间', blank=True, null=True)
    p90ResponseTime = models.FloatField(help_text='90%响应时间(ms)', verbose_name='90%响应时间', blank=True, null=True)
    p95ResponseTime = models.FloatField(help_text='95%响应时间(ms)', verbose_name='95%响应时间', blank=True, null=True)
    p99ResponseTime = models.FloatField(help_text='99%响应时间(ms)', verbose_name='99%响应时间', blank=True, null=True)
    
    # 请求统计
    totalRequests = models.IntegerField(help_text='总请求数', verbose_name='总请求数', blank=True, null=True)
    successRequests = models.IntegerField(help_text='成功请求数', verbose_name='成功请求数', blank=True, null=True)
    failedRequests = models.IntegerField(help_text='失败请求数', verbose_name='失败请求数', blank=True, null=True)
    errorRate = models.FloatField(help_text='错误率(%)', verbose_name='错误率', blank=True, null=True)
    failuresPerSecond = models.FloatField(help_text='每秒失败数', verbose_name='每秒失败数', blank=True, null=True)
    
    # 并发用户数
    maxUsers = models.IntegerField(help_text='最大用户数', verbose_name='最大用户数', blank=True, null=True)
    avgUsers = models.FloatField(help_text='平均用户数', verbose_name='平均用户数', blank=True, null=True)
    
    # 测试时间
    startTime = models.DateTimeField(help_text='测试开始时间', verbose_name='测试开始时间', blank=True, null=True)
    endTime = models.DateTimeField(help_text='测试结束时间', verbose_name='测试结束时间', blank=True, null=True)
    duration = models.IntegerField(help_text='测试持续时间(秒)', verbose_name='测试持续时间', blank=True, null=True)
    
    # 详细报告数据
    reportResult = models.TextField(help_text='详细报告数据(JSON)', verbose_name='详细报告数据', blank=True, null=True)
    resultAnalyse = models.TextField(help_text='结果分析', verbose_name='结果分析', blank=True, null=True)
    
    # 执行环境信息
    env = models.ForeignKey('projects.TestEnv', on_delete=models.SET_NULL, null=True, blank=True, 
                           help_text='测试环境', verbose_name='测试环境')
    executor = models.CharField(max_length=50, help_text='执行人', verbose_name='执行人', blank=True, null=True)

    def __str__(self):
        return self.reportName

    class Meta:
        db_table = 'performance_task_report'
        verbose_name = "性能测试报告表"
        verbose_name_plural = verbose_name
        ordering = ['-create_time']


class PerformanceTestLog(models.Model):
    """性能测试日志模型"""
    report = models.ForeignKey(TaskReport, on_delete=models.CASCADE, related_name='logs',
                              help_text='关联的测试报告', verbose_name='测试报告')
    timestamp = models.DateTimeField(help_text='日志时间', verbose_name='日志时间')
    level = models.CharField(max_length=20, help_text='日志级别', verbose_name='日志级别',
                           choices=[('info', '信息'), ('warning', '警告'), ('error', '错误'), ('debug', '调试')])
    message = models.TextField(help_text='日志内容', verbose_name='日志内容')
    source = models.CharField(max_length=100, help_text='日志来源', verbose_name='日志来源', blank=True, null=True)
    user_count = models.IntegerField(help_text='当前用户数', verbose_name='当前用户数', blank=True, null=True)
    request_name = models.CharField(max_length=200, help_text='请求名称', verbose_name='请求名称', blank=True, null=True)
    response_time = models.FloatField(help_text='响应时间(ms)', verbose_name='响应时间', blank=True, null=True)
    exception = models.TextField(help_text='异常信息', verbose_name='异常信息', blank=True, null=True)
    create_time = models.DateTimeField(auto_now_add=True, help_text='创建时间', verbose_name='创建时间')
    
    def __str__(self):
        return f"[{self.level}] {self.message[:50]}..."
    
    class Meta:
        db_table = 'performance_test_log'
        verbose_name = "性能测试日志"
        verbose_name_plural = verbose_name
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['report', '-timestamp']),
            models.Index(fields=['level', '-timestamp']),
        ]







class AlertRule(BaseModel):
    """告警规则表"""
    METRIC_TYPE_CHOICES = [
        ('tps', 'TPS'),
        ('response_time', '响应时间'),
        ('error_rate', '错误率'),
        ('cpu_usage', 'CPU使用率'),
        ('memory_usage', '内存使用率'),
        ('concurrent_users', '并发用户数'),
        ('avg_response_time', '平均响应时间'),
    ]
    
    CONDITION_CHOICES = [
        ('gt', '大于'),
        ('lt', '小于'),
        ('gte', '大于等于'),
        ('lte', '小于等于'),
        ('eq', '等于'),
    ]
    
    SEVERITY_CHOICES = [
        ('low', '低'),
        ('medium', '中'),
        ('high', '高'),
        ('critical', '严重'),
    ]
    
    name = models.CharField(max_length=128, help_text='规则名称', verbose_name='规则名称')
    description = models.TextField(blank=True, null=True, help_text='规则描述', verbose_name='规则描述')
    metric_type = models.CharField(max_length=50, choices=METRIC_TYPE_CHOICES, help_text='监控指标', verbose_name='监控指标')
    condition = models.CharField(max_length=10, choices=CONDITION_CHOICES, help_text='比较条件', verbose_name='比较条件')
    threshold = models.FloatField(help_text='阈值', verbose_name='阈值')
    duration_minutes = models.IntegerField(default=5, help_text='持续时间(分钟)', verbose_name='持续时间')
    severity = models.CharField(max_length=20, choices=SEVERITY_CHOICES, help_text='告警级别', verbose_name='告警级别')
    notification_types = models.TextField(blank=True, null=True, help_text='通知方式(JSON格式)', verbose_name='通知方式')
    notification_config = models.TextField(blank=True, null=True, help_text='通知配置(JSON格式)', verbose_name='通知配置', default="{}")
    is_enabled = models.BooleanField(default=True, help_text='是否启用', verbose_name='是否启用')
    task_ids = models.TextField(blank=True, null=True, help_text='应用任务ID(JSON格式)', verbose_name='应用任务ID')
    created_by = models.CharField(max_length=50, help_text='创建人', verbose_name='创建人')
    project = models.ForeignKey('projects.Project', on_delete=models.CASCADE, related_name='alert_rules', help_text='项目', verbose_name='项目')

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'performance_alert_rule'
        verbose_name = "告警规则表"
        verbose_name_plural = "告警规则表"
        ordering = ['-create_time']


class AlertEvent(BaseModel):
    """告警事件表"""
    STATUS_CHOICES = [
        ('active', '活跃'),
        ('resolved', '已解决'),
        ('acknowledged', '已确认'),
    ]
    
    SEVERITY_CHOICES = [
        ('low', '低'),
        ('medium', '中'),
        ('high', '高'),
        ('critical', '严重'),
    ]
    
    alert_level = models.CharField(max_length=20, choices=SEVERITY_CHOICES, help_text='告警级别', verbose_name='告警级别')
    metric_value = models.FloatField(help_text='当前指标值', verbose_name='当前指标值')
    threshold_value = models.FloatField(help_text='阈值', verbose_name='阈值')
    message = models.TextField(help_text='告警消息', verbose_name='告警消息')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', help_text='状态', verbose_name='状态')
    triggered_time = models.DateTimeField(auto_now_add=True, help_text='触发时间', verbose_name='触发时间')
    resolved_time = models.DateTimeField(blank=True, null=True, help_text='解决时间', verbose_name='解决时间')
    acknowledged_by = models.CharField(max_length=50, blank=True, null=True, help_text='确认人', verbose_name='确认人')
    acknowledged_time = models.DateTimeField(blank=True, null=True, help_text='确认时间', verbose_name='确认时间')
    notification_sent = models.BooleanField(default=False, help_text='是否已发送通知', verbose_name='是否已发送通知')
    notification_details = models.TextField(blank=True, null=True, help_text='通知详情(JSON格式)', verbose_name='通知详情')
    rule = models.ForeignKey(AlertRule, on_delete=models.CASCADE, related_name='events', help_text='告警规则', verbose_name='告警规则')
    task = models.ForeignKey(PerformanceTask, on_delete=models.CASCADE, related_name='alert_events', help_text='任务', verbose_name='任务')
    report = models.ForeignKey(TaskReport, on_delete=models.SET_NULL, null=True, blank=True, related_name='alert_events', help_text='相关报告', verbose_name='相关报告')

    def __str__(self):
        return f"{self.rule.name} - {self.status}"

    class Meta:
        db_table = 'performance_alert_event'
        verbose_name = "告警事件表"
        verbose_name_plural = "告警事件表"
        ordering = ['-triggered_time']


class PerformanceBaseline(BaseModel):
    """性能基准线数据库模型"""
    # 使用UUID作为主键
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False, help_text='基准线ID', verbose_name='基准线ID')
    name = models.CharField(max_length=128, help_text='基准线名称', verbose_name='基准线名称')
    description = models.TextField(blank=True, null=True, help_text='基准线描述', verbose_name='基准线描述')
    task = models.ForeignKey(PerformanceTask, on_delete=models.CASCADE, related_name='baselines', help_text='关联任务', verbose_name='关联任务')
    project = models.ForeignKey('projects.Project', on_delete=models.CASCADE, related_name='baselines', help_text='项目', verbose_name='项目')
    environment = models.CharField(max_length=50, help_text='环境', verbose_name='环境', default='default')
    created_by = models.CharField(max_length=50, help_text='创建人', verbose_name='创建人', default='system')
    is_active = models.BooleanField(default=True, help_text='是否活跃', verbose_name='是否活跃')
    
    # 性能指标
    response_time = models.FloatField(help_text='响应时间(ms)', verbose_name='响应时间', default=0)
    tps = models.FloatField(help_text='每秒事务数(TPS)', verbose_name='TPS', default=0)
    error_rate = models.FloatField(help_text='错误率(%)', verbose_name='错误率', default=0)
    cpu_usage = models.FloatField(help_text='CPU使用率(%)', verbose_name='CPU使用率', default=0)
    memory_usage = models.FloatField(help_text='内存使用率(%)', verbose_name='内存使用率', default=0)
    
    # 容差百分比
    response_time_tolerance = models.FloatField(help_text='响应时间容差百分比(%)', verbose_name='响应时间容差', default=15.0)
    tps_tolerance = models.FloatField(help_text='TPS容差百分比(%)', verbose_name='TPS容差', default=10.0)
    error_rate_tolerance = models.FloatField(help_text='错误率容差百分比(%)', verbose_name='错误率容差', default=50.0)
    cpu_usage_tolerance = models.FloatField(help_text='CPU使用率容差百分比(%)', verbose_name='CPU使用率容差', default=20.0)
    memory_usage_tolerance = models.FloatField(help_text='内存使用率容差百分比(%)', verbose_name='内存使用率容差', default=20.0)
    
    # 原始基准数据JSON存储
    baseline_data = models.JSONField(help_text='原始基准数据', verbose_name='原始基准数据', default=dict, blank=True)

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'performance_baseline'
        verbose_name = "性能基准线"
        verbose_name_plural = verbose_name
        ordering = ['-create_time']

    @property
    def task_name(self):
        """获取任务名称"""
        if self.task:
            return self.task.taskName
        return f"Task_{self.task_id}"

    @property
    def success_rate(self):
        """获取成功率"""
        return 100 - self.error_rate






