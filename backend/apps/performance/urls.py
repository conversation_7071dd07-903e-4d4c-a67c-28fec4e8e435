# -*- coding: utf-8 -*-
# @author: HRUN

from rest_framework.routers import DefaultRouter

from . import views

router = DefaultRouter()
router.register('server', views.ServerViewSet)
router.register('presetting', views.PresettingViewSet)
router.register('performanceTask', views.PerformanceTaskViewSet)
router.register('taskScence', views.TaskScenceViewSet)
router.register('taskScenceStep', views.ScenceSetpViewSet)
router.register('performanceScenceStep', views.TaskScenceStepViewSet)
router.register('taskReport', views.TaskReportViewSet)
router.register('systemResource', views.SystemResourceViewSet, basename='systemresource')
router.register('scenarioDebug', views.ScenarioDebugViewSet, basename='scenariodebug')
urlpatterns = router.urls
