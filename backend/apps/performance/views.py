import os
import subprocess
import json
from typing import List, Dict, Optional, Any, Tu<PERSON>
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
from django.db.models import OuterRef, Subquery
from django.db import models
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework import  status
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet

from common.pagination import TenPerPageNumberPagination
from performance.export import export_excel
from .models import server, presetting, PerformanceTask, TaskScence, TaskStep, TaskScenceStep, TaskReport, PerformanceBaseline
from .serializers import ServerSerializer, PresettingSerializer, PerformanceTaskSerializer, \
    TaskScenceSerializer, ScenceStepSerializer, TaskScenceStepSerializer, TaskReportSerializer, \
    TaskReportCreateSerializer, TaskReportListSerializer, TaskReportDetailSerializer
from projects.models import newInterface, TestEnv, Project
from projects.serializers import newInterfaceSerializer
from performanceengine import main

# 添加基准线管理器类和相关代码

# 基准线类型枚举
class BaselineType(Enum):
    """基准线类型"""
    TPS = "tps"
    RESPONSE_TIME = "response_time"
    ERROR_RATE = "error_rate"
    CPU_USAGE = "cpu_usage"
    MEMORY_USAGE = "memory_usage"


# 对比结果枚举
class ComparisonResult(Enum):
    """对比结果"""
    BETTER = "better"          # 优于基准线
    SIMILAR = "similar"        # 接近基准线
    WORSE = "worse"           # 劣于基准线
    NO_BASELINE = "no_baseline"  # 无基准线


@dataclass
class BaselineMetric:
    """基准线指标"""
    metric_type: BaselineType
    target_value: float
    tolerance_percent: float  # 容差百分比
    min_threshold: Optional[float] = None
    max_threshold: Optional[float] = None
    
    def __post_init__(self):
        """计算阈值"""
        if self.min_threshold is None or self.max_threshold is None:
            tolerance = self.target_value * (self.tolerance_percent / 100)
            
            if self.metric_type in [BaselineType.RESPONSE_TIME, BaselineType.ERROR_RATE, 
                                  BaselineType.CPU_USAGE, BaselineType.MEMORY_USAGE]:
                # 这些指标越小越好
                self.min_threshold = 0
                self.max_threshold = self.target_value + tolerance
            else:
                # TPS等指标越大越好
                self.min_threshold = self.target_value - tolerance
                self.max_threshold = float('inf')


@dataclass 
class BaselineData:
    """性能基准线数据类"""
    id: str
    name: str
    description: str
    task_id: int
    task_name: str
    environment: str
    baseline_metrics: Dict[str, BaselineMetric]
    created_by: str
    created_time: datetime
    updated_time: Optional[datetime] = None
    is_active: bool = True
    baseline_data: Dict = None  # 原始基准数据
    
    def __post_init__(self):
        if self.baseline_data is None:
            self.baseline_data = {}


@dataclass
class BaselineComparison:
    """基准线对比结果"""
    baseline_id: str
    baseline_name: str
    task_id: int
    report_id: int
    comparison_time: datetime
    overall_result: ComparisonResult
    metric_comparisons: Dict[str, Dict] = None
    performance_score: float = 0.0
    recommendations: List[str] = None
    
    def __post_init__(self):
        if self.metric_comparisons is None:
            self.metric_comparisons = {}
        if self.recommendations is None:
            self.recommendations = []


class PerformanceBaselineManager:
    """性能基准线管理器 - 使用数据库存储"""
    
    def __init__(self):
        # 不再使用内存存储基准线，使用数据库存储
        self.comparison_history: List[BaselineComparison] = []
    
    def create_baseline(self, name: str, description: str, task_id: int, 
                       environment: str, baseline_data: Dict, 
                       created_by: str = "system", project_id: int = None) -> str:
        """创建性能基准线 - 使用数据库存储"""
        try:
            # 提取性能指标
            # 获取任务和项目信息
            task = PerformanceTask.objects.get(id=task_id)
            project_id = project_id or task.project_id
            
            # 提取基准数据中的关键指标
            avg_response_time = baseline_data.get('avgResponseTime', 0)
            avg_tps = baseline_data.get('avgTps', 0)
            error_rate = baseline_data.get('errorRate', 0)
            avg_cpu = baseline_data.get('avgCpu', 0)
            avg_memory = baseline_data.get('avgMemory', 0)
            
            # 创建数据库模型
            baseline = PerformanceBaseline.objects.create(
                name=name,
                description=description,
                task_id=task_id,
                project_id=project_id,
                environment=environment,
                created_by=created_by,
                
                # 性能指标
                response_time=avg_response_time,
                tps=avg_tps,
                error_rate=error_rate,
                cpu_usage=avg_cpu,
                memory_usage=avg_memory,
                
                # 保存原始数据
                baseline_data=baseline_data
            )
            
            return str(baseline.id)
            
        except Exception as e:
            print(f"创建基准线失败: {str(e)}")
            raise
    
    def _extract_baseline_metrics(self, baseline_data: Dict) -> Dict[str, BaselineMetric]:
        """从基准数据中提取关键指标"""
        metrics = {}
        
        # 提取TPS指标
        if 'avgTps' in baseline_data:
            metrics['tps'] = BaselineMetric(
                metric_type=BaselineType.TPS,
                target_value=baseline_data['avgTps'],
                tolerance_percent=10.0  # 10%容差
            )
        
        # 提取响应时间指标
        if 'avgResponseTime' in baseline_data:
            metrics['response_time'] = BaselineMetric(
                metric_type=BaselineType.RESPONSE_TIME,
                target_value=baseline_data['avgResponseTime'],
                tolerance_percent=15.0  # 15%容差
            )
        
        # 提取错误率指标
        if 'errorRate' in baseline_data:
            metrics['error_rate'] = BaselineMetric(
                metric_type=BaselineType.ERROR_RATE,
                target_value=baseline_data['errorRate'],
                tolerance_percent=50.0  # 50%容差
            )
        
        # 提取CPU使用率指标
        if 'avgCpu' in baseline_data:
            metrics['cpu_usage'] = BaselineMetric(
                metric_type=BaselineType.CPU_USAGE,
                target_value=baseline_data['avgCpu'],
                tolerance_percent=20.0  # 20%容差
            )
        
        # 提取内存使用率指标
        if 'avgMemory' in baseline_data:
            metrics['memory_usage'] = BaselineMetric(
                metric_type=BaselineType.MEMORY_USAGE,
                target_value=baseline_data['avgMemory'],
                tolerance_percent=20.0  # 20%容差
            )
        
        return metrics
    
    def _get_task_name(self, task_id: int) -> str:
        """获取任务名称"""
        try:
            task = PerformanceTask.objects.get(id=task_id)
            return task.taskName
        except Exception:
            return f"Task_{task_id}"
    
    def update_baseline(self, baseline_id: str, **kwargs) -> bool:
        """更新基准线 - 使用数据库更新"""
        try:
            # 获取基准线对象
            try:
                baseline = PerformanceBaseline.objects.get(id=baseline_id)
            except PerformanceBaseline.DoesNotExist:
                return False
            
            # 更新允许的普通字段
            updatable_fields = ['name', 'description', 'environment', 'is_active']
            update_data = {}
            
            for field, value in kwargs.items():
                if field in updatable_fields:
                    update_data[field] = value
            
            # 更新性能指标
            if 'avg_response_time' in kwargs:
                update_data['response_time'] = kwargs['avg_response_time']
                # 同时更新原始数据
                if baseline.baseline_data and 'avgResponseTime' in baseline.baseline_data:
                    baseline_data = dict(baseline.baseline_data)
                    baseline_data['avgResponseTime'] = kwargs['avg_response_time']
                    update_data['baseline_data'] = baseline_data
            
            if 'avg_tps' in kwargs:
                update_data['tps'] = kwargs['avg_tps']
                # 同时更新原始数据
                if baseline.baseline_data and 'avgTps' in baseline.baseline_data:
                    baseline_data = dict(baseline.baseline_data)
                    baseline_data['avgTps'] = kwargs['avg_tps']
                    update_data['baseline_data'] = baseline_data
            
            if 'success_rate' in kwargs:
                # 成功率 = 100 - 错误率
                update_data['error_rate'] = 100 - kwargs['success_rate']
                # 同时更新原始数据
                if baseline.baseline_data and 'errorRate' in baseline.baseline_data:
                    baseline_data = dict(baseline.baseline_data)
                    baseline_data['errorRate'] = 100 - kwargs['success_rate']
                    update_data['baseline_data'] = baseline_data
            
            if 'avg_cpu' in kwargs:
                update_data['cpu_usage'] = kwargs['avg_cpu']
                # 同时更新原始数据
                if baseline.baseline_data and 'avgCpu' in baseline.baseline_data:
                    baseline_data = dict(baseline.baseline_data)
                    baseline_data['avgCpu'] = kwargs['avg_cpu']
                    update_data['baseline_data'] = baseline_data
            
            if 'avg_memory' in kwargs:
                update_data['memory_usage'] = kwargs['avg_memory']
                # 同时更新原始数据
                if baseline.baseline_data and 'avgMemory' in baseline.baseline_data:
                    baseline_data = dict(baseline.baseline_data)
                    baseline_data['avgMemory'] = kwargs['avg_memory']
                    update_data['baseline_data'] = baseline_data
            
            # 更新数据库
            PerformanceBaseline.objects.filter(id=baseline_id).update(**update_data)
            
            return True
            
        except Exception as e:
            print(f"更新基准线失败: {str(e)}")
            return False
    
    def delete_baseline(self, baseline_id: str) -> bool:
        """删除基准线 - 使用数据库删除"""
        try:
            result = PerformanceBaseline.objects.filter(id=baseline_id).delete()
            # 返回一个元组，第一个元素是删除的记录数
            return result[0] > 0
        except Exception as e:
            print(f"删除基准线失败: {str(e)}")
            return False
    
    def get_baseline(self, baseline_id: str) -> Optional[PerformanceBaseline]:
        """获取基准线 - 从数据库查询"""
        try:
            return PerformanceBaseline.objects.get(id=baseline_id)
        except PerformanceBaseline.DoesNotExist:
            return None
    
    def list_baselines(self, task_id: Optional[int] = None, 
                      environment: Optional[str] = None,
                      active_only: bool = True,
                      project_id: Optional[int] = None) -> List:
        """列出基准线 - 使用数据库查询"""
        query = PerformanceBaseline.objects.all()
        
        # 应用过滤条件
        if active_only:
            query = query.filter(is_active=True)
        
        if task_id:
            query = query.filter(task_id=task_id)
        
        if environment:
            query = query.filter(environment=environment)
            
        if project_id:
            query = query.filter(project_id=project_id)
        
        # 按创建时间降序排序
        baselines = list(query.order_by('-create_time'))
        return baselines
    
    def compare_with_baseline(self, baseline_id: str, report_data: Dict, 
                             report_id: int) -> BaselineComparison:
        """与基准线进行对比"""
        try:
            baseline = self.get_baseline(baseline_id)
            
            if not baseline:
                return BaselineComparison(
                    baseline_id=baseline_id,
                    baseline_name="Unknown",
                    task_id=0,
                    report_id=report_id,
                    comparison_time=datetime.now(),
                    overall_result=ComparisonResult.NO_BASELINE
                )
            
            metric_comparisons = {}
            score_sum = 0
            metric_count = 0
            
            # 创建指标对象
            metric_fields = {
                'tps': ('tps', 'tps_tolerance'),
                'response_time': ('response_time', 'response_time_tolerance'),
                'error_rate': ('error_rate', 'error_rate_tolerance'),
                'cpu_usage': ('cpu_usage', 'cpu_usage_tolerance'),
                'memory_usage': ('memory_usage', 'memory_usage_tolerance')
            }
            
            # 对比各项指标
            for metric_name, (field_name, tolerance_field) in metric_fields.items():
                target_value = getattr(baseline, field_name, None)
                tolerance_percent = getattr(baseline, tolerance_field, 10.0)
                
                if target_value is not None:
                    # 创建基准线指标对象
                    metric_type = BaselineType(metric_name)
                    baseline_metric = BaselineMetric(
                        metric_type=metric_type,
                        target_value=target_value,
                        tolerance_percent=tolerance_percent
                    )
                    
                    actual_value = self._get_actual_metric_value(report_data, metric_name)
                    
                    if actual_value is not None:
                        comparison_result = self._compare_metric(baseline_metric, actual_value)
                        metric_comparisons[metric_name] = comparison_result
                        
                        # 计算分数 (0-100)
                        score = self._calculate_metric_score(baseline_metric, actual_value)
                        score_sum += score
                        metric_count += 1
            
            # 计算总体性能得分
            performance_score = score_sum / metric_count if metric_count > 0 else 0
            
            # 确定总体结果
            overall_result = self._determine_overall_result(metric_comparisons, performance_score)
            
            # 生成建议
            recommendations = self._generate_recommendations(baseline, metric_comparisons)
            
            comparison = BaselineComparison(
                baseline_id=baseline_id,
                baseline_name=baseline.name,
                task_id=baseline.task_id,
                report_id=report_id,
                comparison_time=datetime.now(),
                overall_result=overall_result,
                metric_comparisons=metric_comparisons,
                performance_score=performance_score,
                recommendations=recommendations
            )
            
            self.comparison_history.append(comparison)
            
            return comparison
            
        except Exception as e:
            raise
    
    def _get_actual_metric_value(self, report_data: Dict, metric_name: str) -> Optional[float]:
        """从报告数据中获取实际指标值"""
        metric_mapping = {
            'tps': 'avgTps',
            'response_time': 'avgResponseTime',
            'error_rate': 'errorRate',
            'cpu_usage': 'avgCpu',
            'memory_usage': 'avgMemory'
        }
        
        field_name = metric_mapping.get(metric_name)
        if field_name and field_name in report_data:
            return float(report_data[field_name])
        
        return None
    
    def _compare_metric(self, baseline_metric: BaselineMetric, actual_value: float) -> Dict:
        """对比单个指标"""
        target = baseline_metric.target_value
        min_threshold = baseline_metric.min_threshold
        max_threshold = baseline_metric.max_threshold
        
        # 计算差异百分比
        diff_percent = ((actual_value - target) / target) * 100 if target > 0 else 0
        
        # 判断结果
        if baseline_metric.metric_type in [BaselineType.RESPONSE_TIME, BaselineType.ERROR_RATE,
                                         BaselineType.CPU_USAGE, BaselineType.MEMORY_USAGE]:
            # 这些指标越小越好
            if actual_value <= target:
                result = ComparisonResult.BETTER
            elif actual_value <= max_threshold:
                result = ComparisonResult.SIMILAR
            else:
                result = ComparisonResult.WORSE
        else:
            # TPS等指标越大越好
            if actual_value >= target:
                result = ComparisonResult.BETTER
            elif actual_value >= min_threshold:
                result = ComparisonResult.SIMILAR
            else:
                result = ComparisonResult.WORSE
        
        return {
            'result': result,
            'target_value': target,
            'actual_value': actual_value,
            'diff_percent': diff_percent,
            'min_threshold': min_threshold,
            'max_threshold': max_threshold,
            'metric_type': baseline_metric.metric_type.value
        }
    
    def _calculate_metric_score(self, baseline_metric: BaselineMetric, actual_value: float) -> float:
        """计算指标得分 (0-100)"""
        target = baseline_metric.target_value
        
        if target == 0:
            return 50.0  # 默认中等分数
        
        if baseline_metric.metric_type in [BaselineType.RESPONSE_TIME, BaselineType.ERROR_RATE,
                                         BaselineType.CPU_USAGE, BaselineType.MEMORY_USAGE]:
            # 这些指标越小越好
            if actual_value <= target:
                # 优于目标值，给高分
                improvement_ratio = (target - actual_value) / target
                return min(100, 80 + improvement_ratio * 20)
            else:
                # 劣于目标值，根据差距程度打分
                degradation_ratio = (actual_value - target) / target
                return max(0, 80 - degradation_ratio * 80)
        else:
            # TPS等指标越大越好
            if actual_value >= target:
                # 优于目标值，给高分
                improvement_ratio = (actual_value - target) / target
                return min(100, 80 + improvement_ratio * 20)
            else:
                # 劣于目标值，根据差距程度打分
                degradation_ratio = (target - actual_value) / target
                return max(0, 80 - degradation_ratio * 80)
    
    def _determine_overall_result(self, metric_comparisons: Dict, performance_score: float) -> ComparisonResult:
        """确定总体对比结果"""
        if not metric_comparisons:
            return ComparisonResult.NO_BASELINE
        
        # 统计各种结果的数量
        results = [comp['result'] for comp in metric_comparisons.values()]
        better_count = sum(1 for r in results if r == ComparisonResult.BETTER)
        similar_count = sum(1 for r in results if r == ComparisonResult.SIMILAR)
        worse_count = sum(1 for r in results if r == ComparisonResult.WORSE)
        
        total_count = len(results)
        
        # 根据比例和得分确定总体结果
        if performance_score >= 80:
            return ComparisonResult.BETTER
        elif performance_score >= 60:
            return ComparisonResult.SIMILAR
        else:
            return ComparisonResult.WORSE
    
    def _generate_recommendations(self, baseline, 
                                metric_comparisons: Dict) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        for metric_name, comparison in metric_comparisons.items():
            if comparison['result'] == ComparisonResult.WORSE:
                metric_type = comparison['metric_type']
                
                if metric_type == 'tps':
                    recommendations.append(f"TPS性能下降{abs(comparison['diff_percent']):.1f}%，建议检查系统负载和网络状况")
                elif metric_type == 'response_time':
                    recommendations.append(f"响应时间增加{comparison['diff_percent']:.1f}%，建议优化接口性能和数据库查询")
                elif metric_type == 'error_rate':
                    recommendations.append(f"错误率上升{comparison['diff_percent']:.1f}%，建议检查应用日志和错误处理")
                elif metric_type == 'cpu_usage':
                    recommendations.append(f"CPU使用率增加{comparison['diff_percent']:.1f}%，建议优化算法或增加计算资源")
                elif metric_type == 'memory_usage':
                    recommendations.append(f"内存使用率增加{comparison['diff_percent']:.1f}%，建议检查内存泄漏或增加内存容量")
        
        if not recommendations:
            recommendations.append("所有指标均达到或超过基准线要求，性能表现良好")
        
        return recommendations
    
    def get_baseline_statistics(self, task_id: Optional[int] = None, 
                              days: int = 30) -> Dict:
        """获取基准线统计信息"""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            
            # 过滤对比历史
            filtered_comparisons = [
                comp for comp in self.comparison_history
                if comp.comparison_time >= start_time
            ]
            
            if task_id:
                filtered_comparisons = [
                    comp for comp in filtered_comparisons
                    if comp.task_id == task_id
                ]
            
            # 统计总体情况
            total_comparisons = len(filtered_comparisons)
            
            if total_comparisons == 0:
                return {
                    'total_comparisons': 0,
                    'avg_performance_score': 0,
                    'result_distribution': {},
                    'trend_analysis': {}
                }
            
            # 结果分布
            result_counts = {}
            performance_scores = []
            
            for comp in filtered_comparisons:
                result = comp.overall_result.value
                result_counts[result] = result_counts.get(result, 0) + 1
                performance_scores.append(comp.performance_score)
            
            # 趋势分析
            recent_scores = performance_scores[-10:] if len(performance_scores) >= 10 else performance_scores
            avg_recent_score = sum(recent_scores) / len(recent_scores) if recent_scores else 0
            
            all_avg_score = sum(performance_scores) / len(performance_scores)
            
            trend = "stable"
            if avg_recent_score > all_avg_score + 5:
                trend = "improving"
            elif avg_recent_score < all_avg_score - 5:
                trend = "declining"
            
            return {
                'total_comparisons': total_comparisons,
                'avg_performance_score': round(all_avg_score, 2),
                'recent_avg_score': round(avg_recent_score, 2),
                'result_distribution': result_counts,
                'trend_analysis': {
                    'trend': trend,
                    'score_change': round(avg_recent_score - all_avg_score, 2)
                },
                'baselines_count': len([b for b in self.baselines.values() 
                                      if not task_id or b.task_id == task_id])
            }
            
        except Exception as e:
            return {}
    
    def auto_create_baseline_from_report(self, report_data: Dict, task_id: int,
                                       environment: str = "default") -> Optional[str]:
        """从测试报告自动创建基准线"""
        try:
            # 检查是否已存在基准线
            existing_baselines = self.list_baselines(task_id=task_id, environment=environment)
            
            if existing_baselines:
                return None
            
            # 验证报告数据是否适合作为基准线
            if not self._is_suitable_for_baseline(report_data):
                return None
            
            # 自动生成基准线名称
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            baseline_name = f"Auto_Baseline_{task_id}_{timestamp}"
            description = f"自动从测试报告生成的基准线，创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            baseline_id = self.create_baseline(
                name=baseline_name,
                description=description,
                task_id=task_id,
                environment=environment,
                baseline_data=report_data,
                created_by="auto_system"
            )
            
            return baseline_id
            
        except Exception as e:
            return None
    
    def _is_suitable_for_baseline(self, report_data: Dict) -> bool:
        """判断报告数据是否适合作为基准线"""
        # 检查关键指标是否存在
        required_fields = ['avgTps', 'avgResponseTime', 'errorRate']
        
        for field in required_fields:
            if field not in report_data or report_data[field] is None:
                return False
        
        # 检查指标合理性
        if (report_data['avgTps'] <= 0 or 
            report_data['avgResponseTime'] <= 0 or
            report_data['errorRate'] > 10):  # 错误率超过10%不适合做基准
            return False
        
        return True


# 全局基准线管理器实例
baseline_manager = PerformanceBaselineManager()


def get_baseline_manager():
    """获取基准线管理器实例"""
    return baseline_manager


class ServerViewSet(ModelViewSet):
    """服务器视图集"""
    queryset = server.objects.all()
    serializer_class = ServerSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['name', 'server_type', 'server_status']
    pagination_class = TenPerPageNumberPagination

    def get_queryset(self):
        queryset = super().get_queryset()
        # 获取参数
        project = self.request.query_params.get('project_id')
        # 过滤
        if project:
            queryset = queryset.filter(project_id=project)

        return queryset

    @action(methods=['post'], detail=False, url_path='check_port')
    def check_port_availability(self, request):
        """检查端口可用性"""
        try:
            import socket
            
            host_ip = request.data.get('host_ip', 'localhost')
            port = request.data.get('port', 8089)
            
            # 创建socket连接测试端口
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)  # 3秒超时
            
            result = sock.connect_ex((host_ip, int(port)))
            sock.close()
            
            # result为0表示端口被占用，非0表示端口可用
            is_available = result != 0
            
            return Response({
                'available': is_available,
                'host_ip': host_ip,
                'port': port,
                'message': f'端口{port}{"可用" if is_available else "被占用"}'
            })
            
        except Exception as e:
            return Response({
                'available': False,
                'error': str(e),
                'message': f'检测端口失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['post'], detail=True)
    def debug_decrypt(self, request, pk):
        """调试密码解密功能"""
        try:
            from common.encryption_and_decryption import decrypt_field, load_key
            
            server_obj = get_object_or_404(server, id=pk)
            
            # 获取加密的密码
            encrypted_password = server_obj.sys_user_passwd
            
            try:
                key = load_key()
                decrypted_password = decrypt_field(encrypted_password, key)
                
                return Response({
                    'encrypted_password_length': len(encrypted_password) if encrypted_password else 0,
                    'encrypted_password_exists': bool(encrypted_password),
                    'decrypted_password_length': len(decrypted_password) if decrypted_password else 0,
                    'decrypted_password_exists': bool(decrypted_password),
                    'decrypted_password_type': str(type(decrypted_password)),
                    'message': '密码解密成功',
                    'status': 'success'
                })
            except Exception as e:
                return Response({
                    'encrypted_password_length': len(encrypted_password) if encrypted_password else 0,
                    'encrypted_password_exists': bool(encrypted_password),
                    'decrypt_error': str(e),
                    'message': '密码解密失败',
                    'status': 'error'
                })
                
        except Exception as e:
            return Response({
                'error': str(e),
                'message': '调试解密失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['post'], detail=True)
    def test_connection(self, request, pk):
        """测试服务器连接"""
        ssh = None  # 先定义ssh变量，确保finally块可以访问
        try:
            import paramiko
            import socket
            from common.encryption_and_decryption import decrypt_field, load_key

            server_obj = get_object_or_404(server, id=pk)

            # 获取请求参数
            data = request.data
            hostname = data.get('host_ip', server_obj.host_ip)
            port = data.get('host_port', server_obj.host_port or 22)
            username = data.get('sys_user_name', server_obj.sys_user_name)

            # 处理密码
            if 'sys_user_passwd' in data:
                password = data.get('sys_user_passwd')
            else:
                try:
                    key = load_key()
                    password = decrypt_field(server_obj.sys_user_passwd, key)
                    if password:
                        password = password.strip()
                except Exception as e:
                    return Response({
                        'message': f'连接测试失败: 密码解密失败 - {str(e)}',
                        'status': 'error',
                        'error_code': 'DECRYPTION_FAILED'
                    }, status=status.HTTP_400_BAD_REQUEST)

            # 验证必要参数
            if not hostname:
                return Response({
                    'message': '连接测试失败: 主机地址不能为空',
                    'status': 'error',
                    'error_code': 'MISSING_HOSTNAME'
                }, status=status.HTTP_400_BAD_REQUEST)

            if not username:
                return Response({
                    'message': '连接测试失败: 用户名不能为空',
                    'status': 'error',
                    'error_code': 'MISSING_USERNAME'
                }, status=status.HTTP_400_BAD_REQUEST)

            if not password:
                return Response({
                    'message': '连接测试失败: 密码不能为空',
                    'status': 'error',
                    'error_code': 'MISSING_PASSWORD'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 网络连接测试
            sock = None
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((hostname, int(port)))
                if result != 0:
                    raise ConnectionError(f'无法连接到 {hostname}:{port}')
            except Exception as e:
                return Response({
                    'message': f'连接测试失败: 网络连接错误 - {str(e)}',
                    'status': 'error',
                    'error_code': 'NETWORK_ERROR'
                }, status=status.HTTP_400_BAD_REQUEST)
            finally:
                if sock:
                    sock.close()

            # SSH连接测试
            try:
                ssh = paramiko.SSHClient()
                ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

                # 配置日志
                import logging
                logger = logging.getLogger(__name__)
                logger.info("Testing SSH connection to %s:%s with user %s", hostname, port, username)

                # SSH连接参数
                ssh_kwargs = {
                    'hostname': hostname,
                    'port': int(port),
                    'username': username,
                    'password': password,
                    'timeout': 10,
                    'banner_timeout': 30,
                    'auth_timeout': 30,
                    'allow_agent': False,
                    'look_for_keys': False,
                    'disabled_algorithms': {'pubkeys': ['rsa-sha2-256', 'rsa-sha2-512']}
                }


                # 尝试密码认证
                try:
                    ssh.connect(**ssh_kwargs)
                    logger.info("Successfully connected using password authentication")
                except paramiko.AuthenticationException as auth_err:
                    # 尝试keyboard-interactive认证
                    logger.info("Password auth failed, trying keyboard-interactive: %s", str(auth_err))
                    try:
                        transport = paramiko.Transport((hostname, int(port)))
                        transport.connect(username=username)

                        def keyboard_interactive_handler(title, instructions, prompt_list):
                            responses = []
                            for prompt in prompt_list:
                                if 'password' in prompt[0].lower():
                                    responses.append(password)
                                else:
                                    responses.append('')
                            return responses

                        transport.auth_interactive(username, keyboard_interactive_handler)
                        ssh._transport = transport
                        logger.info("Successfully connected using keyboard-interactive")
                    except Exception as ki_err:
                        logger.error("Keyboard-interactive failed: %s", str(ki_err))
                        raise auth_err

                # 测试命令执行
                stdin, stdout, stderr = ssh.exec_command('echo "connection test"', timeout=10)
                result = stdout.read().decode().strip()
                error_output = stderr.read().decode().strip()

                if result != "connection test":
                    raise Exception(f'命令执行失败: {error_output or "未知错误"}')

                # 获取系统信息
                system_info = self._get_basic_system_info(ssh)

                # 更新服务器状态
                server_obj.server_status = 'active'
                server_obj.save()

                return Response({
                    'message': '连接测试成功',
                    'status': 'active',
                    'success': True,
                    'details': {
                        'hostname': hostname,
                        'port': port,
                        'username': username,
                        'connection_time': '< 1s',
                        'system_info': system_info
                    }
                })

            except paramiko.AuthenticationException:
                return Response({
                    'message': '连接测试失败: 身份验证失败，请检查用户名和密码',
                    'status': 'error',
                    'error_code': 'AUTHENTICATION_FAILED'
                }, status=status.HTTP_400_BAD_REQUEST)

            except paramiko.SSHException as e:
                error_msg = str(e).lower()
                if 'no existing session' in error_msg:
                    detailed_msg = 'SSH服务未启动或配置问题'
                elif 'unable to connect' in error_msg:
                    detailed_msg = '无法连接到SSH服务，请检查端口和防火墙'
                elif 'authentication' in error_msg:
                    detailed_msg = '认证方式不支持，请确认服务器允许密码认证'
                else:
                    detailed_msg = str(e)

                return Response({
                    'message': f'连接测试失败: {detailed_msg}',
                    'status': 'error',
                    'error_code': 'SSH_ERROR'
                }, status=status.HTTP_400_BAD_REQUEST)

            except socket.timeout:
                return Response({
                    'message': '连接测试失败: 连接超时',
                    'status': 'error',
                    'error_code': 'CONNECTION_TIMEOUT'
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error("Server connection test failed for %s: %s", pk, str(e))

            try:
                server_obj = get_object_or_404(server, id=pk)
                server_obj.server_status = 'error'
                server_obj.save()
            except:
                pass

            return Response({
                'message': f'连接测试失败: {str(e)}',
                'status': 'error',
                'error_code': 'UNKNOWN_ERROR'
            }, status=status.HTTP_400_BAD_REQUEST)

        finally:
            if ssh:
                ssh.close()
    
    def _get_basic_system_info(self, ssh):
        """获取基本系统信息"""
        try:
            info = {}
            
            # 获取操作系统信息
            stdin, stdout, stderr = ssh.exec_command('uname -a', timeout=5)
            info['os'] = stdout.read().decode().strip()
            
            # 获取CPU信息
            stdin, stdout, stderr = ssh.exec_command('nproc', timeout=5)
            info['cpu_cores'] = stdout.read().decode().strip()
            
            # 获取内存信息
            stdin, stdout, stderr = ssh.exec_command('free -h | grep Mem', timeout=5)
            memory_info = stdout.read().decode().strip()
            if memory_info:
                info['memory'] = memory_info.split()[1]  # 总内存
            
            # 获取磁盘信息
            stdin, stdout, stderr = ssh.exec_command('df -h / | tail -1', timeout=5)
            disk_info = stdout.read().decode().strip()
            if disk_info:
                parts = disk_info.split()
                if len(parts) >= 4:
                    info['disk_total'] = parts[1]
                    info['disk_used'] = parts[2]
                    info['disk_available'] = parts[3]
            
            return info
        except:
            return {'note': '无法获取详细系统信息'}

    @action(methods=['post'], detail=True)
    def get_system_info(self, request, pk):
        """获取服务器系统信息"""
        try:
            import paramiko
            from common.encryption_and_decryption import decrypt_field, load_key
            
            server_obj = get_object_or_404(server, id=pk)
            
            # 解密密码
            try:
                key = load_key()
                password = decrypt_field(server_obj.sys_user_passwd, key)
            except Exception as e:
                return Response({
                    'message': f'获取系统信息失败: 密码解密失败 - {str(e)}',
                    'status': 'error'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh.connect(
                hostname=server_obj.host_ip,
                port=server_obj.host_port or 22,
                username=server_obj.sys_user_name,
                password=password,
                timeout=10
            )
            
            # 获取CPU信息
            stdin, stdout, stderr = ssh.exec_command('nproc')
            cpu_cores = int(stdout.read().decode().strip())
            
            # 获取内存信息
            stdin, stdout, stderr = ssh.exec_command("free -g | grep Mem | awk '{print $2}'")
            memory_gb = float(stdout.read().decode().strip())
            
            # 获取系统信息
            stdin, stdout, stderr = ssh.exec_command('uname -a')
            system_info = stdout.read().decode().strip()
            
            # 检查Python和Locust
            stdin, stdout, stderr = ssh.exec_command('python3 --version')
            python_version = stdout.read().decode().strip()
            
            stdin, stdout, stderr = ssh.exec_command('locust --version')
            locust_version = stdout.read().decode().strip()
            
            ssh.close()
            
            # 更新服务器信息
            server_obj.cpu_cores = cpu_cores
            server_obj.memory_gb = memory_gb
            server_obj.save()
            
            return Response({
                'cpu_cores': cpu_cores,
                'memory_gb': memory_gb,
                'system_info': system_info,
                'python_version': python_version,
                'locust_version': locust_version
            })
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['get'], detail=True)
    def check_ports(self, request, pk):
        """检查服务器端口占用状态"""
        try:
            import paramiko
            from common.encryption_and_decryption import decrypt_field, load_key
            
            server_obj = get_object_or_404(server, id=pk)
            
            # 预定义端口列表
            ports_to_check = [8089, 8090, 8091, 8092, 8093, 8094, 8095, 8096]
            occupied_ports = []
            
            # 解密密码
            try:
                key = load_key()
                password = decrypt_field(server_obj.sys_user_passwd, key)
            except Exception as e:
                return Response({
                    'message': f'检查端口失败: 密码解密失败 - {str(e)}',
                    'occupied_ports': [],
                    'available_ports': ports_to_check
                }, status=status.HTTP_400_BAD_REQUEST)
            
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh.connect(
                hostname=server_obj.host_ip,
                port=server_obj.host_port or 22,
                username=server_obj.sys_user_name,
                password=password,
                timeout=10
            )
            
            for port in ports_to_check:
                # 检查端口是否被占用
                stdin, stdout, stderr = ssh.exec_command(f'netstat -tuln | grep :{port}')
                result = stdout.read().decode().strip()
                
                if result:  # 如果有输出说明端口被占用
                    occupied_ports.append(port)
            
            ssh.close()
            
            return Response({
                'occupied_ports': occupied_ports,
                'available_ports': [p for p in ports_to_check if p not in occupied_ports]
            })
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['get'], detail=False)
    def cluster_status(self, request):
        """获取集群状态"""
        try:
            project_id = request.query_params.get('project_id')
            queryset = self.get_queryset()
            
            if project_id:
                queryset = queryset.filter(project_id=project_id)
            
            cluster_stats = {
                'total_servers': queryset.count(),
                'active_servers': queryset.filter(server_status='active').count(),
                'master_servers': queryset.filter(server_type='master').count(),
                'worker_servers': queryset.filter(server_type='worker').count(),
                'total_cpu_cores': sum(s.cpu_cores or 0 for s in queryset),
                'total_memory_gb': sum(s.memory_gb or 0 for s in queryset),
                'servers': []
            }
            
            for srv in queryset:
                cluster_stats['servers'].append({
                    'id': srv.id,
                    'name': srv.name,
                    'host_ip': srv.host_ip,
                    'server_type': srv.server_type,
                    'server_status': srv.server_status,
                    'cpu_cores': srv.cpu_cores,
                    'memory_gb': srv.memory_gb
                })
            
            return Response(cluster_stats)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class PresettingViewSet(ModelViewSet):
    """预置配置视图集"""
    queryset = presetting.objects.all().order_by('-create_time')
    serializer_class = PresettingSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['name', 'task', 'isSetting', 'taskType']
    pagination_class = TenPerPageNumberPagination


    @action(methods=['post'], detail=False)
    def save_presetting(self, request, *args, **kwargs):
        global data, task
        if 'task' in request.data:
            data = request.data
            data['isSetting'] = True
            task = self.request.data.get('task')
        else:
            return Response({"message": "请确认传参完整后再重试"}, status=status.HTTP_400_BAD_REQUEST)

        is_presetting = self.queryset.filter(task_id=task).first()

        if is_presetting:
            serializer = self.get_serializer(is_presetting, data=data, partial=True)  # partial=True 允许部分更新
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data, status=status.HTTP_200_OK)
            return Response({"message": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)
        else:
            serializer = self.get_serializer(data=data)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data, status=status.HTTP_200_OK)
            return Response({"message": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)





    def get_queryset(self):
        queryset = super().get_queryset()
        # 获取参数
        project = self.request.query_params.get('project_id')
        # 过滤
        if project:
            queryset = queryset.filter(project_id=project)

        return queryset




class PerformanceTaskViewSet(ModelViewSet):
    """性能任务视图集"""
    queryset = PerformanceTask.objects.all().order_by('-create_time')
    serializer_class = PerformanceTaskSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['taskName']
    pagination_class = TenPerPageNumberPagination

    def get_queryset(self):
        """获取用户相关的任务"""
        user = self.request.user
        projectID = self.request.query_params.get("project_id", None)
        # 处理过滤条件
        if projectID and Project.objects.filter(id=projectID).exists():
            # 如果指定了项目ID且项目存在
            return PerformanceTask.objects.filter(project=projectID)
        # 默认返回所有任务
        return PerformanceTask.objects.all()
        
    def list(self, request, *args, **kwargs):
        """列出任务，支持不分页选项"""
        # 检查是否请求不分页数据
        no_page = request.query_params.get('no_page', 'false').lower() == 'true'
        
        # 获取查询集
        queryset = self.filter_queryset(self.get_queryset())
        
        if no_page:
            # 如果请求不分页，则直接序列化所有数据
            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        else:
            # 否则使用默认的分页
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)
            
            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)

    def perform_update(self, serializer):
        """更新时设置修改人"""
        user = self.request.user
        serializer.save(modifier=user.username if hasattr(user, 'username') else str(user))

    def perform_create(self, serializer):
        """创建时设置创建人"""
        user = self.request.user
        serializer.save(creator=user.username if hasattr(user, 'username') else str(user))

    @action(methods=['post'], detail=True)
    def run(self, request, pk):
        task = get_object_or_404(PerformanceTask, id=pk)

        # 检查任务下是否存在场景
        if not task.taskScence.exists():
            return Response({'message': '该任务下无性能测试场景，请添加后再执行'}, status=status.HTTP_400_BAD_REQUEST)

        # 遍历每个场景，检查每个场景下是否有步骤
        for scene in task.taskScence.all():
            if not TaskStep.objects.filter(scence=scene).exists():
                return Response({'message': f'场景【{scene.name}】下没有步骤，请添加步骤后再执行'},
                                status=status.HTTP_400_BAD_REQUEST)

        env_id = request.data.get('env')
        
        # 检查是否为分布式模式
        if task.distributed_mode == 'distributed':
            return self._run_distributed_test(task, env_id, request.data)
        else:
            return self._run_single_test(task, env_id)

    @action(methods=['post'], detail=True, url_path='run_optimized')
    def run_optimized(self, request, pk):
        """优化版性能测试运行"""
        task = get_object_or_404(PerformanceTask, id=pk)

        # 检查任务下是否存在场景
        if not task.taskScence.exists():
            return Response({'message': '该任务下无性能测试场景，请添加后再执行'},
                          status=status.HTTP_400_BAD_REQUEST)

        # 遍历每个场景，检查每个场景下是否有步骤
        for scene in task.taskScence.all():
            if not TaskStep.objects.filter(scence=scene).exists():
                return Response({'message': f'场景【{scene.name}】下没有步骤，请添加步骤后再执行'},
                              status=status.HTTP_400_BAD_REQUEST)

        # 获取配置参数
        test_config = request.data
        env_id = test_config.get('env_id')
        mode = test_config.get('mode', 'single')

        # 更新任务配置
        if mode == 'distributed':
            master_server_id = test_config.get('master_server')
            worker_server_ids = test_config.get('worker_servers', [])

            if master_server_id:
                task.master_server_id = master_server_id
            if worker_server_ids:
                task.worker_servers.set(worker_server_ids)

            task.distributed_mode = 'distributed'
        else:
            task.distributed_mode = 'single'

        task.save()

        # 创建测试报告
        report = TaskReport.objects.create(
            task=task,
            reportName=f"{task.taskName}_执行报告_{timezone.now().strftime('%Y%m%d_%H%M%S')}",
            reportStatus='1',  # 运行中
            creator=request.user.username if hasattr(request.user, 'username') else str(request.user)
        )

        try:
            if mode == 'distributed':
                return self._run_distributed_test_optimized(task, env_id, test_config, report.id)
            else:
                return self._run_single_test_optimized(task, env_id, test_config, report.id)
        except Exception as e:
            # 如果启动失败，更新报告状态
            report.reportStatus = '99'  # 失败
            report.save()
            raise e

    def _run_single_test_optimized(self, task, env_id, test_config, report_id):
        """运行优化版单机测试（异步启动）"""
        try:
            # 导入异步任务
            from performanceengine.main import start_performance_test_async, get_host_ip

            # 获取Web端口配置
            web_port = test_config.get('web_port', 8089)

            # 获取本机IP
            host_ip = get_host_ip()

            # 异步启动性能测试
            async_task = start_performance_test_async.delay(
                task.id,
                env_id,
                {'web_port': web_port},
                self.request.user.username if hasattr(self.request.user, 'username') else 'system'
            )

            return Response({
                'message': '单机测试已开始异步启动',
                'task_id': async_task.id,
                'report_id': report_id,
                'gui_url': f'http://{host_ip}:{web_port}',
                'web_port': web_port,
                'status': 'starting'
            })

        except Exception as e:
            return Response({'message': '单机测试启动失败', 'error': str(e)},
                          status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _run_distributed_test_optimized(self, task, env_id, test_config, report_id):
        """运行优化版分布式测试"""
        try:
            from performanceengine.distributedManager import get_distributed_manager
            from performanceengine.main import get_host_ip
            
            # 检查分布式配置
            if not task.master_server:
                return Response({'message': '未配置主服务器'}, status=status.HTTP_400_BAD_REQUEST)
            
            if not task.worker_servers.exists():
                return Response({'message': '未配置工作服务器'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 获取主服务器信息
            master_server = task.master_server
            web_port = test_config.get('web_port', 8089)
            
            # 获取本机IP，用于本地访问
            local_ip = get_host_ip()
            
            # 启动分布式测试
            manager = get_distributed_manager()
            result = manager.start_distributed_test(
                task_id=task.id,
                master_server=master_server,
                worker_servers=list(task.worker_servers.all()),
                env_id=env_id,
                test_config=test_config
            )
            
            if result.get('success'):
                data = result.get('data', {})
                gui_url = data.get('gui_url')
                local_gui_url = data.get('local_gui_url')
                
                return Response({
                    'message': '分布式测试启动成功',
                    'data': data,
                    'report_id': report_id,
                    'gui_url': gui_url,
                    'local_gui_url': local_gui_url,
                    'web_port': web_port,
                    'master_server': {
                        'id': master_server.id,
                        'name': master_server.name,
                        'host_ip': master_server.host_ip
                    }
                })
            else:
                return Response({
                    'message': f'分布式测试启动失败: {result.get("error")}',
                    'error': result.get("error")
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
        except Exception as e:
            return Response({'message': '分布式测试执行失败', 'error': str(e)},
                          status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _run_single_test(self, task, env_id):
        """运行单机测试"""
        try:
            from performanceengine.main import get_host_ip
            
            # 获取本机IP
            host_ip = get_host_ip()
            
            # 定义可用端口列表（10个端口）
            available_ports = [8089, 8090, 8091, 8092, 8093, 8094, 8095, 8096, 8097, 8098]
            selected_port = None
            
            # 使用已有的端口检查功能
            import socket
            for port in available_ports:
                try:
                    # 创建socket连接测试端口
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(1)  # 1秒超时，加快检测速度
                    
                    result = sock.connect_ex(('localhost', port))
                    sock.close()
                    
                    # result非0表示端口可用
                    if result != 0:
                        selected_port = port
                        break
                except Exception as e:
                    print(f"检查端口{port}可用性失败: {e}")
                    continue
            
            # 如果所有端口都被占用，可以选择使用第一个端口并强制释放
            if selected_port is None:
                selected_port = available_ports[0]
                main.kill_process_using_port(selected_port)

            data = main.run_task(task.id, env_id,self.request.user.username if hasattr(self.request.user, 'username') else 'system',selected_port)

            # if os.environ.get('ENV') == 'production':
            #     # 异步执行
            #     async_result = main.run_task.delay(task.id, env_id, self.request.user.username if hasattr(self.request.user, 'username') else 'system', selected_port)
            #     # 返回任务ID而不是AsyncResult对象
            #     data = {'task_id': async_result.id, 'status': 'started', 'port': selected_port}
            # else:
            #     data = main.run_task(task.id, env_id, self.request.user.username if hasattr(self.request.user, 'username') else 'system', selected_port)

            # 使用完整参数启动Locust，使用选定的端口
            subprocess.Popen([
                'python', 'performanceengine/taskGenerate.py',
                '--web-host', '0.0.0.0',  # 绑定所有网络接口
                '--web-port', str(selected_port)
            ])
            
            return Response({
                'message': '单机测试执行成功', 
                'data': data,
                'gui_url': f'http://{host_ip}:{selected_port}'
            })
            
        except Exception as e:
            return Response({'message': '单机测试执行失败', 'error': str(e)},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _run_distributed_test(self, task, env_id, test_params):
        """运行分布式测试"""
        try:
            from performanceengine.distributedManager import get_distributed_manager
            
            # 检查分布式配置
            if not task.master_server:
                return Response({'message': '未配置主服务器'}, status=status.HTTP_400_BAD_REQUEST)
            
            if not task.worker_servers.exists():
                return Response({'message': '未配置工作服务器'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 获取分布式管理器
            manager = get_distributed_manager(task.id)
            
            # 设置分布式测试环境
            if not manager.setup_distributed_test():
                return Response({'message': '分布式测试环境设置失败'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            # 启动分布式测试
            users = test_params.get('users', 10)
            spawn_rate = test_params.get('spawn_rate', 1)
            duration = test_params.get('duration')
            
            if manager.start_distributed_test(users, spawn_rate, duration):
                # 更新任务状态
                task.status = '1'  # 执行中
                task.save()
                
                return Response({
                    'message': '分布式测试启动成功',
                    'mode': 'distributed',
                    'master_server': task.master_server.host_ip,
                    'worker_count': task.worker_servers.count(),
                    'users': users,
                    'spawn_rate': spawn_rate
                })
            else:
                return Response({'message': '分布式测试启动失败'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
        except Exception as e:
            return Response({'message': '分布式测试执行失败', 'error': str(e)},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['post'], detail=True)
    def stop_distributed_test(self, request, pk):
        """停止分布式测试"""
        try:
            from performanceengine.distributedManager import get_distributed_manager
            
            task = get_object_or_404(PerformanceTask, id=pk)
            
            if task.distributed_mode != 'distributed':
                return Response({'message': '该任务不是分布式模式'}, status=status.HTTP_400_BAD_REQUEST)
            
            manager = get_distributed_manager(task.id)
            
            if manager.stop_distributed_test():
                task.status = '0'  # 执行完成
                task.save()
                
                return Response({'message': '分布式测试停止成功'})
            else:
                return Response({'message': '分布式测试停止失败'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
        except Exception as e:
            return Response({'message': '停止分布式测试失败', 'error': str(e)},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['get'], detail=True)
    def distributed_status(self, request, pk):
        """获取分布式测试状态"""
        try:
            from performanceengine.distributedManager import get_distributed_manager
            
            task = get_object_or_404(PerformanceTask, id=pk)
            
            if task.distributed_mode != 'distributed':
                return Response({'message': '该任务不是分布式模式'}, status=status.HTTP_400_BAD_REQUEST)
            
            manager = get_distributed_manager(task.id)
            status_data = manager.get_test_status()
            
            return Response(status_data)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['post'], detail=True)
    def stop(self, request, pk):
        """停止性能测试"""
        try:
            task = get_object_or_404(PerformanceTask, id=pk)
            
            if task.distributed_mode == 'distributed':
                # 调用分布式停止方法
                return self.stop_distributed_test(request, pk)
            else:
                # 单机模式停止
                try:
                    # 获取最近的报告，查看使用的端口
                    from performance.models import TaskReport
                    running_reports = TaskReport.objects.filter(
                        task=task, 
                        reportStatus='1'  # 运行中
                    ).order_by('-create_time')
                    
                    # 尝试获取报告数据中的端口信息
                    port_to_kill = 8089  # 默认端口
                    if running_reports.exists():
                        report = running_reports.first()
                        # 尝试从报告结果中解析端口信息
                        try:
                            if report.reportResult and isinstance(report.reportResult, dict) and 'web_port' in report.reportResult:
                                port_to_kill = report.reportResult['web_port']
                        except:
                            pass
                    
                    # 停止 locust 进程
                    main.kill_process_using_port(port_to_kill)
                    
                    # 更新任务状态
                    task.status = '0'  # 执行完成
                    task.save()
                    
                    # 更新相关报告状态
                    for report in running_reports:
                        report.reportStatus = '99'  # 手动停止
                        report.endTime = timezone.now()
                        report.save()
                    
                    return Response({'message': f'性能测试已停止，已关闭端口 {port_to_kill}'})
                    
                except Exception as e:
                    return Response({'message': f'停止测试失败: {str(e)}'}, 
                                    status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                    
        except Exception as e:
            return Response({'message': f'停止测试失败: {str(e)}'}, 
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['post'], detail=False)
    def test_protocol(self, request):
        """测试协议连接"""
        try:
            from performanceengine.multiProtocolEngine import ProtocolTestConfig, get_protocol_test_manager
            import uuid
            
            # 获取测试参数
            protocol = request.data.get('protocol', 'HTTP').upper()
            host = request.data.get('host')
            port = request.data.get('port', 80)
            path = request.data.get('path', '/')
            method = request.data.get('method', 'GET')
            headers = request.data.get('headers', {})
            timeout = request.data.get('timeout', 10)
            custom_payload = request.data.get('custom_payload')
            ssl_verify = request.data.get('ssl_verify', True)
            
            if not host:
                return Response({'message': '主机地址不能为空'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 创建测试配置
            config = ProtocolTestConfig(
                protocol=protocol,
                host=host,
                port=port,
                path=path,
                method=method,
                headers=headers,
                timeout=timeout,
                concurrent_users=1,  # 连接测试只用一个用户
                duration=5,  # 连接测试只运行5秒
                ssl_verify=ssl_verify,
                custom_payload=custom_payload.encode() if custom_payload else None
            )
            
            # 启动测试
            test_id = str(uuid.uuid4())
            manager = get_protocol_test_manager()
            manager.start_protocol_test(test_id, config)
            
            # 等待测试完成（最多等待10秒）
            import time
            for _ in range(20):  # 20 * 0.5秒 = 10秒
                status_data = manager.get_test_status(test_id)
                if status_data['status'] in ['completed', 'failed']:
                    break
                time.sleep(0.5)
            
            # 获取测试结果
            final_status = manager.get_test_status(test_id)
            
            if final_status['status'] == 'completed':
                result = final_status['result']
                return Response({
                    'message': '协议测试成功',
                    'protocol': protocol,
                    'host': host,
                    'port': port,
                    'result': {
                        'total_requests': result['total_requests'],
                        'successful_requests': result['successful_requests'],
                        'failed_requests': result['failed_requests'],
                        'avg_response_time': result['avg_response_time'],
                        'error_rate': result['error_rate']
                    }
                })
            else:
                return Response({
                    'message': '协议测试失败',
                    'protocol': protocol,
                    'host': host,
                    'port': port,
                    'status': final_status['status']
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            return Response({'message': f'协议测试失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['post'], detail=True)
    def run_protocol_test(self, request, pk):
        """运行多协议性能测试"""
        try:
            from performanceengine.multiProtocolEngine import ProtocolTestConfig, get_protocol_test_manager
            import uuid
            
            task = get_object_or_404(PerformanceTask, id=pk)
            
            # 获取测试参数
            protocol = request.data.get('protocol', 'HTTP').upper()
            host = request.data.get('host')
            port = request.data.get('port', 80)
            concurrent_users = request.data.get('users', 10)
            duration = request.data.get('duration', 60)
            
            if not host:
                return Response({'message': '主机地址不能为空'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 从任务场景中获取配置
            scene_configs = []
            for scene in task.taskScence.all():
                for step_relation in scene.taskscencestep_set.all():
                    step = step_relation.step
                    if step.type in ['api', 'tcp', 'udp', 'websocket']:
                        step_config = self._build_protocol_config_from_step(step, host, port, concurrent_users, duration)
                        scene_configs.append(step_config)
            
            if not scene_configs:
                return Response({'message': '未找到有效的协议测试步骤'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 启动协议测试
            manager = get_protocol_test_manager()
            test_results = []
            
            for i, config in enumerate(scene_configs):
                test_id = f"{pk}_{i}_{uuid.uuid4()}"
                manager.start_protocol_test(test_id, config)
                test_results.append({
                    'test_id': test_id,
                    'protocol': config.protocol,
                    'status': 'started'
                })
            
            # 更新任务状态
            task.status = '1'  # 执行中
            task.save()
            
            return Response({
                'message': '多协议性能测试启动成功',
                'task_id': pk,
                'tests': test_results
            })
            
        except Exception as e:
            return Response({'message': f'多协议测试启动失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _build_protocol_config_from_step(self, step, host, port, concurrent_users, duration):
        """从步骤构建协议配置"""
        from performanceengine.multiProtocolEngine import ProtocolTestConfig
        
        # 获取步骤内容
        content = step.content or {}
        
        # 构建配置
        config = ProtocolTestConfig(
            protocol=step.protocol,
            host=host,
            port=port,
            timeout=step.timeout,
            concurrent_users=concurrent_users,
            duration=duration,
            ssl_verify=step.ssl_verify
        )
        
        # 根据协议类型设置特定参数
        if step.protocol in ['HTTP', 'HTTPS']:
            config.method = content.get('method', 'GET')
            config.path = content.get('url', '/')
            config.headers = content.get('headers', {})
            config.request_data = content.get('data', {})
        
        elif step.protocol in ['TCP', 'UDP']:
            if step.custom_payload:
                config.custom_payload = step.custom_payload.encode()
        
        elif step.protocol == 'WebSocket':
            config.path = content.get('path', '/')
            config.request_data = content.get('data', {})
        
        return config

    @action(methods=['get'], detail=True)
    def protocol_test_status(self, request, pk):
        """获取协议测试状态"""
        try:
            from performanceengine.multiProtocolEngine import get_protocol_test_manager
            
            # 获取所有相关的测试ID
            manager = get_protocol_test_manager()
            active_tests = manager.list_active_tests()
            
            # 过滤出属于当前任务的测试
            task_tests = [test_id for test_id in active_tests if test_id.startswith(f"{pk}_")]
            
            # 获取测试状态
            test_statuses = []
            for test_id in task_tests:
                status_data = manager.get_test_status(test_id)
                test_statuses.append({
                    'test_id': test_id,
                    'status': status_data['status'],
                    'result': status_data.get('result')
                })
            
            return Response({
                'task_id': pk,
                'total_tests': len(test_statuses),
                'tests': test_statuses
            })
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class TaskScenceViewSet(ModelViewSet):
    """性能场景视图集"""
    queryset = TaskScence.objects.all()
    serializer_class = TaskScenceSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['name', 'task']

    def perform_update(self, serializer):

        env_data = self.request.data.get('env', None)  # 假设 'env' 是传递的 env 数据
        instance = serializer.save()

        if env_data is not None:
            env_objects = TestEnv.objects.filter(id__in=env_data)
            instance.env.set(env_objects)
        else:
            instance.env.clear()

        instance.save()

    @action(methods=['get'], detail=False)
    def export(self, request, *args, **kwargs):
        queryset = TaskScence.objects.all()
        data = []
        row_one = ['场景名称', '性能任务名称', '环境名称', '权重', '创建时间']
        for obj in queryset:
            task_name = obj.task.taskName if obj.task else ''
            env_name = obj.env.name if obj.env else ''
            scene_name = obj.name
            weight = obj.weight
            create_time = obj.create_time.strftime('%Y-%m-%d %H:%M:%S') if obj.create_time else ''

            data.append([scene_name, task_name, env_name, weight, create_time])
        return export_excel('性能场景', row_one, data)


class ScenceSetpViewSet(ModelViewSet):
    """步骤视图集"""
    queryset = TaskStep.objects.all()
    serializer_class = ScenceStepSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['type','scence']

    def create(self, request, *args, **kwargs):
        data = request.data

        if isinstance(data, list):
            objs = []
            if isinstance(data, list):
                for item in data:
                    scence_id = int(item.get('scence'))
                    if scence_id is not None :
                        del item['scence']
                        scence = TaskScence.objects.get(pk=scence_id)
                        obj = TaskStep(scence=scence,**item)
                        objs.append(obj)

                TaskStep.objects.bulk_create(objs)
                created_objs = TaskStep.objects.filter(scence__in=[obj.scence.id for obj in objs]).order_by('-id')[:len(objs)]
                # 序列化创建的对象
                serialized_data = ScenceStepSerializer(created_objs, many=True).data
                return Response(serialized_data, status=status.HTTP_201_CREATED)

        return super().create(request, *args, **kwargs)

    @action(methods=['post'], detail=False)
    def batchSaveSetp(self, request, *args, **kwargs):
        try:
            data = request.data
            if not isinstance(data, list):
                return super().update(request, *args, **kwargs)

            # 定义递归函数来处理children中的数据
            def process_children(data):
                for item in data:
                    step_info = item.get('stepInfo', None)
                    if step_info:
                        id = step_info.get('id', None)
                        item['stepInfo'].pop('inputDlg', None)
                        item['stepInfo'].pop('dlg', None)

                        instance = TaskStep.objects.get(id=id)
                        serializer = ScenceStepSerializer(instance, data=step_info, partial=True)
                        if serializer.is_valid():
                            serializer.save()
                        else:
                            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

                    if item:
                        id = item.get('id', None)
                        item.pop('stepInfo')
                        instances = TaskScenceStep.objects.get(id=id)
                        serializer = TaskScenceStepSerializer(instances, data=item, partial=True)
                        if serializer.is_valid():
                            serializer.save()
                        else:
                            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


                    # 递归处理children
                    children = item.get('children', [])
                    if children:
                        process_children(children)

            # 调用递归函数处理数据
            process_children(data)

            return Response(request.data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(methods=['post'], detail=False)
    def batchSaveApiSetp(self, request, *args, **kwargs):
        data = request.data
        response_data = []
        for item in data:
            step_id = item.get('id', None)
            interface_id = item.get('content').get('id', None)
            if not step_id or not interface_id:
                return Response({'message': '缺失step_id or interface_id'}, status=status.HTTP_400_BAD_REQUEST)
            try:
                step_instance = TaskStep.objects.get(id=step_id)
                interface_instance = newInterface.objects.get(id=interface_id)
                serializer = newInterfaceSerializer(interface_instance).data
                step_instance.content = serializer
                step_instance.save()

                step_serializer = ScenceStepSerializer(step_instance)
                response_data.append(step_serializer.data)

            except Exception as e:
                return Response({'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response({'data': response_data}, status=status.HTTP_200_OK)

class TaskScenceStepViewSet(ModelViewSet):
    """性能任务场景步骤视图集"""
    queryset = TaskScenceStep.objects.all()
    serializer_class = TaskScenceStepSerializer
    filterset_fields = ['scence']
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = TaskScenceStep.objects.all().filter(parent_id=None).order_by('sort')
        scence = self.request.query_params.get('scence', None)
        if scence is None or scence == '':
            return TaskScenceStep.objects.none()
        return queryset.filter(scence=scence)
    def create(self, request, *args, **kwargs):
        data = request.data
        steps = request.data.get('step', None)
        if isinstance(steps, list):
            sort_value = request.data.get('sort', None)
            objs = []
            scence = TaskScence.objects.get(pk=int(data['scence']))
            task = PerformanceTask.objects.get(pk=int(data['task']))
            params = {
                'step': None,
                'scence': scence,
                'task': task,
                'sort': None,
                'parent': request.data.get('parent', None),
                'creator': request.data.get('creator', None)
            }
            for index, step in enumerate(steps):
                params['step'] = TaskStep.objects.get(pk=step)
                params['sort'] = sort_value if index == 0 else sort_value + index

                obj = TaskScenceStep(**params)
                objs.append(obj)
            TaskScenceStep.objects.bulk_create(objs)
            created_objs = TaskScenceStep.objects.filter(scence__in=[obj.scence.id for obj in objs]).order_by('-id')[:len(objs)]
            serialized_data = TaskScenceStepSerializer(created_objs, many=True).data
            return Response(serialized_data, status=status.HTTP_201_CREATED)
        return super().create(request, *args, **kwargs)

    def destroy(self, request, pk=None):
        try:
            instance = TaskScenceStep.objects.get(pk=pk)
            if instance.parent_id:
                if TaskScenceStep.objects.filter(parent=instance.id).exists():
                    return Response({"message": "存在未删除的子节点，请先删除子节点后再操作"}, status=status.HTTP_400_BAD_REQUEST)
                TaskScenceStep.objects.filter(id=instance.id).delete()
            else:
                if TaskScenceStep.objects.filter(parent=instance.id).exists():
                    return Response({"message": "存在未删除的子节点，请先删除子节点后再操作"}, status=status.HTTP_400_BAD_REQUEST)
                instance.delete()
            return Response({"message": "操作成功"},status=status.HTTP_204_NO_CONTENT)

        except TaskScenceStep.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @action(methods=['post'], detail=False)
    def batchTaskScenceStep(self, request, *args, **kwargs):
        try:
            data = request.data.get('data')
            type = request.data.get('type')
            response_data = []
            # 定义递归函数来处理children中的数据
            def process_children(data):
                for item in data:
                    step_info = item.get('stepInfo', None)
                    if step_info:
                        id = step_info.get('id', None)
                        lord_id = item.get('id', None)
                        if type == 'start':
                            instance = TaskStep.objects.get(id=id)
                            instance.status = True
                            instance.save()
                            response_data.append(ScenceStepSerializer(instance).data)

                        elif type == 'stop':
                            instance = TaskStep.objects.get(id=id)
                            instance.status = False
                            instance.save()
                            response_data.append(ScenceStepSerializer(instance).data)

                        elif type == 'delete':
                            # 递归处理children，确保先删除子节点
                            children = item.get('children', [])
                            if children:
                                process_children(children)

                            # 删除TaskScenceStep关联表数据
                            if lord_id:
                                TaskScenceStep.objects.filter(id=lord_id).delete()

                            # 删除 TaskStep 数据
                            if id:
                                TaskStep.objects.filter(id=id).delete()

                        else:
                            return Response({'message': 'type参数错误'}, status=status.HTTP_400_BAD_REQUEST)

                    # 递归处理children
                    children = item.get('children', [])
                    if children:
                        process_children(children)

            # 调用递归函数处理数据
            process_children(data)

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)


class TaskReportViewSet(ModelViewSet):
    """性能测试报告视图集"""
    queryset = TaskReport.objects.all().order_by('-create_time')
    permission_classes = [IsAuthenticated]
    filterset_fields = ['reportName', 'reportStatus', 'task', 'executor']
    pagination_class = TenPerPageNumberPagination
    
    def get_serializer_class(self):
        """根据action选择不同的序列化器"""
        if self.action == 'list':
            return TaskReportListSerializer
        elif self.action == 'create':
            return TaskReportCreateSerializer
        elif self.action == 'retrieve':
            return TaskReportDetailSerializer
        return TaskReportSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset().select_related('task', 'task__presetting', 'env')
        # 获取参数
        project = self.request.query_params.get('project_id')
        task_name = self.request.query_params.get('taskName', '')
        report_status = self.request.query_params.get('status', '')
        
        # 过滤
        if project:
            queryset = queryset.filter(task__project_id=project)
        if task_name:
            queryset = queryset.filter(task__taskName__icontains=task_name)
        if report_status:
            queryset = queryset.filter(reportStatus=report_status)
            
        return queryset

    @action(methods=['post'], detail=True)
    def update_status(self, request, pk):
        """更新报告状态"""
        report = get_object_or_404(TaskReport, id=pk)
        new_status = request.data.get('status')
        
        if new_status not in ['1', '0', '99']:
            return Response({'message': '无效的状态值'}, status=status.HTTP_400_BAD_REQUEST)
        
        report.reportStatus = new_status
        report.save()
        
        return Response({'message': '状态更新成功', 'status': new_status})

    @action(methods=['get'], detail=False)
    def statistics(self, request):
        """获取报告统计信息"""
        project_id = request.query_params.get('project_id')
        queryset = self.get_queryset()
        
        if project_id:
            queryset = queryset.filter(task__project_id=project_id)
        
        stats = {
            'total': queryset.count(),
            'completed': queryset.filter(reportStatus='0').count(),
            'running': queryset.filter(reportStatus='1').count(),
            'failed': queryset.filter(reportStatus='99').count(),
        }
        
        return Response(stats)

    @action(methods=['post'], detail=False)
    def batch_delete(self, request):
        """批量删除报告"""
        ids = request.data.get('ids', [])
        if not ids:
            return Response({'message': '请选择要删除的报告'}, status=status.HTTP_400_BAD_REQUEST)
        
        deleted_count = TaskReport.objects.filter(id__in=ids).delete()[0]
        return Response({'message': f'成功删除 {deleted_count} 个报告'})

    @action(methods=['get'], detail=True)
    def export(self, request, pk):
        """导出单个报告"""
        try:
            from performance.data_manager import get_report_exporter
            
            # 使用报告导出器导出报告
            report_exporter = get_report_exporter()
            return report_exporter.export_single_report(pk)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['get'], detail=True)
    def logs(self, request, pk):
        """获取报告日志"""
        report = get_object_or_404(TaskReport, id=pk)
        
        # 获取查询参数
        level = request.query_params.get('level', '')
        limit = int(request.query_params.get('limit', 1000))
        offset = int(request.query_params.get('offset', 0))
        
        # 查询日志
        from performance.models import PerformanceTestLog
        logs_query = PerformanceTestLog.objects.filter(report=report)
        
        # 按级别过滤
        if level and level != 'all':
            logs_query = logs_query.filter(level=level)
        
        # 统计总数
        total = logs_query.count()
        
        # 分页
        logs = logs_query[offset:offset + limit]
        
        # 序列化日志数据
        logs_data = []
        for log in logs:
            logs_data.append({
                'id': log.id,
                'timestamp': log.timestamp.isoformat(),
                'level': log.level,
                'message': log.message,
                'source': log.source,
                'user_count': log.user_count,
                'request_name': log.request_name,
                'response_time': log.response_time,
                'exception': log.exception
            })
        
        return Response({
            'logs': logs_data,
            'total': total,
            'limit': limit,
            'offset': offset
        })

    @action(methods=['get'], detail=False)
    def export_all(self, request):
        """批量导出报告"""
        queryset = self.get_queryset()
        project_id = request.query_params.get('project_id')
        
        if project_id:
            queryset = queryset.filter(task__project_id=project_id)
        
        data = []
        row_one = ['报告名称', '任务名称', '状态', '平均TPS', '平均CPU', '平均内存',
                  '总请求数', '成功请求数', '失败请求数', '错误率', '执行人', '创建时间']
        
        for report in queryset:
            report_data = [
                report.reportName,
                report.task.taskName,
                dict(TaskReport.choices_status).get(report.reportStatus, report.reportStatus),
                report.avgTps or 0,
                report.avgCpu or 0,
                report.avgMemory or 0,
                report.totalRequests or 0,
                report.successRequests or 0,
                report.failedRequests or 0,
                report.errorRate or 0,
                report.executor or '',
                report.create_time.strftime('%Y-%m-%d %H:%M:%S') if report.create_time else ''
            ]
            data.append(report_data)
        
        return export_excel('性能测试报告汇总', row_one, data)

    @action(methods=['post'], detail=True)
    def analyze_performance(self, request, pk):
        """分析性能报告"""
        try:
            from performanceengine.performanceAnalysis import get_performance_analysis_engine
            
            report = get_object_or_404(TaskReport, id=pk)
            time_range_hours = request.data.get('time_range_hours', 24)
            
            # 获取分析引擎
            engine = get_performance_analysis_engine()
            
            # 执行性能分析
            analysis_result = engine.analyze_performance(report.task.id, time_range_hours)
            
            # 转换为字典格式
            result_dict = {
                'task_id': analysis_result.task_id,
                'analysis_time': analysis_result.analysis_time.isoformat(),
                'overall_metrics': {
                    'tps': analysis_result.overall_metrics.tps,
                    'avg_response_time': analysis_result.overall_metrics.avg_response_time,
                    'min_response_time': analysis_result.overall_metrics.min_response_time,
                    'max_response_time': analysis_result.overall_metrics.max_response_time,
                    'p50_response_time': analysis_result.overall_metrics.p50_response_time,
                    'p95_response_time': analysis_result.overall_metrics.p95_response_time,
                    'p99_response_time': analysis_result.overall_metrics.p99_response_time,
                    'error_rate': analysis_result.overall_metrics.error_rate,
                    'total_requests': analysis_result.overall_metrics.total_requests,
                    'successful_requests': analysis_result.overall_metrics.successful_requests,
                    'failed_requests': analysis_result.overall_metrics.failed_requests
                },
                'performance_grade': analysis_result.performance_grade,
                'time_series_analysis': analysis_result.time_series_analysis,
                'error_analysis': analysis_result.error_analysis,
                'bottleneck_analysis': analysis_result.bottleneck_analysis,
                'recommendations': analysis_result.recommendations,
                'trend_analysis': analysis_result.trend_analysis
            }
            
            return Response({
                'message': '性能分析完成',
                'analysis_result': result_dict
            })
            
        except Exception as e:
            return Response({'message': f'性能分析失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['post'], detail=False)
    def compare_reports(self, request):
        """比较两个报告的性能"""
        try:
            from performanceengine.performanceAnalysis import get_performance_analysis_engine
            
            task_id1 = request.data.get('task_id1')
            task_id2 = request.data.get('task_id2')
            time_range_hours = request.data.get('time_range_hours', 24)
            
            if not task_id1 or not task_id2:
                return Response({'message': '请提供两个任务ID'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 获取分析引擎
            engine = get_performance_analysis_engine()
            
            # 执行性能比较
            comparison_result = engine.compare_performance(task_id1, task_id2, time_range_hours)
            
            return Response({
                'message': '性能比较完成',
                'comparison_result': comparison_result
            })
            
        except Exception as e:
            return Response({'message': f'性能比较失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['get'], detail=False)
    def performance_dashboard(self, request):
        """性能仪表板数据"""
        try:
            project_id = request.query_params.get('project_id')
            time_range_hours = int(request.query_params.get('time_range_hours', 24))
            
            queryset = self.get_queryset()
            if project_id:
                queryset = queryset.filter(task__project_id=project_id)
            
            # 获取最近的报告数据
            from django.utils import timezone
            from datetime import timedelta
            
            end_time = timezone.now()
            start_time = end_time - timedelta(hours=time_range_hours)
            
            recent_reports = queryset.filter(
                create_time__gte=start_time,
                create_time__lte=end_time
            ).order_by('-create_time')
            
            # 计算汇总统计
            total_reports = recent_reports.count()
            avg_tps = recent_reports.aggregate(avg_tps=models.Avg('avgTps'))['avg_tps'] or 0
            avg_response_time = recent_reports.aggregate(avg_rt=models.Avg('avgResponseTime'))['avg_rt'] or 0
            avg_error_rate = recent_reports.aggregate(avg_err=models.Avg('errorRate'))['avg_err'] or 0
            
            # 按任务分组统计
            task_stats = {}
            for report in recent_reports:
                task_name = report.task.taskName
                if task_name not in task_stats:
                    task_stats[task_name] = {
                        'task_id': report.task.id,
                        'task_name': task_name,
                        'report_count': 0,
                        'avg_tps': 0,
                        'avg_response_time': 0,
                        'avg_error_rate': 0,
                        'total_requests': 0
                    }
                
                task_stats[task_name]['report_count'] += 1
                task_stats[task_name]['avg_tps'] += report.avgTps or 0
                task_stats[task_name]['avg_response_time'] += report.avgResponseTime or 0
                task_stats[task_name]['avg_error_rate'] += report.errorRate or 0
                task_stats[task_name]['total_requests'] += report.totalRequests or 0
            
            # 计算平均值
            for task_name, stats in task_stats.items():
                if stats['report_count'] > 0:
                    stats['avg_tps'] /= stats['report_count']
                    stats['avg_response_time'] /= stats['report_count']
                    stats['avg_error_rate'] /= stats['report_count']
            
            # 性能趋势数据
            trend_data = []
            for report in recent_reports[:20]:  # 最近20个报告
                trend_data.append({
                    'timestamp': report.create_time.isoformat(),
                    'task_name': report.task.taskName,
                    'tps': report.avgTps or 0,
                    'response_time': report.avgResponseTime or 0,
                    'error_rate': report.errorRate or 0
                })
            
            dashboard_data = {
                'summary': {
                    'total_reports': total_reports,
                    'avg_tps': round(avg_tps, 2),
                    'avg_response_time': round(avg_response_time, 2),
                    'avg_error_rate': round(avg_error_rate, 2),
                    'time_range_hours': time_range_hours
                },
                'task_statistics': list(task_stats.values()),
                'performance_trends': trend_data
            }
            
            return Response(dashboard_data)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['get'], detail=False)
    def alert_rules(self, request):
        """获取告警规则列表"""
        try:
            from performance.models import AlertRule
            import json
            
            # 获取请求参数
            project_id = request.query_params.get('project_id')
            
            # 查询数据库中的告警规则
            queryset = AlertRule.objects.all()
            
            # 按项目过滤
            if project_id:
                queryset = queryset.filter(project_id=project_id)
                
            # 序列化数据
            rules = []
            for rule in queryset:
                # 解析通知方式，从JSON字符串转为列表
                notification_types = []
                if rule.notification_types:
                    try:
                        notification_types = json.loads(rule.notification_types)
                    except:
                        notification_types = ['email']
                
                # 构建规则数据
                rule_data = {
                    'id': rule.id,
                    'name': rule.name,
                    'metric_type': rule.metric_type,
                    'condition': rule.condition,
                    'threshold': rule.threshold,
                    'severity': rule.severity,
                    'notification_types': notification_types,
                    'is_enabled': rule.is_enabled,
                    'description': rule.description or '',
                    'create_time': rule.create_time.isoformat() if rule.create_time else '',
                    'project_id': rule.project_id
                }
                rules.append(rule_data)
            
            return Response(rules)
            
        except Exception as e:
            print(f"获取告警规则失败: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['get'], detail=False)
    def alert_history(self, request):
        """获取告警历史"""
        try:
            from performance.models import AlertEvent
            
            # 获取请求参数
            project_id = request.query_params.get('project_id')
            
            # 查询数据库中的告警历史
            queryset = AlertEvent.objects.all().order_by('-triggered_time')
            
            # 按项目过滤
            if project_id:
                queryset = queryset.filter(rule__project_id=project_id)
                
            # 序列化数据
            history = []
            for event in queryset:
                history_data = {
                    'id': event.id,
                    'rule_name': event.rule.name if event.rule else '未知规则',
                    'severity': event.alert_level,
                    'triggered_value': event.metric_value,
                    'threshold_value': event.threshold_value,
                    'metric_type': event.rule.metric_type if event.rule else '',
                    'status': event.status,
                    'triggered_at': event.triggered_time.isoformat() if event.triggered_time else '',
                    'resolved_at': event.resolved_time.isoformat() if event.resolved_time else None,
                    'message': event.message
                }
                history.append(history_data)
            
            return Response(history)
            
        except Exception as e:
            print(f"获取告警历史失败: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['get'], detail=False)
    def alert_status(self, request):
        """获取告警状态"""
        try:
            from performance.models import AlertRule, AlertEvent
            import json
            
            # 获取项目ID
            project_id = request.query_params.get('project_id')
            
            # 获取统计数据
            alert_count = 0
            active_count = 0
            resolved_count = 0
            
            if project_id:
                # 按项目统计
                alert_count = AlertEvent.objects.filter(project_id=project_id).count()
                active_count = AlertEvent.objects.filter(project_id=project_id, status='active').count()
                resolved_count = AlertEvent.objects.filter(project_id=project_id, status='resolved').count()
            else:
                # 全局统计
                alert_count = AlertEvent.objects.count()
                active_count = AlertEvent.objects.filter(status='active').count()
                resolved_count = AlertEvent.objects.filter(status='resolved').count()
                
            # 返回统计数据和监控状态
            return Response({
                'statistics': {
                    'total': alert_count,
                    'active': active_count,
                    'resolved': resolved_count
                },
                'monitoring_status': True  # 始终返回开启状态
            })
            
        except Exception as e:
            print(f"获取告警状态失败: {str(e)}")
            
            # 返回默认统计数据
            return Response({
                'statistics': {
                    'total': 0,
                    'active': 0,
                    'resolved': 0
                },
                'monitoring_status': False
            })

    @action(methods=['post'], detail=False)
    def add_alert_rule(self, request):
        """添加告警规则"""
        try:
            from performance.models import AlertRule
            import json
            from projects.models import Project
            
            # 打印接收到的数据进行调试
            print("接收到的告警规则数据:", request.data)
            
            # 获取请求参数
            name = request.data.get('name')
            metric_type = request.data.get('metric_type')
            condition = request.data.get('condition')
            threshold = request.data.get('threshold')
            severity = request.data.get('severity')
            notification_types = request.data.get('notification_types', [])
            description = request.data.get('description', '')
            is_enabled = request.data.get('is_enabled', True)
            project_id = request.data.get('project_id')
            
            # 获取任务ID列表
            task_ids = request.data.get('task_ids', [])
            link_all_tasks = request.data.get('link_all_tasks', False)
            
            # 如果标记为关联所有任务
            if link_all_tasks:
                print("关联所有任务")
                # 获取所有任务ID
                from apps.performance.models import PerformanceTask
                all_task_ids = list(PerformanceTask.objects.values_list('id', flat=True))
                task_ids = all_task_ids
            
            # 确保task_ids是JSON字符串，如果为空则设置为空数组
            if task_ids is None:
                task_ids = json.dumps([])
            elif isinstance(task_ids, list):
                task_ids = json.dumps(task_ids)
            
            # 确保notification_types是JSON字符串
            if isinstance(notification_types, list):
                notification_types = json.dumps(notification_types)
            
            # 设置默认的notification_config (解决字段不能为null的问题)
            notification_config = json.dumps({
                "email": {
                    "recipients": []
                },
                "webhook": {
                    "url": ""
                }
            })
            
            # 创建新规则
            rule = AlertRule.objects.create(
                name=name,
                metric_type=metric_type,
                condition=condition,
                threshold=float(threshold) if threshold else 0,
                severity=severity,
                notification_types=notification_types,
                notification_config=notification_config,  # 添加默认值
                description=description,
                is_enabled=is_enabled,
                project_id=project_id,
                task_ids=task_ids  # 添加任务ID
            )
            
            return Response({
                'message': '添加成功',
                'rule': {
                    'id': rule.id,
                    'name': rule.name,
                    'metric_type': rule.metric_type,
                    'condition': rule.condition,
                    'threshold': rule.threshold,
                    'severity': rule.severity,
                    'notification_types': json.loads(rule.notification_types) if rule.notification_types else [],
                    'task_ids': json.loads(rule.task_ids) if rule.task_ids else [],
                    'description': rule.description,
                    'is_enabled': rule.is_enabled,
                    'create_time': rule.create_time.isoformat(),
                    'update_time': rule.update_time.isoformat(),
                    'project_id': rule.project_id
                }
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            import traceback
            print(f"添加告警规则失败: {str(e)}")
            print(traceback.format_exc())
            return Response({'message': f'添加告警规则失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['patch'], detail=True)
    def update_alert_rule(self, request, pk):
        """更新告警规则"""
        try:
            from performance.models import AlertRule
            import json
            
            # 查找规则
            try:
                rule = AlertRule.objects.get(id=pk)
            except AlertRule.DoesNotExist:
                return Response({'message': f'告警规则不存在: {pk}'}, status=status.HTTP_404_NOT_FOUND)
            
            # 获取要更新的字段
            fields_to_update = {}
            for field in ['name', 'metric_type', 'condition', 'threshold', 'severity', 
                          'notification_types', 'description', 'is_enabled', 'task_ids']:
                if field in request.data:
                    # 对于notification_types特殊处理
                    if field == 'notification_types' and isinstance(request.data[field], list):
                        fields_to_update[field] = json.dumps(request.data[field])
                    # 对于task_ids特殊处理
                    elif field == 'task_ids':
                        task_ids = request.data[field]
                        link_all_tasks = request.data.get('link_all_tasks', False)
                        
                        # 如果标记为关联所有任务
                        if link_all_tasks:
                            print("更新为关联所有任务")
                            # 获取所有任务ID
                            from apps.performance.models import PerformanceTask
                            all_task_ids = list(PerformanceTask.objects.values_list('id', flat=True))
                            task_ids = all_task_ids
                        
                        if task_ids is None:
                            fields_to_update[field] = json.dumps([])
                        elif isinstance(task_ids, list):
                            fields_to_update[field] = json.dumps(task_ids)
                        else:
                            fields_to_update[field] = task_ids
                    elif field == 'threshold' and request.data[field] is not None:
                        fields_to_update[field] = float(request.data[field])
                    else:
                        fields_to_update[field] = request.data[field]
            
            # 确保notification_config有值
            if 'notification_types' in fields_to_update and not rule.notification_config:
                fields_to_update['notification_config'] = json.dumps({
                    "email": {
                        "recipients": []
                    },
                    "webhook": {
                        "url": ""
                    }
                })
            
            # 更新规则
            for field, value in fields_to_update.items():
                setattr(rule, field, value)
            rule.save()
            
            return Response({
                'message': '更新成功',
                'rule': {
                    'id': rule.id,
                    'name': rule.name,
                    'metric_type': rule.metric_type,
                    'condition': rule.condition,
                    'threshold': rule.threshold,
                    'severity': rule.severity,
                    'notification_types': json.loads(rule.notification_types) if rule.notification_types else [],
                    'task_ids': json.loads(rule.task_ids) if rule.task_ids else [],
                    'description': rule.description,
                    'is_enabled': rule.is_enabled,
                    'create_time': rule.create_time.isoformat(),
                    'update_time': rule.update_time.isoformat(),
                    'project_id': rule.project_id
                }
            })
            
        except Exception as e:
            import traceback
            print(f"更新告警规则失败: {str(e)}")
            print(traceback.format_exc())
            return Response({'message': f'更新告警规则失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['delete'], detail=True)
    def delete_alert_rule(self, request, pk):
        """删除告警规则"""
        try:
            from performance.models import AlertRule
            
            # 查找规则
            try:
                rule = AlertRule.objects.get(id=pk)
            except AlertRule.DoesNotExist:
                return Response({'message': f'告警规则不存在: {pk}'}, status=status.HTTP_404_NOT_FOUND)
            
            # 删除规则
            rule_name = rule.name
            rule.delete()
            
            # 返回成功响应
            # 修改响应代码为200，以便前端能正确识别为成功
            return Response({'message': f'成功删除告警规则: {rule_name}'}, status=status.HTTP_200_OK)
            
        except Exception as e:
            import traceback
            print(f"删除告警规则失败: {str(e)}")
            print(traceback.format_exc())
            return Response({'message': f'删除告警规则失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['post'], detail=False)
    def create_baseline(self, request):
        """创建基准线"""
        try:
            # 打印接收到的数据进行调试
            print("接收到的基准线数据:", request.data)
            
            # 获取请求参数
            name = request.data.get('name')
            description = request.data.get('description')
            avg_response_time = request.data.get('avg_response_time')
            avg_tps = request.data.get('avg_tps')
            success_rate = request.data.get('success_rate')
            avg_cpu = request.data.get('avg_cpu')
            avg_memory = request.data.get('avg_memory')
            is_active = request.data.get('is_active', True)
            project_id = request.data.get('project_id')
            
            # 详细的参数验证
            missing_params = []
            if not name:
                missing_params.append('name')
            if description is None or description == '':
                missing_params.append('description')
            if avg_response_time is None or avg_response_time == '':
                missing_params.append('avg_response_time')
            if avg_tps is None or avg_tps == '':
                missing_params.append('avg_tps')
            if success_rate is None or success_rate == '':
                missing_params.append('success_rate')
            
            if missing_params:
                return Response({
                    'message': f'缺少必要参数: {", ".join(missing_params)}'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 模拟创建基准线
            baseline = {
                'id': 999,  # 模拟生成的ID
                'name': name,
                'description': description,
                'avg_response_time': avg_response_time,
                'avg_tps': avg_tps,
                'success_rate': success_rate,
                'avg_cpu': avg_cpu or 0,
                'avg_memory': avg_memory or 0,
                'is_active': is_active,
                'creator': request.user.username if hasattr(request.user, 'username') else 'unknown',
                'create_time': timezone.now().isoformat(),
                'project_id': project_id
            }
            
            return Response({
                'message': '基准线创建成功',
                'baseline': baseline
            })
            
        except Exception as e:
            print("创建基准线失败:", str(e))
            return Response({'message': f'创建基准线失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['delete'], detail=True)
    def delete_baseline(self, request, pk):
        """删除基准线"""
        try:
            # 模拟删除基准线
            return Response({'message': '基准线删除成功'}, status=status.HTTP_204_NO_CONTENT)
            
        except Exception as e:
            return Response({'message': f'删除基准线失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['patch'], detail=True)
    def update_baseline(self, request, pk):
        """更新基准线"""
        try:
            # 模拟更新基准线
            return Response({'message': '基准线更新成功'})
            
        except Exception as e:
            return Response({'message': f'更新基准线失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['post'], detail=False)
    def start_alert_monitoring(self, request):
        """启动告警监控"""
        try:
            # 实际项目中，这里可以设置一个系统设置标志位
            # 从而启用告警监控功能
            
            return Response({
                'message': '告警监控已启动',
                'monitoring_status': True
            })
            
        except Exception as e:
            print(f"启动告警监控失败: {str(e)}")
            return Response({'message': f'启动告警监控失败: {str(e)}'}, 
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['post'], detail=False)
    def stop_alert_monitoring(self, request):
        """停止告警监控"""
        try:
            # 实际项目中，这里可以关闭系统设置标志位
            # 从而停用告警监控功能
            
            return Response({
                'message': '告警监控已停止',
                'monitoring_status': False
            })
            
        except Exception as e:
            print(f"停止告警监控失败: {str(e)}")
            return Response({'message': f'停止告警监控失败: {str(e)}'}, 
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['post'], detail=False)
    def acknowledge_alert(self, request):
        """确认告警"""
        try:
            from performance.models import AlertEvent
            
            # 获取请求参数
            alert_id = request.data.get('alert_id')
            acknowledger = request.data.get('acknowledger', request.user.username if hasattr(request.user, 'username') else 'system')
            
            if not alert_id:
                return Response({'message': '缺少参数alert_id'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 查找告警
            try:
                alert = AlertEvent.objects.get(id=alert_id)
            except AlertEvent.DoesNotExist:
                return Response({'message': f'告警不存在: {alert_id}'}, status=status.HTTP_404_NOT_FOUND)
            
            # 更新告警状态
            alert.status = "acknowledged"
            alert.acknowledged_by = acknowledger
            alert.acknowledged_time = timezone.now()
            alert.save()
            
            return Response({
                'message': '告警已确认',
                'alert': {
                    'id': alert.id,
                    'status': alert.status,
                    'acknowledged_by': alert.acknowledged_by,
                    'acknowledged_time': alert.acknowledged_time.isoformat() if alert.acknowledged_time else None
                }
            })
            
        except Exception as e:
            print(f"确认告警失败: {str(e)}")
            return Response({'message': f'确认告警失败: {str(e)}'}, 
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['post'], detail=False)
    def compare_task_performance(self, request):
        """对比多个任务的性能"""
        try:
            task_ids = request.data.get('task_ids', [])
            time_range_hours = request.data.get('time_range_hours', 24)
            
            if len(task_ids) < 2:
                return Response({'message': '请至少选择2个任务进行对比'}, status=status.HTTP_400_BAD_REQUEST)
            
            if len(task_ids) > 5:
                return Response({'message': '最多支持5个任务同时对比'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 获取任务信息
            tasks = PerformanceTask.objects.filter(id__in=task_ids)
            if len(tasks) != len(task_ids):
                return Response({'message': '部分任务不存在'}, status=status.HTTP_404_NOT_FOUND)
            
            # 获取对比数据
            comparison_data = self._get_task_comparison_data(task_ids, time_range_hours)
            
            # 计算对比指标
            comparison_metrics = self._calculate_comparison_metrics(comparison_data)
            
            # 生成对比报告
            comparison_report = self._generate_comparison_report(tasks, comparison_data, comparison_metrics)
            
            return Response({
                'message': '任务性能对比完成',
                'comparison_report': comparison_report
            })
            
        except Exception as e:
            return Response({'message': f'任务性能对比失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_task_comparison_data(self, task_ids: List[int], time_range_hours: int) -> Dict:
        """获取任务对比数据"""
        from django.utils import timezone
        from datetime import timedelta
        
        end_time = timezone.now()
        start_time = end_time - timedelta(hours=time_range_hours)
        
        comparison_data = {}
        
        for task_id in task_ids:
            reports = TaskReport.objects.filter(
                task_id=task_id,
                create_time__gte=start_time,
                create_time__lte=end_time
            ).order_by('create_time')
            
            task_data = {
                'task_id': task_id,
                'reports_count': reports.count(),
                'metrics': [],
                'timeline': []
            }
            
            for report in reports:
                metric = {
                    'timestamp': report.create_time,
                    'tps': report.avgTps or 0,
                    'avg_response_time': report.avgResponseTime or 0,
                    'min_response_time': report.minResponseTime or 0,
                    'max_response_time': report.maxResponseTime or 0,
                    'p95_response_time': report.p95ResponseTime or 0,
                    'error_rate': report.errorRate or 0,
                    'total_requests': report.totalRequests or 0,
                    'successful_requests': report.successRequests or 0,
                    'failed_requests': report.failedRequests or 0,
                    'cpu_usage': report.avgCpu or 0,
                    'memory_usage': report.avgMemory or 0
                }
                task_data['metrics'].append(metric)
                task_data['timeline'].append({
                    'time': report.create_time.isoformat(),
                    'tps': metric['tps'],
                    'response_time': metric['avg_response_time'],
                    'error_rate': metric['error_rate']
                })
            
            comparison_data[task_id] = task_data
        
        return comparison_data

    def _calculate_comparison_metrics(self, comparison_data: Dict) -> Dict:
        """计算对比指标"""
        metrics_summary = {}
        
        for task_id, data in comparison_data.items():
            if not data['metrics']:
                continue
            
            metrics = data['metrics']
            
            # 计算平均值
            avg_tps = sum(m['tps'] for m in metrics) / len(metrics)
            avg_response_time = sum(m['avg_response_time'] for m in metrics) / len(metrics)
            avg_error_rate = sum(m['error_rate'] for m in metrics) / len(metrics)
            
            # 计算最值
            max_tps = max(m['tps'] for m in metrics)
            min_tps = min(m['tps'] for m in metrics)
            max_response_time = max(m['avg_response_time'] for m in metrics)
            min_response_time = min(m['avg_response_time'] for m in metrics)
            
            # 计算总计
            total_requests = sum(m['total_requests'] for m in metrics)
            total_successful = sum(m['successful_requests'] for m in metrics)
            total_failed = sum(m['failed_requests'] for m in metrics)
            
            # 计算稳定性指标
            tps_values = [m['tps'] for m in metrics]
            rt_values = [m['avg_response_time'] for m in metrics]
            
            from statistics import stdev, mean
            tps_stability = 1 - (stdev(tps_values) / mean(tps_values)) if len(tps_values) > 1 and mean(tps_values) > 0 else 0
            rt_stability = 1 - (stdev(rt_values) / mean(rt_values)) if len(rt_values) > 1 and mean(rt_values) > 0 else 0
            
            metrics_summary[task_id] = {
                'averages': {
                    'tps': round(avg_tps, 2),
                    'response_time': round(avg_response_time, 2),
                    'error_rate': round(avg_error_rate, 2)
                },
                'extremes': {
                    'max_tps': max_tps,
                    'min_tps': min_tps,
                    'max_response_time': max_response_time,
                    'min_response_time': min_response_time
                },
                'totals': {
                    'total_requests': total_requests,
                    'successful_requests': total_successful,
                    'failed_requests': total_failed
                },
                'stability': {
                    'tps_stability': round(tps_stability, 3),
                    'response_time_stability': round(rt_stability, 3)
                }
            }
        
        return metrics_summary

    def _generate_comparison_report(self, tasks, comparison_data: Dict, comparison_metrics: Dict) -> Dict:
        """生成对比报告"""
        
        # 基本信息
        task_info = {}
        for task in tasks:
            task_info[task.id] = {
                'task_name': task.taskName,
                'task_type': task.taskType,
                'distributed_mode': task.distributed_mode,
                'create_time': task.create_time.isoformat() if task.create_time else None
            }
        
        # 性能排名
        task_ids = list(comparison_metrics.keys())
        
        # TPS排名
        tps_ranking = sorted(task_ids, 
                            key=lambda tid: comparison_metrics[tid]['averages']['tps'], 
                            reverse=True)
        
        # 响应时间排名（越小越好）
        rt_ranking = sorted(task_ids, 
                           key=lambda tid: comparison_metrics[tid]['averages']['response_time'])
        
        # 错误率排名（越小越好）
        error_ranking = sorted(task_ids, 
                              key=lambda tid: comparison_metrics[tid]['averages']['error_rate'])
        
        # 稳定性排名
        stability_ranking = sorted(task_ids, 
                                  key=lambda tid: (comparison_metrics[tid]['stability']['tps_stability'] + 
                                                 comparison_metrics[tid]['stability']['response_time_stability']) / 2, 
                                  reverse=True)
        
        # 计算相对性能差异
        performance_diff = {}
        if len(task_ids) >= 2:
            baseline_task = tps_ranking[0]  # 以TPS最高的作为基准
            baseline_metrics = comparison_metrics[baseline_task]
            
            for task_id in task_ids:
                if task_id == baseline_task:
                    continue
                
                current_metrics = comparison_metrics[task_id]
                
                performance_diff[task_id] = {
                    'tps_diff_percent': ((current_metrics['averages']['tps'] - baseline_metrics['averages']['tps']) / 
                                        baseline_metrics['averages']['tps'] * 100) if baseline_metrics['averages']['tps'] > 0 else 0,
                    'rt_diff_percent': ((current_metrics['averages']['response_time'] - baseline_metrics['averages']['response_time']) / 
                                       baseline_metrics['averages']['response_time'] * 100) if baseline_metrics['averages']['response_time'] > 0 else 0,
                    'error_diff_percent': ((current_metrics['averages']['error_rate'] - baseline_metrics['averages']['error_rate']) / 
                                          baseline_metrics['averages']['error_rate'] * 100) if baseline_metrics['averages']['error_rate'] > 0 else 0
                }
        
        # 生成总结和建议
        summary = self._generate_comparison_summary(task_info, comparison_metrics, tps_ranking)
        
        return {
            'task_info': task_info,
            'comparison_metrics': comparison_metrics,
            'rankings': {
                'tps': [{'task_id': tid, 'task_name': task_info[tid]['task_name'], 'value': comparison_metrics[tid]['averages']['tps']} for tid in tps_ranking],
                'response_time': [{'task_id': tid, 'task_name': task_info[tid]['task_name'], 'value': comparison_metrics[tid]['averages']['response_time']} for tid in rt_ranking],
                'error_rate': [{'task_id': tid, 'task_name': task_info[tid]['task_name'], 'value': comparison_metrics[tid]['averages']['error_rate']} for tid in error_ranking],
                'stability': [{'task_id': tid, 'task_name': task_info[tid]['task_name'], 'value': (comparison_metrics[tid]['stability']['tps_stability'] + comparison_metrics[tid]['stability']['response_time_stability']) / 2} for tid in stability_ranking]
            },
            'performance_differences': performance_diff,
            'timeline_data': {tid: comparison_data[tid]['timeline'] for tid in task_ids},
            'summary': summary
        }

    def _generate_comparison_summary(self, task_info: Dict, comparison_metrics: Dict, tps_ranking: List) -> Dict:
        """生成对比总结"""
        if not tps_ranking:
            return {'message': '无有效数据进行对比'}
        
        best_task_id = tps_ranking[0]
        worst_task_id = tps_ranking[-1] if len(tps_ranking) > 1 else best_task_id
        
        best_task_name = task_info[best_task_id]['task_name']
        worst_task_name = task_info[worst_task_id]['task_name']
        
        best_metrics = comparison_metrics[best_task_id]
        worst_metrics = comparison_metrics[worst_task_id] if worst_task_id != best_task_id else best_metrics
        
        recommendations = []
        
        # 性能差异分析
        if worst_task_id != best_task_id:
            tps_diff = best_metrics['averages']['tps'] - worst_metrics['averages']['tps']
            rt_diff = worst_metrics['averages']['response_time'] - best_metrics['averages']['response_time']
            
            if tps_diff > 100:
                recommendations.append(f"任务 '{worst_task_name}' 的TPS显著低于 '{best_task_name}'，建议优化性能配置")
            
            if rt_diff > 200:
                recommendations.append(f"任务 '{worst_task_name}' 的响应时间比 '{best_task_name}' 高 {rt_diff:.0f}ms，建议检查网络和服务器性能")
        
        # 稳定性分析
        unstable_tasks = []
        for task_id, metrics in comparison_metrics.items():
            stability = (metrics['stability']['tps_stability'] + metrics['stability']['response_time_stability']) / 2
            if stability < 0.8:
                unstable_tasks.append(task_info[task_id]['task_name'])
        
        if unstable_tasks:
            recommendations.append(f"任务 {', '.join(unstable_tasks)} 性能波动较大，建议检查系统稳定性")
        
        if not recommendations:
            recommendations.append("所有任务性能表现良好，建议继续监控")
        
        return {
            'best_performance_task': {
                'task_id': best_task_id,
                'task_name': best_task_name,
                'avg_tps': best_metrics['averages']['tps'],
                'avg_response_time': best_metrics['averages']['response_time']
            },
            'worst_performance_task': {
                'task_id': worst_task_id,
                'task_name': worst_task_name,
                'avg_tps': worst_metrics['averages']['tps'],
                'avg_response_time': worst_metrics['averages']['response_time']
            } if worst_task_id != best_task_id else None,
            'recommendations': recommendations,
            'comparison_count': len(task_info)
        }

    @action(methods=['post'], detail=True)
    def generate_report_html(self, request, pk):
        """生成HTML格式的性能报告"""
        try:
            from performanceengine.reportGenerator import get_report_generator
            from performanceengine.performanceAnalysis import get_performance_analysis_engine
            
            report = get_object_or_404(TaskReport, id=pk)
            template_type = request.data.get('template_type', 'summary')  # summary, detailed, comparison
            time_range_hours = request.data.get('time_range_hours', 24)
            
            # 获取分析数据
            analysis_engine = get_performance_analysis_engine()
            analysis_result = analysis_engine.analyze_performance(report.task.id, time_range_hours)
            
            # 准备报告数据
            report_data = {
                'task_name': report.task.taskName,
                'report_name': report.reportName,
                'metrics': {
                    'avg_tps': analysis_result.overall_metrics.tps,
                    'max_tps': analysis_result.overall_metrics.tps * 1.2,  # 估算值
                    'avg_response_time': analysis_result.overall_metrics.avg_response_time,
                    'p95_response_time': analysis_result.overall_metrics.p95_response_time,
                    'p99_response_time': analysis_result.overall_metrics.p99_response_time,
                    'error_rate': analysis_result.overall_metrics.error_rate,
                    'total_requests': analysis_result.overall_metrics.total_requests
                },
                'test_config': {
                    'environment': report.testEnvironment or '默认环境',
                    'concurrent_users': report.concurrentUsers,
                    'duration': report.duration,
                    'distributed_mode': report.task.get_distributed_mode_display()
                },
                'performance_grade': analysis_result.performance_grade,
                'error_analysis': analysis_result.error_analysis,
                'bottleneck_analysis': analysis_result.bottleneck_analysis,
                'recommendations': analysis_result.recommendations
            }
            
            # 准备图表数据
            chart_data = self._prepare_chart_data(analysis_result, report)
            report_data['chart_data'] = chart_data
            
            # 生成报告
            generator = get_report_generator()
            html_content = generator.generate_report(template_type, report_data)
            
            return Response({
                'message': '报告生成成功',
                'html_content': html_content,
                'template_type': template_type
            })
            
        except Exception as e:
            return Response({'message': f'生成报告失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _prepare_chart_data(self, analysis_result, report):
        """准备图表数据"""
        chart_data = {}
        
        # TPS趋势图数据
        if hasattr(analysis_result, 'time_series_analysis') and analysis_result.time_series_analysis:
            timeline_data = analysis_result.time_series_analysis.get('timeline_data', [])
            if timeline_data:
                chart_data['tps_chart'] = {
                    'x': [point['time'] for point in timeline_data],
                    'y': [point['tps'] for point in timeline_data],
                    'xlabel': '时间',
                    'ylabel': 'TPS'
                }
                
                chart_data['response_time_chart'] = {
                    'x': [point['time'] for point in timeline_data],
                    'y': [point['response_time'] for point in timeline_data],
                    'xlabel': '时间',
                    'ylabel': '响应时间(ms)'
                }
        
        # 错误分布图数据
        if analysis_result.error_analysis and analysis_result.error_analysis.get('top_errors'):
            top_errors = analysis_result.error_analysis['top_errors']
            chart_data['error_rate_chart'] = {
                'labels': [error['error_type'] for error in top_errors],
                'values': [error['count'] for error in top_errors]
            }
        
        # 响应时间分布直方图（模拟数据）
        import numpy as np
        np.random.seed(42)
        response_times = np.random.normal(
            analysis_result.overall_metrics.avg_response_time, 
            analysis_result.overall_metrics.avg_response_time * 0.3, 
            1000
        )
        response_times = response_times[response_times > 0]  # 确保都是正值
        
        chart_data['response_time_distribution'] = {
            'values': response_times.tolist(),
            'xlabel': '响应时间(ms)',
            'ylabel': '频次'
        }
        
        return chart_data

    @action(methods=['get'], detail=False) 
    def report_templates(self, request):
        """获取可用的报告模板"""
        try:
            from performanceengine.reportGenerator import get_report_generator
            
            generator = get_report_generator()
            templates = generator.list_templates()
            
            return Response({
                'templates': templates
            })
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['post'], detail=False)
    def generate_comparison_report(self, request):
        """生成对比报告"""
        try:
            from performanceengine.reportGenerator import get_report_generator
            
            task_ids = request.data.get('task_ids', [])
            time_range_hours = request.data.get('time_range_hours', 24)
            
            if len(task_ids) < 2:
                return Response({'message': '请至少选择2个任务进行对比'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 获取对比数据
            comparison_data = self._get_task_comparison_data(task_ids, time_range_hours)
            comparison_metrics = self._calculate_comparison_metrics(comparison_data)
            
            # 获取任务信息
            tasks = PerformanceTask.objects.filter(id__in=task_ids)
            comparison_report = self._generate_comparison_report(tasks, comparison_data, comparison_metrics)
            
            # 准备报告数据
            report_data = {
                'comparison_data': comparison_report,
                'recommendations': comparison_report['summary']['recommendations']
            }
            
            # 准备对比图表数据
            chart_data = self._prepare_comparison_chart_data(comparison_data, comparison_metrics)
            report_data['chart_data'] = chart_data
            
            # 生成HTML报告
            generator = get_report_generator()
            html_content = generator.generate_report('comparison', report_data)
            
            return Response({
                'message': '对比报告生成成功',
                'html_content': html_content,
                'comparison_data': comparison_report
            })
            
        except Exception as e:
            return Response({'message': f'生成对比报告失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _prepare_comparison_chart_data(self, comparison_data, comparison_metrics):
        """准备对比图表数据"""
        chart_data = {}
        
        task_ids = list(comparison_metrics.keys())
        
        # 性能指标对比柱状图
        chart_data['performance_comparison'] = {
            'categories': ['TPS', '响应时间', '错误率'],
            'series': {},
            'xlabel': '性能指标',
            'ylabel': '值'
        }
        
        for task_id in task_ids:
            metrics = comparison_metrics[task_id]
            task_name = f"任务{task_id}"
            chart_data['performance_comparison']['series'][task_name] = [
                metrics['averages']['tps'],
                metrics['averages']['response_time'],
                metrics['averages']['error_rate']
            ]
        
        # 趋势对比图
        if comparison_data:
            chart_data['trend_comparison'] = {
                'x': [],
                'series': {},
                'xlabel': '时间',
                'ylabel': 'TPS'
            }
            
            # 使用第一个任务的时间线作为x轴
            first_task_data = list(comparison_data.values())[0]
            if first_task_data['timeline']:
                chart_data['trend_comparison']['x'] = [point['time'] for point in first_task_data['timeline']]
                
                for task_id, data in comparison_data.items():
                    task_name = f"任务{task_id}"
                    chart_data['trend_comparison']['series'][task_name] = [
                        point['tps'] for point in data['timeline']
                    ]
        
        return chart_data


    @action(methods=['post'], detail=False)
    def export_data(self, request):
        """导出测试数据"""
        try:
            from apps.performance.data_manager import get_data_exporter
            
            task_ids = request.data.get('task_ids', [])
            export_format = request.data.get('format', 'json')
            include_reports = request.data.get('include_reports', True)
            include_config = request.data.get('include_config', True)
            
            if not task_ids:
                return Response({'message': '请选择要导出的任务'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 验证任务是否存在
            existing_tasks = PerformanceTask.objects.filter(id__in=task_ids)
            if len(existing_tasks) != len(task_ids):
                return Response({'message': '部分任务不存在'}, status=status.HTTP_404_NOT_FOUND)
            
            exporter = get_data_exporter()
            result = exporter.export_task_data(task_ids, export_format, include_reports, include_config)
            
            if export_format == 'json':
                return Response({
                    'message': '数据导出成功',
                    'filename': result['filename'],
                    'content': result['content'],
                    'content_type': result['content_type']
                })
            else:
                # CSV, Excel, ZIP格式直接返回文件响应
                return result
                
        except Exception as e:
            return Response({'message': f'数据导出失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['post'], detail=False)
    def import_data(self, request):
        """导入测试数据"""
        try:
            from apps.performance.data_manager import get_data_importer
            
            project_id = request.data.get('project_id')
            file_format = request.data.get('format', 'json')
            import_options = request.data.get('options', {})
            
            if not project_id:
                return Response({'message': '请指定项目ID'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 检查文件
            uploaded_file = request.FILES.get('file')
            if not uploaded_file:
                return Response({'message': '请上传文件'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 读取文件内容
            file_content = uploaded_file.read()
            
            # 导入数据
            importer = get_data_importer()
            result = importer.import_data(file_content, file_format, project_id, import_options)
            
            if result['success']:
                return Response({
                    'message': result['message'],
                    'imported_counts': result['imported_counts']
                })
            else:
                return Response({
                    'message': result['message'],
                    'errors': result.get('errors', [])
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            return Response({'message': f'数据导入失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['get'], detail=False)
    def export_template(self, request):
        """获取导出模板"""
        try:
            template_type = request.query_params.get('type', 'task')
            
            if template_type == 'task':
                template_data = {
                    'tasks': [{
                        'taskName': '示例任务',
                        'taskType': '10',
                        'desc': '这是一个示例任务描述',
                        'runPattern': '1',
                        'distributed_mode': 'single',
                        'total_workers': 1
                    }],
                    'scenes': [{
                        'name': '示例场景',
                        'weight': 100
                    }],
                    'steps': [{
                        'name': '示例步骤',
                        'type': 'api',
                        'protocol': 'HTTP',
                        'content': {
                            'url': '/api/test',
                            'method': 'GET',
                            'headers': {}
                        },
                        'timeout': 30,
                        'retry_count': 0
                    }]
                }
            else:
                template_data = {'message': '不支持的模板类型'}
            
            return Response({
                'template_type': template_type,
                'template_data': template_data,
                'supported_formats': ['json', 'csv', 'excel']
            })
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


    @action(methods=['post'], detail=False)
    def create_baseline(self, request):
        """创建性能基准线 - 使用数据库模型"""
        try:
            name = request.data.get('name')
            description = request.data.get('description', '')
            task_id = request.data.get('task_id')
            project_id = request.data.get('project_id')
            environment = request.data.get('environment', 'default')
            
            # 直接获取性能指标
            avg_response_time = float(request.data.get('avg_response_time', 0))
            avg_tps = float(request.data.get('avg_tps', 0))
            success_rate = float(request.data.get('success_rate', 100))
            avg_cpu = float(request.data.get('avg_cpu', 0))
            avg_memory = float(request.data.get('avg_memory', 0))
            
            # 获取容差设置
            response_time_tolerance = float(request.data.get('response_time_tolerance', 15.0))
            tps_tolerance = float(request.data.get('tps_tolerance', 10.0))
            error_rate_tolerance = float(request.data.get('error_rate_tolerance', 50.0))
            cpu_usage_tolerance = float(request.data.get('cpu_usage_tolerance', 20.0))
            memory_usage_tolerance = float(request.data.get('memory_usage_tolerance', 20.0))
            
            if not all([name, task_id]):
                return Response({'message': '缺少必要参数: name, task_id'}, 
                               status=status.HTTP_400_BAD_REQUEST)
            
            # 验证任务是否存在
            try:
                task = PerformanceTask.objects.get(id=task_id)
                if not project_id:
                    project_id = task.project_id
            except PerformanceTask.DoesNotExist:
                return Response({'message': '任务不存在'}, status=status.HTTP_404_NOT_FOUND)
            
            # 初始化基准数据对象
            baseline_data = {
                'avgTps': avg_tps,
                'avgResponseTime': avg_response_time,
                'errorRate': 100 - success_rate,  # 转换成错误率
                'avgCpu': avg_cpu,
                'avgMemory': avg_memory,
                'totalRequests': 0,
                'duration': 0
            }
            
            # 如果用户没有提供指标值，尝试从最新报告中获取
            if not any([avg_response_time, avg_tps, avg_cpu, avg_memory]):
                latest_report = TaskReport.objects.filter(task_id=task_id).order_by('-create_time').first()
                if latest_report:
                    avg_response_time = latest_report.avgResponseTime or 0
                    avg_tps = latest_report.avgTps or 0
                    error_rate = latest_report.errorRate or 0
                    success_rate = 100 - error_rate
                    avg_cpu = latest_report.avgCpu or 0
                    avg_memory = latest_report.avgMemory or 0
                    
                    # 更新基准数据对象
                    baseline_data = {
                        'avgTps': avg_tps,
                        'avgResponseTime': avg_response_time,
                        'errorRate': error_rate,
                        'avgCpu': avg_cpu,
                        'avgMemory': avg_memory,
                        'totalRequests': latest_report.totalRequests,
                        'duration': latest_report.duration
                    }


            # 创建基准线
            manager = get_baseline_manager()
            baseline_id = manager.create_baseline(
                name=name,
                description=description,
                task_id=task_id,
                project_id=project_id,
                environment=environment,
                baseline_data=baseline_data,
                created_by=request.user.username if hasattr(request.user, 'username') else 'unknown'
            )
            
            return Response({
                'message': '基准线创建成功',
                'baseline_id': baseline_id
            })
            
        except Exception as e:
            return Response({'message': f'创建基准线失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['get'], detail=False)
    def list_baselines(self, request):
        """获取基准线列表 - 使用数据库模型"""
        try:
            # 获取查询参数
            task_id = request.query_params.get('task_id')
            project_id = request.query_params.get('project_id')
            environment = request.query_params.get('environment')
            name = request.query_params.get('name', '')
            active_only = request.query_params.get('active_only', 'true').lower() == 'true'
            
            # 获取分页参数
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 20))
            
            # 使用数据库管理器查询基准线
            manager = get_baseline_manager()
            baselines = manager.list_baselines(
                task_id=int(task_id) if task_id else None,
                project_id=int(project_id) if project_id else None,
                environment=environment,
                active_only=active_only
            )
            
            # 如果提供了名称，进行过滤
            if name:
                baselines = [b for b in baselines if name.lower() in b.name.lower()]
            
            # 统计总数
            total_count = len(baselines)
            
            # 分页
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            paginated_baselines = baselines[start_idx:end_idx]
            
            baseline_list = []
            for baseline in paginated_baselines:
                # 直接使用数据库模型的字段值
                baseline_data = {
                    'id': str(baseline.id),
                    'name': baseline.name,
                    'description': baseline.description or '',
                    'task_id': baseline.task_id,
                    'task_name': baseline.task_name,
                    'environment': baseline.environment,
                    'creator': baseline.created_by,  # 重命名为creator以匹配前端期望
                    'create_time': baseline.create_time.isoformat(),  # 已经是create_time
                    'update_time': baseline.update_time.isoformat() if baseline.update_time else None,
                    'is_active': baseline.is_active,
                    # 添加前端组件期望的字段，保留两位小数
                    'avg_response_time': round(baseline.response_time, 2),
                    'avg_tps': round(baseline.tps, 2),
                    'success_rate': round(baseline.success_rate, 2),
                    'avg_cpu': round(baseline.cpu_usage, 2), 
                    'avg_memory': round(baseline.memory_usage, 2),
                    # 指标数量固定为5个: 响应时间、TPS、成功率、CPU、内存
                    'metrics_count': 5
                }
                baseline_list.append(baseline_data)
            
            # 返回Django REST Framework风格的分页响应
            return Response({
                'count': total_count,
                'results': baseline_list  # 使用results字段包含数据列表
            })
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['get'], detail=True)
    def get_baseline(self, request, pk):
        """获取基准线详情 - 使用数据库模型"""
        try:
            # 使用数据库管理器获取基准线
            manager = get_baseline_manager()
            baseline = manager.get_baseline(pk)
            
            if not baseline:
                return Response({'message': '基准线不存在'}, status=status.HTTP_404_NOT_FOUND)
            
            # 创建指标数据字典，保留两位小数
            metrics_data = {
                'response_time': {
                    'metric_type': 'response_time',
                    'target_value': round(baseline.response_time, 2),
                    'tolerance_percent': round(baseline.response_time_tolerance, 2),
                    'min_threshold': 0,
                    'max_threshold': round(baseline.response_time * (1 + baseline.response_time_tolerance/100), 2)
                },
                'tps': {
                    'metric_type': 'tps',
                    'target_value': round(baseline.tps, 2),
                    'tolerance_percent': round(baseline.tps_tolerance, 2),
                    'min_threshold': round(baseline.tps * (1 - baseline.tps_tolerance/100), 2),
                    'max_threshold': float('inf')
                },
                'error_rate': {
                    'metric_type': 'error_rate',
                    'target_value': round(baseline.error_rate, 2),
                    'tolerance_percent': round(baseline.error_rate_tolerance, 2),
                    'min_threshold': 0,
                    'max_threshold': round(baseline.error_rate * (1 + baseline.error_rate_tolerance/100), 2)
                },
                'cpu_usage': {
                    'metric_type': 'cpu_usage',
                    'target_value': round(baseline.cpu_usage, 2),
                    'tolerance_percent': round(baseline.cpu_usage_tolerance, 2),
                    'min_threshold': 0,
                    'max_threshold': round(baseline.cpu_usage * (1 + baseline.cpu_usage_tolerance/100), 2)
                },
                'memory_usage': {
                    'metric_type': 'memory_usage',
                    'target_value': round(baseline.memory_usage, 2),
                    'tolerance_percent': round(baseline.memory_usage_tolerance, 2),
                    'min_threshold': 0,
                    'max_threshold': round(baseline.memory_usage * (1 + baseline.memory_usage_tolerance/100), 2)
                }
                }
            
            baseline_data = {
                'id': str(baseline.id),
                'name': baseline.name,
                'description': baseline.description or '',
                'task_id': baseline.task_id,
                'task_name': baseline.task_name,
                'environment': baseline.environment,
                'baseline_metrics': metrics_data,
                'created_by': baseline.created_by,
                'created_time': baseline.create_time.isoformat(),
                'updated_time': baseline.update_time.isoformat() if baseline.update_time else None,
                'is_active': baseline.is_active,
                'baseline_data': baseline.baseline_data,
                
                # 添加前端组件期望的字段，保留两位小数
                'avg_response_time': round(baseline.response_time, 2),
                'avg_tps': round(baseline.tps, 2),
                'success_rate': round(baseline.success_rate, 2),
                'avg_cpu': round(baseline.cpu_usage, 2),
                'avg_memory': round(baseline.memory_usage, 2)
            }
            
            return Response(baseline_data)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['put', 'patch'], detail=True)
    def update_baseline(self, request, pk):
        """更新基准线"""
        try:
            # 使用本地的baseline_manager
            
            manager = get_baseline_manager()
            
            update_data = {}
            # 基本字段
            for field in ['name', 'description', 'environment', 'is_active']:
                if field in request.data:
                    update_data[field] = request.data[field]
            
            # 性能指标字段
            for field in ['avg_response_time', 'avg_tps', 'success_rate', 'avg_cpu', 'avg_memory']:
                if field in request.data:
                    try:
                        update_data[field] = float(request.data[field])
                    except (ValueError, TypeError):
                        return Response({'message': f'{field} 必须是一个有效的数值'}, 
                                      status=status.HTTP_400_BAD_REQUEST)
            
            # 输出调试日志
            print(f"更新基准线 {pk} 数据: {update_data}")
            
            if manager.update_baseline(pk, **update_data):
                return Response({'message': '基准线更新成功'})
            else:
                return Response({'message': '基准线不存在'}, status=status.HTTP_404_NOT_FOUND)
                
        except Exception as e:
            return Response({'message': f'更新基准线失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['delete'], detail=True)
    def delete_baseline(self, request, pk):
        """删除基准线"""
        try:
            # 使用本地的baseline_manager
            
            manager = get_baseline_manager()
            
            if manager.delete_baseline(pk):
                return Response({'message': '基准线删除成功'})
            else:
                return Response({'message': '基准线不存在'}, status=status.HTTP_404_NOT_FOUND)
                
        except Exception as e:
            return Response({'message': f'删除基准线失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['post'], detail=True)
    def compare_with_baseline(self, request, pk):
        """与基准线进行对比"""
        try:
            # 使用本地的baseline_manager
            
            baseline_id = request.data.get('baseline_id')
            if not baseline_id:
                return Response({'message': '请指定基准线ID'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 获取报告数据
            report = get_object_or_404(TaskReport, id=pk)
            report_data = {
                'avgTps': report.avgTps,
                'avgResponseTime': report.avgResponseTime,
                'errorRate': report.errorRate,
                'avgCpu': report.avgCpu,
                'avgMemory': report.avgMemory,
                'totalRequests': report.totalRequests,
                'duration': report.duration
            }
            
            manager = get_baseline_manager()
            comparison = manager.compare_with_baseline(baseline_id, report_data, pk)
            
            # 转换对比结果为可序列化格式
            metric_comparisons_data = {}
            for name, comp in comparison.metric_comparisons.items():
                metric_comparisons_data[name] = {
                    'result': comp['result'].value,
                    'target_value': comp['target_value'],
                    'actual_value': comp['actual_value'],
                    'diff_percent': comp['diff_percent'],
                    'metric_type': comp['metric_type']
                }
            
            comparison_data = {
                'baseline_id': comparison.baseline_id,
                'baseline_name': comparison.baseline_name,
                'task_id': comparison.task_id,
                'report_id': comparison.report_id,
                'comparison_time': comparison.comparison_time.isoformat(),
                'overall_result': comparison.overall_result.value,
                'metric_comparisons': metric_comparisons_data,
                'performance_score': comparison.performance_score,
                'recommendations': comparison.recommendations
            }
            
            return Response({
                'message': '基准线对比完成',
                'comparison': comparison_data
            })
            
        except Exception as e:
            return Response({'message': f'基准线对比失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['get'], detail=False)
    def baseline_statistics(self, request):
        """获取基准线统计信息"""
        try:
            # 使用本地的baseline_manager
            
            task_id = request.query_params.get('task_id')
            days = int(request.query_params.get('days', 30))
            
            manager = get_baseline_manager()
            statistics = manager.get_baseline_statistics(
                task_id=int(task_id) if task_id else None,
                days=days
            )
            
            return Response(statistics)
            
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['post'], detail=True)
    def auto_create_baseline(self, request, pk):
        """从报告自动创建基准线"""
        try:
            # 使用本地的baseline_manager
            
            report = get_object_or_404(TaskReport, id=pk)
            environment = request.data.get('environment', 'default')
            
            report_data = {
                'avgTps': report.avgTps,
                'avgResponseTime': report.avgResponseTime,
                'errorRate': report.errorRate,
                'avgCpu': report.avgCpu,
                'avgMemory': report.avgMemory,
                'totalRequests': report.totalRequests,
                'duration': report.duration
            }
            
            manager = get_baseline_manager()
            baseline_id = manager.auto_create_baseline_from_report(
                report_data=report_data,
                task_id=report.task.id,
                environment=environment
            )
            
            if baseline_id:
                return Response({
                    'message': '自动创建基准线成功',
                    'baseline_id': baseline_id
                })
            else:
                return Response({
                    'message': '该任务环境已存在基准线或报告数据不适合作为基准线'
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            return Response({'message': f'自动创建基准线失败: {str(e)}'}, 
                           status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['get'], detail=True)
    def target_service_status(self, request, pk):
        """获取目标服务监控状态"""
        try:
            report = get_object_or_404(TaskReport, id=pk)
            
            # 这里应该根据实际的目标服务获取监控数据
            # 可以从环境配置中获取目标服务的信息
            if report.env and hasattr(report.env, 'host') and report.env.host:
                target_host = report.env.host
                # 解析主机信息
                import urllib.parse
                try:
                    parsed = urllib.parse.urlparse(target_host if target_host.startswith('http') else f'http://{target_host}')
                    host = parsed.hostname
                    port = parsed.port or 80
                    
                    # 检测服务状态
                    service_status = self._check_service_health(host, port)
                    
                    # 模拟监控数据（实际应用中应该从监控系统获取）
                    monitoring_data = {
                        'service_status': service_status,
                        'connection_pool_active': 45,
                        'connection_pool_total': 50,
                        'db_connections': 12,
                        'cache_hit_rate': 95.2,
                        'response_time_trend': {
                            'labels': self._generate_time_labels(),
                            'values': self._generate_response_time_data()
                        },
                        'recent_errors': self._get_recent_errors(report),
                        'target_host': target_host,
                        'last_check_time': timezone.now().isoformat()
                    }
                    
                    return Response(monitoring_data)
                except Exception as e:
                    return Response({
                        'error': f'解析目标服务地址失败: {str(e)}',
                        'service_status': 'parse_error',
                        'target_host': target_host
                    }, status=status.HTTP_400_BAD_REQUEST)
            else:
                # 返回模拟数据以避免前端错误
                return Response({
                    'service_status': 'unknown',
                    'connection_pool_active': 0,
                    'connection_pool_total': 0,
                    'db_connections': 0,
                    'cache_hit_rate': 0,
                    'response_time_trend': {
                        'labels': self._generate_time_labels(),
                        'values': [0] * 10
                    },
                    'recent_errors': [],
                    'target_host': '未配置',
                    'last_check_time': timezone.now().isoformat(),
                    'message': '未配置目标服务'
                })
                
        except Exception as e:
            return Response({
                'error': str(e),
                'service_status': 'error',
                'connection_pool_active': 0,
                'connection_pool_total': 0,
                'db_connections': 0,
                'cache_hit_rate': 0,
                'response_time_trend': {
                    'labels': self._generate_time_labels(),
                    'values': [0] * 10
                },
                'recent_errors': [],
                'target_host': '错误',
                'last_check_time': timezone.now().isoformat()
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _check_service_health(self, host, port):
        """检查服务健康状态"""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, port))
            sock.close()
            return 'healthy' if result == 0 else 'error'
        except:
            return 'error'
    
    def _generate_time_labels(self):
        """生成时间标签"""
        from datetime import datetime, timedelta
        now = datetime.now()
        labels = []
        for i in range(10):
            time_point = now - timedelta(minutes=i*5)
            labels.append(time_point.strftime('%H:%M'))
        return labels[::-1]
    
    def _generate_response_time_data(self):
        """生成响应时间数据"""
        import random
        return [random.randint(100, 300) for _ in range(10)]
    
    def _get_recent_errors(self, report):
        """获取近期错误统计"""
        # 从报告结果中解析错误信息
        if report.reportResult:
            try:
                result = json.loads(report.reportResult)
                failure_summary = result.get('failure_summary', [])
                error_stats = {}
                
                for failure in failure_summary:
                    error_type = failure.get('name', 'UnknownError')
                    count = failure.get('occurrences', 0)
                    if error_type in error_stats:
                        error_stats[error_type] += count
                    else:
                        error_stats[error_type] = count
                
                return [{'type': k, 'count': v} for k, v in error_stats.items()]
            except:
                pass
        
        return [
            {'type': 'TimeoutError', 'count': 3},
            {'type': 'ConnectionError', 'count': 1}
        ]





class SystemResourceViewSet(ModelViewSet):
    """系统资源监控视图集"""
    permission_classes = [IsAuthenticated]
    
    @action(methods=['get'], detail=False)
    def current_status(self, request):
        """获取当前系统状态"""
        try:
            try:
                from performanceengine.systemMonitor import get_current_system_monitor, SystemResourceMonitor
            except ImportError as e:
                return Response({
                    'error': f'系统监控模块导入失败: {str(e)}',
                    'message': '可能缺少psutil依赖，请安装: pip install psutil',
                    'status': 'module_error'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            monitor = get_current_system_monitor()
            if monitor:
                # 获取实时系统信息
                current_info = monitor.collect_system_info()
                
                # 检查是否收集失败
                if 'error' in current_info:
                    return Response({
                        'status': 'monitoring_error', 
                        'current': {
                            'timestamp': current_info.get('timestamp'),
                            'error': current_info.get('error')
                        },
                        'message': '系统信息收集失败'
                    })
                
                try:
                    avg_resources = monitor.get_average_resources()
                except Exception as e:
                    avg_resources = {'error': str(e)}
                
                return Response({
                    'status': 'monitoring',
                    'current': current_info,
                    'averages': avg_resources,
                    'data_points': len(monitor.resource_data)
                })
            else:
                # 没有监控时，返回单次采集的系统信息
                temp_monitor = SystemResourceMonitor()
                current_info = temp_monitor.collect_system_info()
                
                # 检查是否收集失败
                if 'error' in current_info:
                    return Response({
                        'status': 'collection_error',
                        'current': current_info,
                        'message': '系统信息收集失败，可能缺少psutil依赖'
                    })
                
                return Response({
                    'status': 'not_monitoring',
                    'current': current_info,
                    'message': '未启动实时监控'
                })
                
        except Exception as e:
            return Response({
                'error': str(e),
                'message': '获取系统状态失败',
                'status': 'error'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(methods=['get'], detail=False)
    def history(self, request):
        """获取历史监控数据"""
        try:
            from performanceengine.systemMonitor import get_current_system_monitor
            
            monitor = get_current_system_monitor()
            if not monitor:
                return Response({
                    'error': '监控未启动',
                    'data': []
                }, status=status.HTTP_400_BAD_REQUEST)
            
            limit = int(request.query_params.get('limit', 50))
            history_data = monitor.get_resource_history(limit)
            
            return Response({
                'data': history_data,
                'total_points': len(monitor.resource_data),
                'returned_points': len(history_data)
            })
            
        except Exception as e:
            return Response({
                'error': str(e),
                'message': '获取历史数据失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(methods=['get'], detail=False)
    def process_status(self, request):
        """获取进程状态"""
        try:
            from performanceengine.systemMonitor import ProcessResourceMonitor
            
            process_name = request.query_params.get('process', 'locust')
            monitor = ProcessResourceMonitor(process_name)
            process_info = monitor.get_process_resources()
            
            return Response(process_info)
            
        except Exception as e:
            return Response({
                'error': str(e),
                'message': '获取进程状态失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


    @action(methods=['post'], detail=True)
    def rerun(self, request, pk):
        """重新运行测试报告对应的任务"""
        try:
            # 获取原始报告
            report = get_object_or_404(TaskReport, id=pk)
            task = report.task
            
            # 检查任务是否存在
            if not task:
                return Response({
                    'message': '关联的任务不存在'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # 获取请求参数
            rerun = request.data.get('rerun', True)
            copy_settings = request.data.get('copy_settings', True)
            new_report_name = request.data.get('new_report_name', f'{report.reportName}_重跑_{timezone.now().strftime("%Y%m%d_%H%M%S")}')
            env_id = request.data.get('env_id')  # 从前端获取环境ID
            
            # 如果前端没有传递env_id，使用原报告的环境
            if env_id:
                try:
                    from apps.utils.models import TestEnv
                    env = TestEnv.objects.get(id=env_id)
                except TestEnv.DoesNotExist:
                    return Response({
                        'message': f'指定的环境(ID: {env_id})不存在'
                    }, status=status.HTTP_400_BAD_REQUEST)
            else:
                env = report.env
            # 创建新的报告
            new_report = TaskReport.objects.create(
                task=task,
                reportName=new_report_name,
                desc=f'基于报告 {report.reportName} 重新运行',
                reportStatus='1',  # 运行中
                env=env,  # 使用指定的环境
                executor=request.user.username if hasattr(request.user, 'username') else 'system',
                startTime=timezone.now()
            )
            
            # 异步启动测试任务
            try:
                # 这里应该调用实际的测试执行逻辑
                # 由于原有的 run 方法在 PerformanceTaskViewSet 中，我们需要调用它
                from performanceengine import main
                
                # 构建测试配置
                test_config = {
                    'rerun': True,
                    'original_report_id': pk,
                    'new_report_id': new_report.id,
                    'copy_settings': copy_settings
                }
                
                # 启动后台任务执行测试
                # 注意：这里应该使用异步任务队列（如Celery）来执行，避免阻塞请求
                import threading
                def run_test():
                    try:
                        # 获取环境ID - 使用新指定的环境
                        final_env_id = env.id if env else None
                        
                        # 调用测试执行逻辑
                        main.run_performance_test(
                            task_id=task.id,
                            env_id=final_env_id,
                            report_id=new_report.id,
                            config=test_config
                        )
                    except Exception as e:
                        # 更新报告状态为失败
                        new_report.reportStatus = '99'
                        new_report.endTime = timezone.now()
                        new_report.desc = f'执行失败: {str(e)}'
                        new_report.save()
                
                # 启动后台线程
                thread = threading.Thread(target=run_test)
                thread.daemon = True
                thread.start()
                
                return Response({
                    'message': '重新运行任务已启动',
                    'report_id': new_report.id,
                    'report_name': new_report.reportName,
                    'task_id': task.id,
                    'task_name': task.taskName
                })
                
            except Exception as e:
                # 如果启动失败，删除已创建的报告
                new_report.delete()
                raise e
                
        except Exception as e:
            return Response({
                'message': f'重新运行失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ScenarioDebugViewSet(ModelViewSet):
    """场景调试视图集"""
    queryset = TaskScence.objects.all()
    permission_classes = [IsAuthenticated]
    
    @action(methods=['post'], detail=False)
    def debug_scenario(self, request):
        """调试场景功能"""
        try:
            # 获取请求参数
            scene_id = request.data.get('scene_id')
            env_id = request.data.get('env_id')
            debug_mode = request.data.get('debug_mode', True)
            
            if not scene_id:
                return Response({
                    'message': '缺少必要参数: scene_id'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 获取场景信息
            try:
                scene = TaskScence.objects.get(id=scene_id)
            except TaskScence.DoesNotExist:
                return Response({
                    'message': '场景不存在'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # 获取场景的步骤
            from performance.models import TaskScenceStep
            scene_steps = TaskScenceStep.objects.filter(scence=scene).order_by('sort')
            
            if not scene_steps.exists():
                return Response({
                    'message': '场景没有配置步骤，无法调试'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 获取环境信息
            test_env = None
            if env_id:
                try:
                    from projects.models import TestEnv
                    test_env = TestEnv.objects.get(id=env_id)
                except TestEnv.DoesNotExist:
                    return Response({
                        'message': '测试环境不存在'
                    }, status=status.HTTP_404_NOT_FOUND)
            
            # 构建调试配置
            debug_config = {
                'scene_id': scene_id,
                'scene_name': scene.name,
                'env_id': env_id,
                'env_name': test_env.name if test_env else None,
                'debug_mode': debug_mode,
                'step_count': scene_steps.count(),
                'task_id': scene.task.id,
                'task_name': scene.task.taskName
            }
            
            # 验证场景步骤配置
            validation_results = []
            for step_relation in scene_steps:
                step = step_relation.step
                step_validation = self._validate_step(step, test_env)
                validation_results.append({
                    'step_id': step.id,
                    'step_name': step.name,
                    'step_type': step.type,
                    'sort': step_relation.sort,
                    'is_valid': step_validation['is_valid'],
                    'errors': step_validation['errors'],
                    'warnings': step_validation['warnings']
                })
            
            # 执行场景调试
            debug_results = self._execute_scenario_debug(scene, scene_steps, test_env, debug_config)
            
            return Response({
                'message': '场景调试执行完成',
                'debug_config': debug_config,
                'validation_results': validation_results,
                'debug_results': debug_results,
                # 提供顶层字段（前端可能从这里读取）
                'execution_time': debug_results.get('execution_time', 0),
                'executionTime': debug_results.get('execution_time', 0),  # 驼峰命名兼容
                'success_rate': debug_results.get('success_rate', 0),
                'successRate': debug_results.get('success_rate', 0),  # 驼峰命名兼容
                'total_steps': len(validation_results),
                'totalSteps': len(validation_results),  # 驼峰命名兼容
                'successful_steps': debug_results.get('successful_steps', 0),
                'successfulSteps': debug_results.get('successful_steps', 0),  # 驼峰命名兼容
                'failed_steps': debug_results.get('failed_steps', 0),
                'failedSteps': debug_results.get('failed_steps', 0),  # 驼峰命名兼容
                'skipped_steps': len(validation_results) - debug_results.get('successful_steps', 0) - debug_results.get('failed_steps', 0),
                'skippedSteps': len(validation_results) - debug_results.get('successful_steps', 0) - debug_results.get('failed_steps', 0),  # 驼峰命名兼容
                'overall_result': debug_results.get('overall_result', 'unknown'),
                'overallResult': debug_results.get('overall_result', 'unknown')  # 驼峰命名兼容
            })
            
        except Exception as e:
            return Response({
                'message': f'场景调试失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _validate_step(self, step, test_env):
        """验证单个步骤配置"""
        errors = []
        warnings = []
        is_valid = True
        
        try:
            # 解析步骤内容
            step_content = step.content if isinstance(step.content, dict) else {}
            
            if step.type == 'api':
                # 验证API步骤
                if not step_content.get('url'):
                    errors.append('缺少请求URL')
                    is_valid = False
                if not step_content.get('method'):
                    errors.append('缺少请求方法')
                    is_valid = False
                
                # 检查URL是否包含环境变量
                url = step_content.get('url', '')
                if '${' in url and not test_env:
                    warnings.append('URL包含环境变量但未选择测试环境')
                
            elif step.type == 'script':
                # 验证脚本步骤
                if not step.script or step.script.strip() == '':
                    errors.append('脚本内容为空')
                    is_valid = False
                
            elif step.type == 'wait':
                # 验证等待步骤
                wait_time = step_content.get('time', 0)
                try:
                    wait_time = float(wait_time)
                    if wait_time < 0:
                        errors.append('等待时间不能为负数')
                        is_valid = False
                    elif wait_time > 300:  # 5分钟
                        warnings.append('等待时间过长，可能影响调试效率')
                except (ValueError, TypeError):
                    errors.append('等待时间格式不正确')
                    is_valid = False
                    
            elif step.type == 'if':
                # 验证条件步骤
                if not step_content.get('variable'):
                    errors.append('缺少判断变量')
                    is_valid = False
                if not step_content.get('JudgmentMode'):
                    errors.append('缺少判断模式')
                    is_valid = False
                if step_content.get('value') is None:
                    errors.append('缺少判断值')
                    is_valid = False
            
            elif step.type == 'loop':
                # 验证循环步骤
                loop_type = step_content.get('select')
                if loop_type == 'count':
                    try:
                        cycle_count = int(step_content.get('cycleIndex', 0))
                        if cycle_count <= 0:
                            errors.append('循环次数必须大于0')
                            is_valid = False
                        elif cycle_count > 1000:
                            warnings.append('循环次数过多，可能影响调试性能')
                    except (ValueError, TypeError):
                        errors.append('循环次数格式不正确')
                        is_valid = False
                elif loop_type == 'for':
                    if not step_content.get('variable'):
                        errors.append('缺少循环变量')
                        is_valid = False
                    if not step_content.get('variableName'):
                        errors.append('缺少循环变量名')
                        is_valid = False
                        
        except Exception as e:
            errors.append(f'步骤验证异常: {str(e)}')
            is_valid = False
        
        return {
            'is_valid': is_valid,
            'errors': errors,
            'warnings': warnings
        }
    
    def _execute_scenario_debug(self, scene, scene_steps, test_env, debug_config):
        """执行场景调试"""
        import time
        start_time = time.time()
        
        debug_results = {
            'execution_time': 0,
            'successful_steps': 0,
            'failed_steps': 0,
            'step_results': [],
            'overall_result': 'success',
            'error_summary': []
        }
        
        try:
            # 准备环境变量
            env_variables = {}
            if test_env and hasattr(test_env, 'global_variable'):
                try:
                    # TestEnv模型中的global_variable是JSONField，直接使用
                    env_variables = test_env.global_variable if test_env.global_variable else {}
                except:
                    env_variables = {}
            
            # 依次执行每个步骤
            for step_relation in scene_steps:
                step = step_relation.step
                step_start_time = time.time()
                
                step_result = {
                    'step_id': step.id,
                    'step_name': step.name,
                    'step_type': step.type,
                    'sort': step_relation.sort,
                    'status': 'success',
                    'execution_time': 0,
                    'message': '',
                    'details': {}
                }
                
                try:
                    # 根据步骤类型执行调试
                    if step.type == 'api':
                        step_debug_result = self._debug_api_step(step, env_variables, test_env)
                        step_result.update(step_debug_result)
                    elif step.type == 'script':
                        step_debug_result = self._debug_script_step(step, env_variables)
                        step_result.update(step_debug_result)
                    elif step.type == 'wait':
                        step_debug_result = self._debug_wait_step(step)
                        step_result.update(step_debug_result)
                    elif step.type == 'if':
                        step_debug_result = self._debug_condition_step(step, env_variables)
                        step_result.update(step_debug_result)
                    elif step.type == 'loop':
                        step_debug_result = self._debug_loop_step(step, env_variables)
                        step_result.update(step_debug_result)
                    else:
                        step_result.update({
                            'status': 'skipped',
                            'message': f'步骤类型 {step.type} 暂不支持调试'
                        })
                    
                    step_result['execution_time'] = round(time.time() - step_start_time, 3)
                    
                    # 统计步骤状态
                    if step_result['status'] == 'success':
                        debug_results['successful_steps'] += 1
                    elif step_result['status'] == 'failed':
                        debug_results['failed_steps'] += 1
                        debug_results['error_summary'].append(f"步骤 {step.name}: {step_result['message']}")
                    # 跳过的步骤不计入成功或失败
                    
                except Exception as e:
                    step_result.update({
                        'status': 'failed',
                        'message': f'步骤执行异常: {str(e)}',
                        'execution_time': round(time.time() - step_start_time, 3)
                    })
                    debug_results['failed_steps'] += 1
                    debug_results['error_summary'].append(f"步骤 {step.name}: {str(e)}")
                
                debug_results['step_results'].append(step_result)
            
            # 计算总体结果
            total_steps = len(scene_steps)
            # 计算实际执行的步骤数（排除跳过的步骤）
            executed_steps = debug_results['successful_steps'] + debug_results['failed_steps']
            debug_results['success_rate'] = round((debug_results['successful_steps'] / executed_steps) * 100, 2) if executed_steps > 0 else 0
            debug_results['execution_time'] = round(time.time() - start_time, 3)
            
            # 设置总体结果状态
            if debug_results['failed_steps'] > 0:
                debug_results['overall_result'] = 'partial' if debug_results['successful_steps'] > 0 else 'failed'
            elif executed_steps == 0:
                debug_results['overall_result'] = 'skipped'
            else:
                debug_results['overall_result'] = 'success'
            
        except Exception as e:
            debug_results.update({
                'overall_result': 'failed',
                'execution_time': round(time.time() - start_time, 3),
                'error_summary': [f'场景调试执行异常: {str(e)}']
            })
        
        return debug_results
    
    def _debug_api_step(self, step, env_variables, test_env):
        """调试API步骤"""
        try:
            import requests
            import json
            
            step_content = step.content if isinstance(step.content, dict) else {}
            
            # 获取URL和其他参数
            url = step_content.get('url', '')
            method = step_content.get('method', 'GET').upper()
            headers = step_content.get('headers', {})
            params = step_content.get('params', {})
            data = step_content.get('data', {})
            
            # 简单的环境变量替换
            for var_name, var_value in env_variables.items():
                placeholder = f'${{{var_name}}}'
                url = url.replace(placeholder, str(var_value))
            
            # 拼接环境host地址
            if test_env and hasattr(test_env, 'host') and test_env.host:
                base_url = test_env.host.rstrip('/')  # 移除尾部斜杠
                # 如果URL是相对路径（以/开头但不是完整URL），则拼接base_url
                if url.startswith('/') and not url.startswith('http'):
                    url = base_url + url
                elif not url.startswith('http'):
                    # 如果URL既不是完整URL也不以/开头，则拼接/
                    url = base_url + '/' + url.lstrip('/')
            elif not url.startswith('http'):
                # 如果没有配置环境或环境没有host，但URL不是完整URL，则返回错误
                return {
                    'status': 'failed',
                    'message': f'请求失败: Invalid URL \'{url}\': No scheme supplied. Perhaps you meant https://{url}?',
                    'details': {'error_type': 'invalid_url', 'original_url': url}
                }
            
            # 执行HTTP请求（带超时）
            timeout = getattr(step, 'timeout', 10)
            
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                params=params,
                json=data if data else None,
                timeout=timeout,
                verify=getattr(step, 'ssl_verify', True)
            )
            
            return {
                'status': 'success' if response.status_code < 400 else 'failed',
                'message': f'HTTP {method} {url} -> {response.status_code}',
                'details': {
                    'url': url,
                    'method': method,
                    'status_code': response.status_code,
                    'response_time': response.elapsed.total_seconds(),
                    'response_size': len(response.content),
                    'headers': dict(response.headers)
                }
            }
            
        except requests.exceptions.Timeout:
            return {
                'status': 'failed',
                'message': '请求超时',
                'details': {'error_type': 'timeout'}
            }
        except requests.exceptions.ConnectionError:
            return {
                'status': 'failed',
                'message': '连接错误',
                'details': {'error_type': 'connection_error'}
            }
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'请求失败: {str(e)}',
                'details': {'error_type': 'request_error', 'error': str(e)}
            }
    
    def _debug_script_step(self, step, env_variables):
        """调试脚本步骤"""
        try:
            # 这里只是模拟脚本验证，实际执行需要考虑安全性
            script_content = step.script or ''
            
            if not script_content.strip():
                return {
                    'status': 'failed',
                    'message': '脚本内容为空',
                    'details': {}
                }
            
            # 简单的脚本语法检查
            try:
                compile(script_content, '<script>', 'exec')
                return {
                    'status': 'success',
                    'message': '脚本语法检查通过',
                    'details': {
                        'script_lines': len(script_content.split('\n')),
                        'script_length': len(script_content)
                    }
                }
            except SyntaxError as e:
                return {
                    'status': 'failed',
                    'message': f'脚本语法错误: {str(e)}',
                    'details': {'syntax_error': str(e)}
                }
                
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'脚本调试失败: {str(e)}',
                'details': {'error': str(e)}
            }
    
    def _debug_wait_step(self, step):
        """调试等待步骤"""
        try:
            step_content = step.content if isinstance(step.content, dict) else {}
            wait_time = float(step_content.get('time', 1))
            
            if wait_time < 0:
                return {
                    'status': 'failed',
                    'message': '等待时间不能为负数',
                    'details': {'wait_time': wait_time}
                }
            
            # 在调试模式下，限制最大等待时间
            actual_wait_time = min(wait_time, 5)  # 最多等待5秒
            
            import time
            time.sleep(actual_wait_time)
            
            return {
                'status': 'success',
                'message': f'等待 {actual_wait_time} 秒',
                'details': {
                    'configured_wait_time': wait_time,
                    'actual_wait_time': actual_wait_time,
                    'debug_limited': wait_time > 5
                }
            }
            
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'等待步骤失败: {str(e)}',
                'details': {'error': str(e)}
            }
    
    def _debug_condition_step(self, step, env_variables):
        """调试条件步骤"""
        try:
            step_content = step.content if isinstance(step.content, dict) else {}
            
            variable = step_content.get('variable', '')
            judgment_mode = step_content.get('JudgmentMode', '')
            expected_value = step_content.get('value', '')
            
            # 获取变量值
            actual_value = env_variables.get(variable, 'undefined')
            
            # 模拟条件判断
            result = False
            try:
                if judgment_mode == '==':
                    result = str(actual_value) == str(expected_value)
                elif judgment_mode == '!=':
                    result = str(actual_value) != str(expected_value)
                elif judgment_mode == '>':
                    result = float(actual_value) > float(expected_value)
                elif judgment_mode == '<':
                    result = float(actual_value) < float(expected_value)
                elif judgment_mode == '>=':
                    result = float(actual_value) >= float(expected_value)
                elif judgment_mode == '<=':
                    result = float(actual_value) <= float(expected_value)
                elif judgment_mode == 'contains':
                    result = str(expected_value) in str(actual_value)
                else:
                    return {
                        'status': 'failed',
                        'message': f'不支持的判断模式: {judgment_mode}',
                        'details': {}
                    }
                
                return {
                    'status': 'success',
                    'message': f'条件判断: {variable} {judgment_mode} {expected_value} = {result}',
                    'details': {
                        'variable': variable,
                        'actual_value': actual_value,
                        'expected_value': expected_value,
                        'judgment_mode': judgment_mode,
                        'result': result
                    }
                }
                
            except (ValueError, TypeError) as e:
                return {
                    'status': 'failed',
                    'message': f'条件判断类型错误: {str(e)}',
                    'details': {
                        'variable': variable,
                        'actual_value': actual_value,
                        'expected_value': expected_value,
                        'error': str(e)
                    }
                }
                
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'条件步骤调试失败: {str(e)}',
                'details': {'error': str(e)}
            }
    
    def _debug_loop_step(self, step, env_variables):
        """调试循环步骤"""
        try:
            step_content = step.content if isinstance(step.content, dict) else {}
            loop_type = step_content.get('select', 'count')
            
            if loop_type == 'count':
                cycle_count = int(step_content.get('cycleIndex', 1))
                # 在调试模式下限制循环次数
                actual_count = min(cycle_count, 10)  # 最多循环10次
                
                return {
                    'status': 'success',
                    'message': f'循环配置验证通过，将循环 {actual_count} 次',
                    'details': {
                        'loop_type': 'count',
                        'configured_count': cycle_count,
                        'actual_count': actual_count,
                        'debug_limited': cycle_count > 10
                    }
                }
                
            elif loop_type == 'for':
                variable = step_content.get('variable', '')
                variable_name = step_content.get('variableName', '')
                
                # 获取循环变量值
                loop_data = env_variables.get(variable, [])
                if not isinstance(loop_data, (list, tuple, str)):
                    loop_data = [loop_data] if loop_data else []
                
                actual_count = min(len(loop_data), 10)  # 最多循环10次
                
                return {
                    'status': 'success',
                    'message': f'for循环配置验证通过，将循环 {actual_count} 次',
                    'details': {
                        'loop_type': 'for',
                        'variable': variable,
                        'variable_name': variable_name,
                        'loop_data_length': len(loop_data),
                        'actual_count': actual_count,
                        'debug_limited': len(loop_data) > 10
                    }
                }
            else:
                return {
                    'status': 'failed',
                    'message': f'不支持的循环类型: {loop_type}',
                    'details': {'loop_type': loop_type}
                }
                
        except Exception as e:
            return {
                'status': 'failed',
                'message': f'循环步骤调试失败: {str(e)}',
                'details': {'error': str(e)}
            }