# -*- coding: utf-8 -*-
# @author: HRUN

from rest_framework.routers import DefaultRouter
from django.urls import path
from . import views

router = DefaultRouter()
router.register('wxPush', views.WxPushViewSet)

# 手动注册 API视图集
urlpatterns = [
    path('yapi/', views.YApiViewSet.as_view(), name='yapi'),
    path('curl/', views.CurlViewSet.as_view(), name='curl'),
    path('postman/', views.PostmanViewSet.as_view(), name='postman'),
    path('apipost/', views.ApipostViewSet.as_view(), name='apipost'),
    path('swagger/file/', views.SwaggerFileViewSet.as_view(), name='swagger_file'),
    path('swagger/url/', views.SwaggerUrlViewSet.as_view(), name='swagger_url'),
    path('jsfetch/', views.JsFetchViewSet.as_view(), name='jsfetch'),
    path('ProjectBoard/', views.ProjectBoardView.as_view(), name='ProjectBoard'),
]

# 将自定义路由器中的 URLs 与手动注册的 URL 集成
urlpatterns += router.urls
