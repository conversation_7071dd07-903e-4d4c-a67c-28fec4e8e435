validation_rules:
  required: # 针对必填项的规则
    - ""
    - null

  boundary_values: # 边界值规则
    - 1
    - 5
    - 15
    - 50
    - 99
    - 200
    - 512
    - 513

  data_types: # 针对不同数据类型的规则
    string:
      - abc123
      - 123456
      - @!$####$%^&*&*()_+
      - AAbb测试一下

    integer or number:
      - 3
      - abc123
      - @!$####$%^&*&*()_+
      - AAbb测试一下
    datetime:
      - 当前日期
      - 大于当前日期
      - 小于当前日期
      - 其他日期格式

    object:
      allowed_keys:
        - "key1"
        - "key2"
    array:

  business_logic: # 业务逻辑验证
    duplicate_operation: "不能重复操作同一条数据"
    expired_data: "数据已过期"
    data_permissions: "数据权限不足"
    sequence_validation: "操作顺序错误"

  database_interaction: # 与数据库的交互验证
    multiple_storage_check: "检查存储的条数是否正确"
    missing_field_check: "检查是否存在字段漏存或错误"
    update_check: "检查更新操作是否成功"

  format_checks: # 格式验证
    phone_number:
      pattern: "^[0-9]{11}$"
    email:
      pattern: "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$"
    postal_code:
      pattern: "^[0-9]{6}$"
    file_upload:
      allowed_types:
        - "image/png"
        - "application/pdf"
    date_format:
      pattern: "yyyy-MM-dd"