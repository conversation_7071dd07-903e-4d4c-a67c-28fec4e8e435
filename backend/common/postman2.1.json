{"info": {"name": "TMS接口管理", "description": "", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [], "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "env": [], "item": [{"name": "干线版本接口管理", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "运输平台", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "下达订单", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "/tms/transport/trunk-load/release", "host": ["tms"], "query": [], "variable": [], "path": ["transport", "trunk-load", "release"]}, "header": [], "body": {"mode": "raw", "raw": "{  \n    \"loadCode\": \"XWGS22041502010\"\n    \n}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}]}, {"name": "各承运商接口管理", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "tms2.0新增弘力2.0(云贵模式)", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "oms->tms并下单承运商", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "用户管理-登录获取公钥", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["apt.assert('response.raw.status == 200');\napt.assert('response.json.message == \"Success\"')\n\n\napt.globals.set(\"key\", response.json.data.key);"]}}], "request": {"method": "POST", "url": {"raw": "/tms/base/login/query_key", "host": ["tms"], "query": [], "variable": [], "path": ["base", "login", "query_key"]}, "header": [{"key": "Content-Type", "value": "application/json;charset=UTF-8", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"loginFlag\": 1,\n\t\"accountNum\": \"A80438\"\n}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "查询订单详情", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["apt.globals.set(\"planbill\", response.json.data.tmsOrder.omsPlanOrderCode);"]}}], "request": {"method": "POST", "url": {"raw": "/tms/business/order_detail/get_order_detail", "host": ["tms"], "query": [], "variable": [], "path": ["business", "order_detail", "get_order_detail"]}, "header": [], "body": {"mode": "raw", "raw": "{\n    \"tmsOrderCode\": \"{{tmsbill}}\",\n    \"uid\": \"879\"\n}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "指定承运商", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "/tms/business/order/assign", "host": ["tms"], "query": [], "variable": [], "path": ["business", "order", "assign"]}, "header": [], "body": {"mode": "raw", "raw": "{\n    \"uid\":\"879\",\n    \"carrierCode\":\"ZYLD\",\n    \"transportTypeCode\":\"LD\",\n    \"carrierName\":\"中原零担\",\n    \"transportTypeName\":\"零担\",\n    \"tmsOrderCodeList\":[\"{{tmsbill}}\"]\n}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}, {"name": "撤销/拦截TMS订单", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "/tms/oms-data/cancel-order", "host": ["tms"], "query": [], "variable": [], "path": ["oms-data", "cancel-order"]}, "header": [], "body": {"mode": "raw", "raw": "{\n  \"orderId\": \"1461518335438397441\",  //OMS单Id号（不用改）\n  \"orderNo\": \"S000202211317143\",   //OMS单号\n  \"plannedOrderId\": \"1457958174358827614\",  //计划单号\n  \"arrangeOrderNo\": \"1457640815121351746\",   //排车单号\n//   \"revertDesc\": \"xkj取消\"   //撤单说明\n    \"remark\":\"测试撤销\"\n}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "tms向承运商撤销", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "/tms/carrier/order/cancel", "host": ["tms"], "query": [], "variable": [], "path": ["carrier", "order", "cancel"]}, "header": [], "body": {"mode": "raw", "raw": "{\n    \"omsCarOrderCode\":\"1457640815121351746\",\n    \"tmsOrderCode\":\"TMS2201130000036\",\n    \"omsOrderCode\":\"S000202211317143\",\n    \"omsPlanOrderCode\":\"1457958174358827614\",\n    \"remark\":\"123\",\n    \"carrierCode\":\"ZYLD\"\n}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}, {"name": "TMS2.0新增国力零担/专车承运商", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "tms接收承运商信息", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "车辆信息回传（修改司机信息）", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "/tms/carrier_data/producer/carrier_driver_modify", "host": ["tms"], "query": [], "variable": [], "path": ["carrier_data", "producer", "carrier_driver_modify"]}, "header": [{"key": "carrier", "value": "GL", "type": "text"}, {"key": "sign<PERSON>ethod", "value": "SHA-256", "type": "text"}, {"key": "signature", "value": "{{sha256}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n   \"carrierCode\":\"zhongyuan\",  //承运商编码\n    \"waybill\": \"RJ22011404862\", //装载单号\n    \"planLoadTime\":\"2022-01-15 13:59:25\",\n    \"carNo\": \"粤BNS338\",\n    \"driverName\": \"吴雪雪\",\n    \"driverMobile\": \"13715396139\",\n    \"driverCard\": \"******************\",\n    \"vehicleType\": \"5.6m\",\n    \"vehicleLength\": \"4.562\",\n    \"vehicleWide\": \"2.354\",\n    \"vehicleHeight\":\"3.584\",\n   \"vehicleLoad\": \"2304\",\n    \"vehicleLoadingRate\": \"15.42%\",\n    \"vehicleVolume\": \"125\",\n    \"vehicleVolumeRate\":\"84.34%\"\n   \n}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "装载信息新增", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "/tms/carrier_data/producer/carrier_waybill_add", "host": ["tms"], "query": [], "variable": [], "path": ["carrier_data", "producer", "carrier_waybill_add"]}, "header": [{"key": "carrier", "value": "GL", "type": "text"}, {"key": "sign<PERSON>ethod", "value": "SHA-256", "type": "text"}, {"key": "signature", "value": "{{sha256}}", "type": "text"}], "body": {"mode": "raw", "raw": "{ //此接口一个装载单只允许请求一次，必须要将该装载单下面的所有订单都收集完成之后再请求该接口\n  \"carrierCode\":\"GL\", //承运商编码--传约定的承运商编码\n    \"waybill\": \"DO20220521273896\",  //装载单号\n    \"planLoadTime\":\"2022-05-11 10:24:59\",\n    \"carNo\": \"皖A8926U\",\n    \"driverName\": \"金传余\",\n    \"driverMobile\": \"13956944645\",\n    \"driverCard\": \"\",\n    \"vehicleType\": \"5.6m\",\n    \"vehicleLength\": \"4.562\",\n    \"vehicleWide\": \"2.354\",\n    \"vehicleHeight\":\"3.584\",\n   \"vehicleLoad\": \"2304\",\n    \"vehicleLoadingRate\": \"15.42%\",\n    \"vehicleVolume\": \"125\",\n    \"vehicleVolumeRate\":\"84.34%\",\n    \"orderList\": [\n        {\n            \"tmsOrder\": \"TMS2205210000115\", //TMS单号\n             \"transportCode\":\"SO202205211061314\", //运单号\n             \"codeSorting\": 1,\n            \"items\": [\n                {\n                    \"productCode\": \"KB358N07800\", //SP1018569001\n                    \"num\": 1\n                },\n                {\n                    \"productCode\": \"KB276W03200\",\n                    \"num\": 1\n                }\n            ]\n        },\n         {\n            \"tmsOrder\": \"TMS2205210000114\", //TMS单号\n             \"transportCode\":\"SO202205211061309\", //运单号\n             \"codeSorting\": 2,\n            \"items\": [\n                {\n                    \"productCode\": \"KB559W00800\", //SP1018569001\n                    \"num\": 4\n                },\n                {\n                    \"productCode\": \"KB559N00800\",\n                    \"num\": 4\n                }\n            ]\n        },\n         {\n            \"tmsOrder\": \"TMS2205210000113\", //TMS单号\n             \"transportCode\":\"SO202205211061310\", //运单号\n             \"codeSorting\": 3,\n            \"items\": [\n                {\n                    \"productCode\": \"KB559W00400\", //SP1018569001\n                    \"num\": 2\n                },\n                {\n                    \"productCode\": \"KB559N00400\",\n                    \"num\": 2\n                }\n            ]\n        },\n        {\n            \"tmsOrder\": \"TMS2205210000105\", //TMS单号\n             \"transportCode\":\"SO202205211061307\", //运单号\n             \"codeSorting\": 4,\n            \"items\": [\n                {\n                    \"productCode\": \"KB559W41400\", //SP1018569001\n                    \"num\": 1\n                },\n                {\n                    \"productCode\": \"KB559N41400\",\n                    \"num\": 1\n                }\n            ]\n        },\n        {\n            \"tmsOrder\": \"TMS2205210000096\", //TMS单号\n             \"transportCode\":\"SO202205211061315\", //运单号\n             \"codeSorting\": 5,\n            \"items\": [\n                {\n                    \"productCode\": \"KB559W00800\", //SP1018569001\n                    \"num\": 2\n                },\n                {\n                    \"productCode\": \"KB559N00800\",\n                    \"num\": 2\n                }\n            ]\n        },\n        {\n            \"tmsOrder\": \"TMS2205210000082\", //TMS单号\n             \"transportCode\":\"SO202205211061313\", //运单号\n             \"codeSorting\": 6,\n            \"items\": [\n                {\n                    \"productCode\": \"KB559W00800\", //SP1018569001\n                    \"num\": 6\n                },\n                {\n                    \"productCode\": \"KB559N00800\",\n                    \"num\": 6\n                }\n            ]\n        },\n        {\n            \"tmsOrder\": \"TMS2205210000081\", //TMS单号\n             \"transportCode\":\"SO202205211061312\", //运单号\n             \"codeSorting\": 7,\n            \"items\": [\n                {\n                    \"productCode\": \"KB559W00400\", //SP1018569001\n                    \"num\": 4\n                },\n                {\n                    \"productCode\": \"KB559N00400\",\n                    \"num\": 4\n                }\n            ]\n        },\n        {\n            \"tmsOrder\": \"TMS2205200000123\", //TMS单号\n             \"transportCode\":\"SO202205211061308\", //运单号\n             \"codeSorting\": 8,\n            \"items\": [\n                {\n                    \"productCode\": \"KH211W01500\", //SP1018569001\n                    \"num\": 1\n                },\n                {\n                    \"productCode\": \"KH252N00300\",\n                    \"num\": 1\n                }\n            ]\n        }\n    ]\n}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "装载顺序修改", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "/tms/carrier_data/producer/carrier_waybill_modify", "host": ["tms"], "query": [], "variable": [], "path": ["carrier_data", "producer", "carrier_waybill_modify"]}, "header": [{"key": "carrier", "value": "GL", "type": "text"}, {"key": "sign<PERSON>ethod", "value": "SHA-256", "type": "text"}, {"key": "signature", "value": "{{sha256}}", "type": "text"}], "body": {"mode": "raw", "raw": "{ //全量更新，不允许删除装载单下面的订单，只能修改装载顺序，修改的时候必须要将该装载单下面的所有订单全部传过来\n  \"carrierCode\":\"GL\",\n    \"waybill\": \"RJ22011707019\", //装载单号\n    \"orderList\": [\n        {\n            \"tmsOrder\": \"TMS202111170002141\", //TMS单号\n             \"transportCode\":\"RJ22011707019\",  //运单号\n             \"codeSorting\":1\n        },\n        //  {\n        //     \"tmsOrder\": \"TMS202111170002142\", //TMS单号\n        //      \"transportCode\":\"YD23423424\",  //运单号\n        //      \"codeSorting\": 2\n        // }\n    ]\n}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}, {"name": "对象签名", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["apt.variables.set(\"sha256\", response.json.data[\"sha256\"]);"]}}], "request": {"method": "POST", "url": {"raw": "/tms/carrier_data/producer/carrier_object", "host": ["tms"], "query": [], "variable": [], "path": ["carrier_data", "producer", "carrier_object"]}, "header": [], "body": {"mode": "raw", "raw": "{ //此接口一个装载单只允许请求一次，必须要将该装载单下面的所有订单都收集完成之后再请求该接口\n  \"carrierCode\":\"GL\", //承运商编码--传约定的承运商编码\n    \"waybill\": \"DO20220509270334\",  //装载单号\n    \"planLoadTime\":\"2022-05-07 17:24:00\",\n    \"carNo\": \"皖AS78A1\",\n    \"driverName\": \"高树宝\",\n    \"driverMobile\": \"15205657378\",\n    \"driverCard\": \"\",\n    \"vehicleType\": \"5.6m\",\n    \"vehicleLength\": \"4.562\",\n    \"vehicleWide\": \"2.354\",\n    \"vehicleHeight\":\"3.584\",\n   \"vehicleLoad\": \"2304\",\n    \"vehicleLoadingRate\": \"15.42%\",\n    \"vehicleVolume\": \"125\",\n    \"vehicleVolumeRate\":\"84.34%\",\n    \"orderList\": [\n        {\n            \"tmsOrder\": \"TMS2205070000266\", //TMS单号\n             \"transportCode\":\"SO202205091051932\", //运单号\n             \"codeSorting\": 1,\n            \"items\": [\n                {\n                    \"productCode\": \"KH223N00800\", //SP1018569001\n                    \"num\": 2\n                },\n                {\n                    \"productCode\": \"KH226W01100\",\n                    \"num\": 2\n                }\n            ]\n        },\n         {\n            \"tmsOrder\": \"TMS2205070000265\", //TMS单号\n             \"transportCode\":\"SO202205091051933\", //运单号\n             \"codeSorting\": 2,\n            \"items\": [\n                {\n                    \"productCode\": \"KB393N02000\", //SP1018569001\n                    \"num\": 1\n                },\n                {\n                    \"productCode\": \"KB393W02000\",\n                    \"num\": 1\n                }\n            ]\n        },\n         {\n            \"tmsOrder\": \"TMS2205070000191\", //TMS单号\n             \"transportCode\":\"SO202205091051930\", //运单号\n             \"codeSorting\": 3,\n            \"items\": [\n                {\n                    \"productCode\": \"KJ100N81800\", //SP1018569001\n                    \"num\": 2\n                },\n                {\n                    \"productCode\": \"KJ100W81800\",\n                    \"num\": 2\n                }\n            ]\n        },\n        {\n            \"tmsOrder\": \"TMS2205070000189\", //TMS单号\n             \"transportCode\":\"SO202205091051931\", //运单号\n             \"codeSorting\": 4,\n            \"items\": [\n                {\n                    \"productCode\": \"KH171N02700\", //SP1018569001\n                    \"num\": 4\n                },\n                {\n                    \"productCode\": \"KH224W01300\",\n                    \"num\": 4\n                }\n            ]\n        },\n        {\n            \"tmsOrder\": \"TMS2205070000188\", //TMS单号\n             \"transportCode\":\"SO202205091051929\", //运单号\n             \"codeSorting\": 5,\n            \"items\": [\n                {\n                    \"productCode\": \"KB541N40800\", //SP1018569001\n                    \"num\": 6\n                },\n                {\n                    \"productCode\": \"KB541W40800\",\n                    \"num\": 6\n                }\n            ]\n        }\n    ]\n}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}, {"name": "oms->tms并下单承运商", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "item": [{"name": "指定承运商", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "request": {"method": "POST", "url": {"raw": "/tms/business/order/assign", "host": ["tms"], "query": [], "variable": [], "path": ["business", "order", "assign"]}, "header": [], "body": {"mode": "raw", "raw": "{\n    \"uid\":\"879\",\n    \"carrierCode\":\"ZYLD\",\n    \"transportTypeCode\":\"LD\",\n    \"carrierName\":\"中原零担\",\n    \"transportTypeName\":\"零担\",\n    \"tmsOrderCodeList\":[\"{{tmsbill}}\"]\n}"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "apikey": [{"key": "value", "value": "", "type": "string"}, {"key": "key", "value": "", "type": "string"}], "bearer": [{"key": "token", "value": "", "type": "string"}], "basic": [{"key": "username", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}]}, "description": ""}}]}]}]}]}