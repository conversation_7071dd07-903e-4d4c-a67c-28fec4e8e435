{"scenes": [{"steps": [{"id": 24, "title": "HTTP接口", "content": {"id": 2, "name": "修改车辆", "host": {}, "url": "/prod-api/dbu_tms/api/supplier/vehicle/update", "method": "POST", "headers": {}, "request": {"json": {"licensePlateNo": "TEST", "vehicleAttributes": "1", "vehicleStatus": "1", "loadingCapacity": 5, "loadingWeight": 20, "mainDriver": "", "mainDriverTelephone": "", "coPilotDriver": "", "coPilotDriverTelephone": "", "emissionStandard": "", "energyType": "", "modelId": 6551, "modelName": "ZBCX", "id": 10094}, "data": null, "params": {}}, "file": [], "setup_script": "# 前置脚本(python):\n# global_tools:全局工具函数\n# data:用例数据 \n# env: 局部环境\n# ENV: 全局环境\n# db: 数据库操作对象", "teardown_script": "# 后置脚本(python):\n# global_tools:全局工具函数\n# data:用例数据 \n# response:响应对象response \n# env: 局部环境\n# ENV: 全局环境\n# db: 数据库操作对象", "interface_tag": {"tag": []}, "creator": "admin", "create_time": "2025-07-10T15:21:42.194812+08:00", "modifier": "admin", "update_time": "2025-07-10T15:21:58.398000+08:00", "desc": "", "type": "api", "YApi_id": null, "YApi_status": 0, "status": "状态", "project": 1, "treenode": 1}, "type": "api", "script": "", "status": true, "weight": 1, "children": []}], "name": "测试", "env": 1, "weight": 1}], "presetting": {"id": 3, "serverNames": ["测试"], "creator": "admin", "create_time": "2025-07-09T16:20:40.534624+08:00", "modifier": "admin", "update_time": "2025-07-28T14:22:08.631217+08:00", "name": "压测性能列表", "rule": "* * * * *", "taskType": "10", "logMode": "0", "pressureMode": "10", "timeUnit": "s", "control": "20", "resource": "10", "pressureConfig": {"lastLong": "60", "concurrencyNumber": "20", "concurrencyStep": "10"}, "isSetting": true, "thinkTime": [0], "thinkTimeType": "10", "project": 1, "task": 2, "serverArray": [1]}, "env": 1, "report_id": 220, "web_port": 8090}