# -*- coding: utf-8 -*-
# @author: HRUN

import os
import sys
import time
import json
import logging
import threading
import subprocess
import requests
import paramiko
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

from performanceengine.main import get_host_ip

# 配置日志
logger = logging.getLogger(__name__)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler = logging.StreamHandler()
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)


class DistributedTestManager:
    """分布式测试管理器"""
    
    def __init__(self, task_id: int = 0):
        """初始化分布式测试管理器"""
        self.task_id = task_id
        self.master_process = None
        self.worker_processes = {}
        self.ssh_clients = {}
        self.master_server = None
        self.worker_servers = []
        self.test_running = False
        self.results = {}
        self.web_port = 8089  # 默认端口，可以通过配置修改
        
        # 获取任务信息
        if task_id:
            self._get_task_info()
            
    def setup_distributed_test(self) -> bool:
        """设置分布式测试环境"""
        try:
            # 获取任务信息
            task_info = self._get_task_info()
            if not task_info:
                logger.error(f"无法获取任务信息: {self.task_id}")
                return False
            
            self.master_server = task_info['master_server']
            self.worker_servers = task_info['worker_servers']
            
            if not self.master_server:
                logger.error("未设置主服务器")
                return False
            
            # 检查服务器连接
            if not self._check_server_connections():
                logger.error("服务器连接检查失败")
                return False
            
            # 分发测试文件
            if not self._distribute_test_files():
                logger.error("测试文件分发失败")
                return False
            
            # 启动主节点
            if not self._start_master_node():
                logger.error("主节点启动失败")
                return False
            
            # 启动工作节点
            if not self._start_worker_nodes():
                logger.error("工作节点启动失败")
                return False
            
            logger.info(f"分布式测试环境设置成功 - 任务ID: {self.task_id}")
            return True
            
        except Exception as e:
            logger.error(f"分布式测试环境设置失败: {e}")
            return False
    
    def _get_task_info(self) -> Optional[Dict]:
        """获取任务信息"""
        try:
            import os
            import django
            os.environ.setdefault("DJANGO_SETTINGS_MODULE", "primaryApp.settings.dev")
            django.setup()
            
            from performance.models import PerformanceTask
            
            task = PerformanceTask.objects.get(id=self.task_id)
            
            return {
                'master_server': task.master_server,
                'worker_servers': list(task.worker_servers.all()),
                'distributed_mode': task.distributed_mode,
                'worker_distribution': task.worker_distribution,
                'total_workers': task.total_workers
            }
            
        except Exception as e:
            logger.error(f"获取任务信息失败: {e}")
            return None
    
    def _check_server_connections(self) -> bool:
        """检查服务器连接"""
        try:
            # 检查主服务器连接
            if not self._test_ssh_connection(self.master_server):
                logger.error(f"主服务器连接失败: {self.master_server.host_ip}")
                return False
            
            # 检查工作服务器连接
            for worker in self.worker_servers:
                if not self._test_ssh_connection(worker):
                    logger.error(f"工作服务器连接失败: {worker.host_ip}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"服务器连接检查失败: {e}")
            return False
    
    def _test_ssh_connection(self, server) -> bool:
        """测试SSH连接"""
        try:
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh.connect(
                hostname=server.host_ip,
                port=server.host_port or 22,
                username=server.sys_user_name,
                password=server.sys_user_passwd,
                timeout=10
            )
            
            # 测试基本命令
            stdin, stdout, stderr = ssh.exec_command('echo "connection test"')
            result = stdout.read().decode().strip()
            
            if result == "connection test":
                self.ssh_clients[server.id] = ssh
                return True
            else:
                ssh.close()
                return False
                
        except Exception as e:
            logger.error(f"SSH连接测试失败 {server.host_ip}: {e}")
            return False
    
    def _distribute_test_files(self) -> bool:
        """分发测试文件"""
        try:
            # 获取测试文件内容
            test_script = self._generate_test_script()
            if not test_script:
                return False
            
            # 分发到所有服务器
            for server in [self.master_server] + self.worker_servers:
                if not self._upload_test_file(server, test_script):
                    logger.error(f"文件分发失败: {server.host_ip}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"测试文件分发失败: {e}")
            return False
    
    def _generate_test_script(self) -> Optional[str]:
        """生成测试脚本"""
        try:
            # 从params.py获取测试数据
            from performanceengine.params import data
            
            if not data:
                logger.error("无法获取测试数据")
                return None
            
            # 生成Locust测试脚本
            script_content = f"""
import json
from locust import HttpUser, task, between, events
from locust.env import Environment
from locust.stats import stats_printer, stats_history
from locust.log import setup_logging
import time
import requests
import logging

# 测试数据
TEST_DATA = {json.dumps(data, ensure_ascii=False)}

class PerformanceUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        self.scenes = TEST_DATA.get('scenes', [])
        self.presetting = TEST_DATA.get('presetting', {{}})
        
    @task
    def execute_test_scenarios(self):
        for scene in self.scenes:
            self._execute_scene(scene)
    
    def _execute_scene(self, scene):
        steps = scene.get('steps', [])
        for step in steps:
            self._execute_step(step)
    
    def _execute_step(self, step):
        if step.get('type') == 'api':
            self._execute_api_step(step)
        elif step.get('type') == 'wait':
            time.sleep(step.get('waitTime', 1))
    
    def _execute_api_step(self, step):
        try:
            method = step.get('method', 'GET').upper()
            url = step.get('url', '')
            headers = step.get('headers', {{}})
            data = step.get('data', {{}})
            
            if method == 'GET':
                response = self.client.get(url, headers=headers, params=data)
            elif method == 'POST':
                response = self.client.post(url, headers=headers, json=data)
            elif method == 'PUT':
                response = self.client.put(url, headers=headers, json=data)
            elif method == 'DELETE':
                response = self.client.delete(url, headers=headers)
                
        except Exception as e:
            logging.error(f"API请求失败: {{e}}")

# 事件监听器
@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    logging.info("分布式测试开始")

@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    logging.info("分布式测试结束")

@events.request_success.add_listener
def on_request_success(request_type, name, response_time, response_length, **kwargs):
    pass

@events.request_failure.add_listener
def on_request_failure(request_type, name, response_time, response_length, exception, **kwargs):
    logging.error(f"请求失败: {{name}} - {{exception}}")
"""
            
            return script_content
            
        except Exception as e:
            logger.error(f"生成测试脚本失败: {e}")
            return None
    
    def _upload_test_file(self, server, script_content: str) -> bool:
        """上传测试文件"""
        try:
            ssh = self.ssh_clients.get(server.id)
            if not ssh:
                return False
            
            # 创建测试目录
            stdin, stdout, stderr = ssh.exec_command('mkdir -p /tmp/locust_test')
            
            # 上传测试脚本
            sftp = ssh.open_sftp()
            with sftp.open('/tmp/locust_test/locustfile.py', 'w') as f:
                f.write(script_content)
            sftp.close()
            
            return True
            
        except Exception as e:
            logger.error(f"上传测试文件失败 {server.host_ip}: {e}")
            return False
    
    def _start_master_node(self) -> bool:
        """启动主节点"""
        try:
            ssh = self.ssh_clients.get(self.master_server.id)
            if not ssh:
                return False
            
            # 停止可能存在的进程
            ssh.exec_command('pkill -f "locust.*master"')
            time.sleep(2)
            
            # 启动主节点
            master_port = self.master_server.locust_master_port or 5557
            web_port = self.web_port
            
            cmd = f"""
            cd /tmp/locust_test && 
            nohup locust -f locustfile.py --master --master-bind-port={master_port} --web-port={web_port} > master.log 2>&1 &
            """
            
            stdin, stdout, stderr = ssh.exec_command(cmd)
            
            # 等待主节点启动
            time.sleep(5)
            
            # 检查进程是否运行
            stdin, stdout, stderr = ssh.exec_command('pgrep -f "locust.*master"')
            pid = stdout.read().decode().strip()
            
            if pid:
                logger.info(f"主节点启动成功: PID {pid}")
                return True
            else:
                logger.error("主节点启动失败")
                return False
                
        except Exception as e:
            logger.error(f"启动主节点失败: {e}")
            return False
    
    def _start_worker_nodes(self) -> bool:
        """启动工作节点"""
        try:
            master_host = self.master_server.host_ip
            master_port = self.master_server.locust_master_port or 5557
            
            # 并发启动所有工作节点
            with ThreadPoolExecutor(max_workers=len(self.worker_servers)) as executor:
                futures = []
                
                for worker in self.worker_servers:
                    future = executor.submit(self._start_single_worker, worker, master_host, master_port)
                    futures.append(future)
                
                # 等待所有工作节点启动
                results = []
                for future in as_completed(futures):
                    results.append(future.result())
                
                # 检查是否所有工作节点都启动成功
                if all(results):
                    logger.info("所有工作节点启动成功")
                    return True
                else:
                    logger.error("部分工作节点启动失败")
                    return False
                    
        except Exception as e:
            logger.error(f"启动工作节点失败: {e}")
            return False
    
    def _start_single_worker(self, worker, master_host: str, master_port: int) -> bool:
        """启动单个工作节点"""
        try:
            ssh = self.ssh_clients.get(worker.id)
            if not ssh:
                return False
            
            # 停止可能存在的进程
            ssh.exec_command('pkill -f "locust.*worker"')
            time.sleep(2)
            
            # 启动工作节点
            cmd = f"""
            cd /tmp/locust_test && 
            nohup locust -f locustfile.py --worker --master-host={master_host} --master-port={master_port} > worker.log 2>&1 &
            """
            
            stdin, stdout, stderr = ssh.exec_command(cmd)
            
            # 等待工作节点启动
            time.sleep(3)
            
            # 检查进程是否运行
            stdin, stdout, stderr = ssh.exec_command('pgrep -f "locust.*worker"')
            pid = stdout.read().decode().strip()
            
            if pid:
                logger.info(f"工作节点启动成功 {worker.host_ip}: PID {pid}")
                return True
            else:
                logger.error(f"工作节点启动失败: {worker.host_ip}")
                return False
                
        except Exception as e:
            logger.error(f"启动工作节点失败 {worker.host_ip}: {e}")
            return False
    
    def start_distributed_test(self, task_id: int, master_server, worker_servers: List, env_id: int, test_config: Dict) -> Dict:
        """启动分布式测试"""
        try:
            # 存储任务信息
            self.task_id = task_id
            self.master_server = master_server
            self.worker_servers = worker_servers
            
            # 检查服务器连接
            if not self._check_server_connections():
                return {'success': False, 'error': '服务器连接检查失败'}
            
            # 获取Web端口配置
            self.web_port = test_config.get('web_port', 8089)
            
            # 获取当前机器IP，用于生成访问URL
            local_ip = get_host_ip()
            
            # 分发测试文件
            if not self._distribute_test_files():
                return {'success': False, 'error': '测试文件分发失败'}
            
            # 启动主节点
            if not self._start_master_node():
                return {'success': False, 'error': '主节点启动失败'}
            
            # 启动工作节点
            if not self._start_worker_nodes():
                return {'success': False, 'error': '工作节点启动失败'}
            
            self.test_running = True
            
            # 准备测试数据
            # 导入run_task而不是提前导入，防止循环导入
            from performanceengine.main import run_task
            
            # 执行任务准备
            executor = test_config.get('executor', 'system')
            test_data = run_task(task_id, env_id, executor, self.web_port)
            
            # 返回成功信息
            return {
                'success': True,
                'message': '分布式测试环境启动成功',
                'data': {
                    'master_server': {
                        'id': master_server.id,
                        'name': master_server.name,
                        'host_ip': master_server.host_ip
                    },
                    'worker_count': len(worker_servers),
                    'gui_url': f'http://{master_server.host_ip}:{self.web_port}',
                    'local_gui_url': f'http://{local_ip}:{self.web_port}',
                    'web_port': self.web_port
                }
            }
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            return {'success': False, 'error': str(e)}
    
    def start_test_with_params(self, users: int, spawn_rate: int, duration: int = None) -> bool:
        """使用参数启动测试"""
        try:
            # 更新任务状态为"执行中"
            try:
                from performance.models import PerformanceTask
                task = PerformanceTask.objects.get(id=self.task_id)
                task.status = '1'  # 执行中
                task.save()
                logger.info(f"已更新任务状态为'执行中': {self.task_id}")
            except Exception as e:
                logger.error(f"更新任务状态失败: {e}")
            
            # 通过主节点的Web API启动测试
            master_url = f"http://{self.master_server.host_ip}:{self.web_port}"
            
            # 启动测试
            start_data = {
                'user_count': users,
                'spawn_rate': spawn_rate
            }
            
            response = requests.post(f"{master_url}/swarm", json=start_data)
            
            if response.status_code == 200:
                self.test_running = True
                logger.info(f"分布式测试启动成功 - 用户数: {users}, 启动速率: {spawn_rate}")
                
                # 如果设置了持续时间，启动定时器
                if duration:
                    threading.Timer(duration, self.stop_distributed_test).start()
                
                return True
            else:
                logger.error(f"分布式测试启动失败: {response.text}")
                # 更新任务状态为"执行失败"
                try:
                    from performance.models import PerformanceTask
                    task = PerformanceTask.objects.get(id=self.task_id)
                    task.status = '99'  # 执行失败
                    task.save()
                    logger.info(f"已更新任务状态为'执行失败': {self.task_id}")
                except Exception as e:
                    logger.error(f"更新任务状态失败: {e}")
                return False
                
        except Exception as e:
            logger.error(f"启动分布式测试失败: {e}")
            # 更新任务状态为"执行失败"
            try:
                from performance.models import PerformanceTask
                task = PerformanceTask.objects.get(id=self.task_id)
                task.status = '99'  # 执行失败
                task.save()
                logger.info(f"已更新任务状态为'执行失败': {self.task_id}")
            except Exception as ex:
                logger.error(f"更新任务状态失败: {ex}")
            return False
    
    def stop_distributed_test(self) -> bool:
        """停止分布式测试"""
        try:
            if not self.test_running:
                return True
            
            # 通过主节点的Web API停止测试
            master_url = f"http://{self.master_server.host_ip}:{self.web_port}"
            
            response = requests.post(f"{master_url}/stop")
            
            if response.status_code == 200:
                self.test_running = False
                logger.info("分布式测试停止成功")
                
                # 收集测试结果
                self._collect_test_results()
                
                # 更新任务状态为"执行完成"
                try:
                    from performance.models import PerformanceTask
                    task = PerformanceTask.objects.get(id=self.task_id)
                    task.status = '0'  # 执行完成
                    task.save()
                    logger.info(f"已更新任务状态为'执行完成': {self.task_id}")
                except Exception as e:
                    logger.error(f"更新任务状态失败: {e}")
                
                return True
            else:
                logger.error(f"分布式测试停止失败: {response.text}")
                
                # 更新任务状态为"执行失败"
                try:
                    from performance.models import PerformanceTask
                    task = PerformanceTask.objects.get(id=self.task_id)
                    task.status = '99'  # 执行失败
                    task.save()
                    logger.info(f"已更新任务状态为'执行失败': {self.task_id}")
                except Exception as e:
                    logger.error(f"更新任务状态失败: {e}")
                
                return False
                
        except Exception as e:
            logger.error(f"停止分布式测试失败: {e}")
            
            # 更新任务状态为"执行失败"
            try:
                from performance.models import PerformanceTask
                task = PerformanceTask.objects.get(id=self.task_id)
                task.status = '99'  # 执行失败
                task.save()
                logger.info(f"已更新任务状态为'执行失败': {self.task_id}")
            except Exception as ex:
                logger.error(f"更新任务状态失败: {ex}")
            
            return False
    
    def _collect_test_results(self):
        """收集测试结果"""
        try:
            # 通过主节点的Web API获取测试结果
            master_url = f"http://{self.master_server.host_ip}:{self.web_port}"
            
            response = requests.get(f"{master_url}/stats/requests")
            
            if response.status_code == 200:
                self.test_results = response.json()
                logger.info("测试结果收集成功")
            else:
                logger.error(f"测试结果收集失败: {response.text}")
            
        except Exception as e:
            logger.error(f"收集测试结果失败: {e}")
    
    def cleanup_distributed_test(self):
        """清理分布式测试环境"""
        try:
            # 停止所有进程
            for server in [self.master_server] + self.worker_servers:
                ssh = self.ssh_clients.get(server.id)
                if ssh:
                    ssh.exec_command('pkill -f "locust"')
                    ssh.exec_command('rm -rf /tmp/locust_test')
            
            # 关闭SSH连接
            for ssh in self.ssh_clients.values():
                ssh.close()
            
            self.ssh_clients.clear()
            logger.info("分布式测试环境清理完成")
            
        except Exception as e:
            logger.error(f"清理分布式测试环境失败: {e}")
    
    def get_test_status(self) -> Dict:
        """获取测试状态"""
        try:
            if not self.test_running:
                return {
                    'status': 'stopped',
                    'message': '测试未运行',
                    'results': self.test_results
                }
            
            # 通过主节点的Web API获取测试状态
            master_url = f"http://{self.master_server.host_ip}:{self.web_port}"
            
            try:
                response = requests.get(f"{master_url}/stats/requests")
                
                if response.status_code == 200:
                    stats = response.json()
                    return {
                        'status': 'running',
                        'message': '测试运行中',
                        'stats': stats
                    }
                else:
                    return {
                        'status': 'error',
                        'message': f'获取测试状态失败: {response.text}'
                    }
            except Exception as e:
                return {
                    'status': 'error',
                    'message': f'获取测试状态失败: {str(e)}'
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'message': f'获取测试状态失败: {str(e)}'
            }


# 全局分布式测试管理器实例
_distributed_manager_instances = {}


def get_distributed_manager(task_id: int = None) -> DistributedTestManager:
    """获取分布式测试管理器单例"""
    global _distributed_manager_instances
    
    if task_id is None:
        # 如果没有提供task_id，创建一个新的实例但不缓存
        return DistributedTestManager(0)  # 使用0作为临时ID
        
    if task_id not in _distributed_manager_instances:
        _distributed_manager_instances[task_id] = DistributedTestManager(task_id)
    
    return _distributed_manager_instances[task_id]


def cleanup_distributed_manager(task_id: int):
    """清理分布式测试管理器"""
    if task_id in _distributed_manager_instances:
        _distributed_manager_instances[task_id].cleanup_distributed_test()
        del _distributed_manager_instances[task_id]