# -*- coding: utf-8 -*-
# @author: HRUN

import os
import subprocess
from datetime import datetime
from celery import shared_task

from performance.models import PerformanceTask
from performance.serializers import PerformanceTaskRunSerializer
from performanceengine.params import save_data


def get_host_ip():
    """优先获取内网IP（10/172.16-31/192.168），其次获取公网IP，最后localhost"""
    import socket
    import requests
    import re

    # 1. 优先获取内网IP
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('*******', 80))
        ip = s.getsockname()[0]
        s.close()
        # 判断是否为内网IP
        if (
            ip.startswith('10.') or
            ip.startswith('192.168.') or
            re.match(r'^172\\.(1[6-9]|2[0-9]|3[0-1])\\.', ip)
        ):
            return ip
    except Exception as e:
        print(f"通过socket获取内网IP失败: {e}")

    # 2. 获取公网IP
    try:
        response = requests.get('https://ipinfo.io/ip', timeout=3)
        if response.status_code == 200:
            ip = response.text.strip()
            if ip and not ip.startswith('127.'):
                return ip
    except Exception as e:
        print(f"通过外部服务获取公网IP失败: {e}")

    # 3. 兜底
    return 'localhost'

@shared_task
def start_performance_test_async(task_id, env_id, test_config, executor='system'):
    """异步启动性能测试"""
    try:
        # 获取Web端口配置
        web_port = test_config.get('web_port', 8089)
        
        # 获取本机IP
        host_ip = get_host_ip()
        
        # 检查并清理端口
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex((host_ip, web_port))
            sock.close()
            
            if result == 0:  # 端口被占用
                kill_process_using_port(web_port)
        except Exception as e:
            print(f"端口检查失败: {e}")
        
        # 准备测试数据
        test_data = run_task(task_id, env_id, executor, web_port)
        
        # 异步启动Locust进程
        process = subprocess.Popen([
            'locust', '-f', 'performanceengine/taskGenerate.py', 
            '--headless', '--web-host', '0.0.0.0', 
            '--web-port', str(web_port), '--autostart'
        ])
        
        return {
            'success': True,
            'message': '性能测试启动成功',
            'process_pid': process.pid,
            'web_port': web_port,
            'gui_url': f'http://{host_ip}:{web_port}',
            'test_data': test_data
        }
        
    except Exception as e:
        return {
            'success': False,
            'message': f'性能测试启动失败: {str(e)}',
            'error': str(e)
        }


@shared_task
def run_task(task_id, env_id, executor='system', web_port=8089):
    # 获取执行任务的数据
    task = PerformanceTask.objects.get(pk=task_id)
    
    # 更新任务状态为"执行中"
    task.status = '1'  # 执行中
    task.save()
    
    task_data = PerformanceTaskRunSerializer(task).data
    presetting = task_data['presetting']
    scene_list = []
    
    # 导入结果收集模块
    try:
        from performanceengine.taskResult import create_and_monitor_report
        from performanceengine.systemMonitor import start_system_monitoring
        
        # 创建测试报告 - 使用传入的执行人信息
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        report_name = f"【{task.taskName + '性能报告'}】{timestamp}"
        
        report = create_and_monitor_report(task_id, env_id, report_name, executor)
        
        # 启动系统资源监控
        start_system_monitoring(report.id, task_id)
        print(f"已启动系统资源监控")
        
    except Exception as e:
        print(f"创建报告或启动监控失败: {e}")
        # 如果创建报告失败，更新任务状态为"执行失败"
        task.status = '99'  # 执行失败
        task.save()
        report = None
    
    for scene in task_data['taskScence']:
        steps = scene['taskscencestep_set']
        # 排序
        steps.sort(key=lambda x: x['sort'])

        def process_item(item):
            processed_item = {**item['stepInfo'], **{'children': []}}
            if 'children' in item:
                processed_item['children'] = [process_item(child) for child in item['children']]
            return processed_item

        scene_list.append({'steps': [process_item(item) for item in steps], 'name': scene['name'], 'env': scene['env'][0] if scene['env'] else None, 'weight': scene['weight']})

    data = {'scenes': scene_list, 'presetting': {**presetting},'env': env_id, 'report_id': report.id if report else None, 'web_port': web_port}

    # 将 data 存储到 params.py 文件
    save_data(data)

    return data


def kill_process_using_port(port):
    """杀掉占用指定端口的进程"""
    import platform
    
    try:
        if platform.system() == "Darwin":  # macOS
            # 在macOS上查找并杀掉占用端口的进程
            result = subprocess.run(f"lsof -ti :{port}", shell=True, capture_output=True, text=True)
            if result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    if pid.strip():
                        try:
                            subprocess.run(f"kill -9 {pid.strip()}", shell=True, check=True)
                            print(f"Killed process with PID {pid.strip()} on port {port}")
                        except subprocess.CalledProcessError as e:
                            print(f"Failed to kill process {pid.strip()}: {e}")
            else:
                print(f"No process found using port {port}")
                
        elif platform.system() == "Linux":
            # Linux系统
            result = subprocess.run(f"fuser -k {port}/tcp", shell=True, capture_output=True, text=True)
            print(f"Killed processes on port {port}")
            
        else:  # Windows
            # Windows系统 (原来的逻辑)
            result = subprocess.run(f"netstat -ano | findstr :{port}", shell=True, capture_output=True, text=True)
            lines = result.stdout.strip().split('\n')
            
            if not lines or lines[0] == '':
                print(f"No process found using port {port}.")
                return
                
            for line in lines:
                parts = line.split()
                if len(parts) >= 5:
                    pid = parts[-1]
                    try:
                        os.kill(int(pid), 9)
                        print(f"Terminated process with PID {pid} occupying port {port}.")
                    except Exception as e:
                        print(f"Error terminating process: {e}")
                        
    except Exception as e:
        print(f"Error killing process on port {port}: {e}")