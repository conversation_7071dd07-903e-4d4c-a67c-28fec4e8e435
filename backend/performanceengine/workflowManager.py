# -*- coding: utf-8 -*-
# @author: HRUN

import json
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import uuid


logger = logging.getLogger(__name__)


class WorkflowStatus(Enum):
    """工作流状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class StepStatus(Enum):
    """步骤状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class TriggerType(Enum):
    """触发器类型"""
    MANUAL = "manual"           # 手动触发
    SCHEDULED = "scheduled"     # 定时触发
    EVENT = "event"            # 事件触发
    WEBHOOK = "webhook"        # Webhook触发


class StepType(Enum):
    """步骤类型"""
    PERFORMANCE_TEST = "performance_test"
    ENVIRONMENT_SETUP = "environment_setup"
    DATA_PREPARATION = "data_preparation"
    NOTIFICATION = "notification"
    SCRIPT_EXECUTION = "script_execution"
    CONDITION_CHECK = "condition_check"
    APPROVAL = "approval"
    DELAY = "delay"


@dataclass
class WorkflowStep:
    """工作流步骤"""
    id: str
    name: str
    step_type: StepType
    description: str = ""
    config: Dict = field(default_factory=dict)
    depends_on: List[str] = field(default_factory=list)  # 依赖的步骤ID
    retry_count: int = 0
    timeout_minutes: int = 60
    continue_on_failure: bool = False
    status: StepStatus = StepStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None
    output: Dict = field(default_factory=dict)


@dataclass
class WorkflowTrigger:
    """工作流触发器"""
    trigger_type: TriggerType
    config: Dict = field(default_factory=dict)
    
    def should_trigger(self, context: Dict = None) -> bool:
        """判断是否应该触发"""
        if self.trigger_type == TriggerType.MANUAL:
            return context and context.get('manual_trigger', False)
        elif self.trigger_type == TriggerType.SCHEDULED:
            return self._check_schedule()
        elif self.trigger_type == TriggerType.EVENT:
            return self._check_event(context)
        elif self.trigger_type == TriggerType.WEBHOOK:
            return context and context.get('webhook_trigger', False)
        return False
    
    def _check_schedule(self) -> bool:
        """检查定时调度"""
        # 这里可以实现复杂的定时逻辑
        schedule_config = self.config.get('schedule', {})
        # 简化实现，实际可以使用cron表达式
        return True
    
    def _check_event(self, context: Dict) -> bool:
        """检查事件触发"""
        event_config = self.config.get('event', {})
        event_type = event_config.get('type')
        
        if context and event_type:
            return context.get('event_type') == event_type
        return False


@dataclass
class AutomatedWorkflow:
    """自动化工作流"""
    id: str
    name: str
    description: str
    project_id: int
    steps: List[WorkflowStep] = field(default_factory=list)
    triggers: List[WorkflowTrigger] = field(default_factory=list)
    variables: Dict = field(default_factory=dict)  # 工作流变量
    created_by: str = "system"
    created_time: datetime = field(default_factory=datetime.now)
    updated_time: Optional[datetime] = None
    is_active: bool = True
    status: WorkflowStatus = WorkflowStatus.PENDING
    current_step_id: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    execution_log: List[Dict] = field(default_factory=list)


@dataclass
class WorkflowExecution:
    """工作流执行记录"""
    id: str
    workflow_id: str
    workflow_name: str
    status: WorkflowStatus
    trigger_type: TriggerType
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_seconds: Optional[int] = None
    step_results: Dict[str, Dict] = field(default_factory=dict)
    error_message: Optional[str] = None
    triggered_by: str = "system"


class WorkflowStepExecutor:
    """工作流步骤执行器"""
    
    def __init__(self):
        self.step_handlers = {
            StepType.PERFORMANCE_TEST: self._execute_performance_test,
            StepType.ENVIRONMENT_SETUP: self._execute_environment_setup,
            StepType.DATA_PREPARATION: self._execute_data_preparation,
            StepType.NOTIFICATION: self._execute_notification,
            StepType.SCRIPT_EXECUTION: self._execute_script,
            StepType.CONDITION_CHECK: self._execute_condition_check,
            StepType.APPROVAL: self._execute_approval,
            StepType.DELAY: self._execute_delay
        }
    
    async def execute_step(self, step: WorkflowStep, context: Dict) -> bool:
        """执行工作流步骤"""
        try:
            step.status = StepStatus.RUNNING
            step.start_time = datetime.now()
            
            logger.info(f"开始执行步骤: {step.name} ({step.step_type.value})")
            
            handler = self.step_handlers.get(step.step_type)
            if not handler:
                raise ValueError(f"不支持的步骤类型: {step.step_type}")
            
            # 执行步骤
            success = await handler(step, context)
            
            step.end_time = datetime.now()
            step.status = StepStatus.COMPLETED if success else StepStatus.FAILED
            
            logger.info(f"步骤执行完成: {step.name}, 状态: {step.status.value}")
            return success
            
        except Exception as e:
            step.end_time = datetime.now()
            step.status = StepStatus.FAILED
            step.error_message = str(e)
            logger.error(f"步骤执行失败: {step.name}, 错误: {e}")
            return False
    
    async def _execute_performance_test(self, step: WorkflowStep, context: Dict) -> bool:
        """执行性能测试"""
        try:
            config = step.config
            task_id = config.get('task_id')
            environment_id = config.get('environment_id')
            
            if not task_id:
                raise ValueError("缺少任务ID配置")
            
            # 这里可以调用性能测试的API
            # 简化实现，实际需要集成真实的测试执行逻辑
            import os
            import django
            os.environ.setdefault("DJANGO_SETTINGS_MODULE", "primaryApp.settings.dev")
            django.setup()
            
            from performance.models import PerformanceTask
            
            task = PerformanceTask.objects.get(id=task_id)
            
            # 模拟执行性能测试
            await asyncio.sleep(2)  # 模拟测试执行时间
            
            # 记录输出
            step.output = {
                'task_id': task_id,
                'task_name': task.taskName,
                'status': 'completed',
                'execution_time': datetime.now().isoformat()
            }
            
            return True
            
        except Exception as e:
            step.error_message = f"性能测试执行失败: {e}"
            return False
    
    async def _execute_environment_setup(self, step: WorkflowStep, context: Dict) -> bool:
        """执行环境设置"""
        try:
            config = step.config
            environment_config = config.get('environment', {})
            
            # 模拟环境设置
            await asyncio.sleep(1)
            
            step.output = {
                'environment_setup': 'completed',
                'config': environment_config
            }
            
            return True
            
        except Exception as e:
            step.error_message = f"环境设置失败: {e}"
            return False
    
    async def _execute_data_preparation(self, step: WorkflowStep, context: Dict) -> bool:
        """执行数据准备"""
        try:
            config = step.config
            data_source = config.get('data_source')
            
            # 模拟数据准备
            await asyncio.sleep(1)
            
            step.output = {
                'data_preparation': 'completed',
                'data_source': data_source,
                'records_prepared': config.get('record_count', 100)
            }
            
            return True
            
        except Exception as e:
            step.error_message = f"数据准备失败: {e}"
            return False
    
    async def _execute_notification(self, step: WorkflowStep, context: Dict) -> bool:
        """执行通知"""
        try:
            config = step.config
            message = config.get('message', '工作流执行通知')
            recipients = config.get('recipients', [])
            
            # 模拟发送通知
            logger.info(f"发送通知: {message}, 接收者: {recipients}")
            
            step.output = {
                'notification_sent': True,
                'message': message,
                'recipients_count': len(recipients)
            }
            
            return True
            
        except Exception as e:
            step.error_message = f"通知发送失败: {e}"
            return False
    
    async def _execute_script(self, step: WorkflowStep, context: Dict) -> bool:
        """执行脚本"""
        try:
            config = step.config
            script_content = config.get('script')
            script_type = config.get('script_type', 'shell')
            
            if not script_content:
                raise ValueError("缺少脚本内容")
            
            # 模拟脚本执行
            await asyncio.sleep(1)
            
            step.output = {
                'script_executed': True,
                'script_type': script_type,
                'exit_code': 0
            }
            
            return True
            
        except Exception as e:
            step.error_message = f"脚本执行失败: {e}"
            return False
    
    async def _execute_condition_check(self, step: WorkflowStep, context: Dict) -> bool:
        """执行条件检查"""
        try:
            config = step.config
            condition = config.get('condition')
            
            # 简化的条件检查实现
            # 实际可以支持更复杂的条件表达式
            result = eval(condition) if condition else True
            
            step.output = {
                'condition_result': result,
                'condition': condition
            }
            
            return result
            
        except Exception as e:
            step.error_message = f"条件检查失败: {e}"
            return False
    
    async def _execute_approval(self, step: WorkflowStep, context: Dict) -> bool:
        """执行审批"""
        try:
            config = step.config
            approvers = config.get('approvers', [])
            
            # 模拟审批过程
            # 实际实现需要与审批系统集成
            step.output = {
                'approval_requested': True,
                'approvers': approvers,
                'status': 'pending_approval'
            }
            
            # 简化实现，自动通过
            return True
            
        except Exception as e:
            step.error_message = f"审批执行失败: {e}"
            return False
    
    async def _execute_delay(self, step: WorkflowStep, context: Dict) -> bool:
        """执行延迟"""
        try:
            config = step.config
            delay_seconds = config.get('delay_seconds', 30)
            
            await asyncio.sleep(delay_seconds)
            
            step.output = {
                'delay_completed': True,
                'delay_seconds': delay_seconds
            }
            
            return True
            
        except Exception as e:
            step.error_message = f"延迟执行失败: {e}"
            return False


class AutomatedWorkflowManager:
    """自动化工作流管理器"""
    
    def __init__(self):
        self.workflows: Dict[str, AutomatedWorkflow] = {}
        self.executions: List[WorkflowExecution] = []
        self.executor = WorkflowStepExecutor()
        self.running_workflows: Dict[str, asyncio.Task] = {}
    
    def create_workflow(self, name: str, description: str, project_id: int,
                       created_by: str = "system") -> str:
        """创建工作流"""
        try:
            workflow_id = str(uuid.uuid4())
            
            workflow = AutomatedWorkflow(
                id=workflow_id,
                name=name,
                description=description,
                project_id=project_id,
                created_by=created_by
            )
            
            self.workflows[workflow_id] = workflow
            
            logger.info(f"创建工作流成功: {name} (ID: {workflow_id})")
            return workflow_id
            
        except Exception as e:
            logger.error(f"创建工作流失败: {e}")
            raise
    
    def add_step(self, workflow_id: str, step: WorkflowStep) -> bool:
        """添加工作流步骤"""
        try:
            if workflow_id not in self.workflows:
                return False
            
            workflow = self.workflows[workflow_id]
            workflow.steps.append(step)
            workflow.updated_time = datetime.now()
            
            logger.info(f"添加步骤成功: {step.name} -> 工作流: {workflow.name}")
            return True
            
        except Exception as e:
            logger.error(f"添加步骤失败: {e}")
            return False
    
    def add_trigger(self, workflow_id: str, trigger: WorkflowTrigger) -> bool:
        """添加工作流触发器"""
        try:
            if workflow_id not in self.workflows:
                return False
            
            workflow = self.workflows[workflow_id]
            workflow.triggers.append(trigger)
            workflow.updated_time = datetime.now()
            
            logger.info(f"添加触发器成功: {trigger.trigger_type.value} -> 工作流: {workflow.name}")
            return True
            
        except Exception as e:
            logger.error(f"添加触发器失败: {e}")
            return False
    
    def get_workflow(self, workflow_id: str) -> Optional[AutomatedWorkflow]:
        """获取工作流"""
        return self.workflows.get(workflow_id)
    
    def list_workflows(self, project_id: Optional[int] = None,
                      active_only: bool = True) -> List[AutomatedWorkflow]:
        """列出工作流"""
        workflows = list(self.workflows.values())
        
        if project_id:
            workflows = [w for w in workflows if w.project_id == project_id]
        
        if active_only:
            workflows = [w for w in workflows if w.is_active]
        
        return sorted(workflows, key=lambda x: x.created_time, reverse=True)
    
    def update_workflow(self, workflow_id: str, **kwargs) -> bool:
        """更新工作流"""
        try:
            if workflow_id not in self.workflows:
                return False
            
            workflow = self.workflows[workflow_id]
            
            updatable_fields = ['name', 'description', 'is_active', 'variables']
            for field, value in kwargs.items():
                if field in updatable_fields:
                    setattr(workflow, field, value)
            
            workflow.updated_time = datetime.now()
            
            logger.info(f"更新工作流成功: {workflow_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新工作流失败: {e}")
            return False
    
    def delete_workflow(self, workflow_id: str) -> bool:
        """删除工作流"""
        try:
            if workflow_id in self.workflows:
                # 如果工作流正在执行，先停止
                if workflow_id in self.running_workflows:
                    self.stop_workflow(workflow_id)
                
                del self.workflows[workflow_id]
                logger.info(f"删除工作流成功: {workflow_id}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"删除工作流失败: {e}")
            return False
    
    async def execute_workflow(self, workflow_id: str, context: Dict = None,
                             triggered_by: str = "system") -> str:
        """执行工作流"""
        try:
            if workflow_id not in self.workflows:
                raise ValueError(f"工作流不存在: {workflow_id}")
            
            workflow = self.workflows[workflow_id]
            if not workflow.is_active:
                raise ValueError(f"工作流未激活: {workflow.name}")
            
            # 创建执行记录
            execution_id = str(uuid.uuid4())
            execution = WorkflowExecution(
                id=execution_id,
                workflow_id=workflow_id,
                workflow_name=workflow.name,
                status=WorkflowStatus.RUNNING,
                trigger_type=TriggerType.MANUAL,  # 简化实现
                start_time=datetime.now(),
                triggered_by=triggered_by
            )
            
            self.executions.append(execution)
            
            # 更新工作流状态
            workflow.status = WorkflowStatus.RUNNING
            workflow.start_time = datetime.now()
            
            # 创建执行任务
            task = asyncio.create_task(self._execute_workflow_async(workflow, execution, context or {}))
            self.running_workflows[workflow_id] = task
            
            logger.info(f"开始执行工作流: {workflow.name} (执行ID: {execution_id})")
            return execution_id
            
        except Exception as e:
            logger.error(f"执行工作流失败: {e}")
            raise
    
    async def _execute_workflow_async(self, workflow: AutomatedWorkflow, 
                                    execution: WorkflowExecution, context: Dict):
        """异步执行工作流"""
        try:
            # 构建步骤依赖图
            step_dependencies = self._build_dependency_graph(workflow.steps)
            executed_steps = set()
            
            while len(executed_steps) < len(workflow.steps):
                # 找到可以执行的步骤（依赖已完成）
                ready_steps = []
                for step in workflow.steps:
                    if (step.id not in executed_steps and 
                        all(dep_id in executed_steps for dep_id in step.depends_on)):
                        ready_steps.append(step)
                
                if not ready_steps:
                    # 如果没有可执行的步骤，可能是循环依赖
                    raise ValueError("工作流存在循环依赖或无法执行的步骤")
                
                # 并行执行准备就绪的步骤
                tasks = []
                for step in ready_steps:
                    task = asyncio.create_task(self.executor.execute_step(step, context))
                    tasks.append((step, task))
                
                # 等待所有步骤完成
                for step, task in tasks:
                    try:
                        success = await task
                        
                        # 记录步骤结果
                        execution.step_results[step.id] = {
                            'step_name': step.name,
                            'status': step.status.value,
                            'success': success,
                            'start_time': step.start_time.isoformat() if step.start_time else None,
                            'end_time': step.end_time.isoformat() if step.end_time else None,
                            'error_message': step.error_message,
                            'output': step.output
                        }
                        
                        if success:
                            executed_steps.add(step.id)
                        else:
                            if not step.continue_on_failure:
                                # 步骤失败且不允许继续，停止工作流
                                raise ValueError(f"步骤失败: {step.name}")
                            else:
                                # 步骤失败但允许继续
                                executed_steps.add(step.id)
                                
                    except Exception as e:
                        step.status = StepStatus.FAILED
                        step.error_message = str(e)
                        
                        if not step.continue_on_failure:
                            raise
            
            # 工作流执行完成
            workflow.status = WorkflowStatus.COMPLETED
            workflow.end_time = datetime.now()
            
            execution.status = WorkflowStatus.COMPLETED
            execution.end_time = datetime.now()
            execution.duration_seconds = int((execution.end_time - execution.start_time).total_seconds())
            
            logger.info(f"工作流执行完成: {workflow.name}")
            
        except Exception as e:
            # 工作流执行失败
            workflow.status = WorkflowStatus.FAILED
            workflow.end_time = datetime.now()
            
            execution.status = WorkflowStatus.FAILED
            execution.end_time = datetime.now()
            execution.error_message = str(e)
            execution.duration_seconds = int((execution.end_time - execution.start_time).total_seconds())
            
            logger.error(f"工作流执行失败: {workflow.name}, 错误: {e}")
            
        finally:
            # 清理运行状态
            if workflow.id in self.running_workflows:
                del self.running_workflows[workflow.id]
    
    def _build_dependency_graph(self, steps: List[WorkflowStep]) -> Dict[str, List[str]]:
        """构建步骤依赖图"""
        graph = {}
        for step in steps:
            graph[step.id] = step.depends_on.copy()
        return graph
    
    def stop_workflow(self, workflow_id: str) -> bool:
        """停止工作流执行"""
        try:
            if workflow_id in self.running_workflows:
                task = self.running_workflows[workflow_id]
                task.cancel()
                
                workflow = self.workflows.get(workflow_id)
                if workflow:
                    workflow.status = WorkflowStatus.CANCELLED
                    workflow.end_time = datetime.now()
                
                del self.running_workflows[workflow_id]
                
                logger.info(f"工作流执行已停止: {workflow_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"停止工作流失败: {e}")
            return False
    
    def get_execution_history(self, workflow_id: Optional[str] = None,
                            limit: int = 50) -> List[WorkflowExecution]:
        """获取执行历史"""
        executions = self.executions
        
        if workflow_id:
            executions = [e for e in executions if e.workflow_id == workflow_id]
        
        # 按开始时间排序，最新的在前面
        executions = sorted(executions, key=lambda x: x.start_time, reverse=True)
        
        return executions[:limit]
    
    def get_workflow_statistics(self, project_id: Optional[int] = None,
                              days: int = 30) -> Dict:
        """获取工作流统计信息"""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            
            # 过滤执行记录
            filtered_executions = [
                exec for exec in self.executions
                if exec.start_time >= start_time
            ]
            
            if project_id:
                workflow_ids = [w.id for w in self.workflows.values() if w.project_id == project_id]
                filtered_executions = [
                    exec for exec in filtered_executions
                    if exec.workflow_id in workflow_ids
                ]
            
            # 统计数据
            total_executions = len(filtered_executions)
            
            if total_executions == 0:
                return {
                    'total_executions': 0,
                    'success_rate': 0,
                    'avg_duration_seconds': 0,
                    'status_distribution': {},
                    'workflow_count': len(self.workflows)
                }
            
            # 状态分布
            status_counts = {}
            total_duration = 0
            success_count = 0
            
            for exec in filtered_executions:
                status = exec.status.value
                status_counts[status] = status_counts.get(status, 0) + 1
                
                if exec.duration_seconds:
                    total_duration += exec.duration_seconds
                
                if exec.status == WorkflowStatus.COMPLETED:
                    success_count += 1
            
            success_rate = (success_count / total_executions) * 100 if total_executions > 0 else 0
            avg_duration = total_duration / total_executions if total_executions > 0 else 0
            
            return {
                'total_executions': total_executions,
                'success_rate': round(success_rate, 2),
                'avg_duration_seconds': round(avg_duration, 2),
                'status_distribution': status_counts,
                'workflow_count': len([w for w in self.workflows.values() 
                                     if not project_id or w.project_id == project_id]),
                'active_workflows': len([w for w in self.workflows.values() 
                                       if w.is_active and (not project_id or w.project_id == project_id)])
            }
            
        except Exception as e:
            logger.error(f"获取工作流统计失败: {e}")
            return {}


# 全局工作流管理器实例
workflow_manager = AutomatedWorkflowManager()


def get_workflow_manager() -> AutomatedWorkflowManager:
    """获取工作流管理器实例"""
    return workflow_manager