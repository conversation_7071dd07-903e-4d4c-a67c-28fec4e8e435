# -*- coding: utf-8 -*-
# @author: HRUN

"""
ASGI config for primaryApp project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/howto/deployment/asgi/
"""

import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
import primaryApp.routing  # 替换成你的应用程序的路由配置文件

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'primaryApp.settings')

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack(
        URLRouter(
            primaryApp.routing.websocket_urlpatterns
        )
    ),
})