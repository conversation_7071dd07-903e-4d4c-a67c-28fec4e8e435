# -*- coding: utf-8 -*-
# @author: HRUN
from django.urls import re_path
from .consumers import SSHConsumer
from performance.consumers import PerformanceMonitorConsumer, PerformanceReportConsumer

websocket_urlpatterns = [
    re_path(r'^asset/terminal/$', SSHConsumer.as_asgi()),
    re_path(r'^ws/performance/monitor/(?P<task_id>\d+)/$', PerformanceMonitorConsumer.as_asgi()),
    re_path(r'^ws/performance/report/(?P<report_id>\d+)/$', PerformanceReportConsumer.as_asgi()),
    # 添加更多的 WebSocket 路由和对应的 Consumer
]