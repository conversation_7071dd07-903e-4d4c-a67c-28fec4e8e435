# -*- coding: utf-8 -*-
# @author: HRUN

# 开发配置模块
# 导入公共配置
from .base_settings import *

ASGI_APPLICATION = "primaryApp.asgi.application"
# 开发配置
# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}


# 跨域配置
# 运行所有域名跨域
CORS_ORIGIN_ALLOW_ALL = True
# 允许cookie跨域，如果前端携带了cookie，必须要配
CORS_ALLOW_CREDENTIALS = True


# celery配置 - 暂时禁用
# CELERY_TIMEZONE = TIME_ZONE
# CELERY_BROKER_URL = 'redis://localhost:6379/0'
# CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'
# CELERY_WORKER_HIJACK_ROOT_LOGGER = False

# 使用本地缓存替代Redis
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
    }
}

