version: "3.8"

services:
  redis:
    image: redis:alpine
    restart: always
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  mariadb:
    image: mariadb:10.5
    restart: always
    environment:
      MARIADB_ROOT_PASSWORD: pythonvip
      MARIADB_DATABASE: HRUN
      MARIADB_USER: hrun
      MARIADB_PASSWORD: pythonvip
    volumes:
      - mariadb_data:/var/lib/mysql
    ports:
      - "3307:3306"

  backend:
    depends_on:
      - redis
      - mariadb
    build: ./backend
    image: backend_django_image
    environment:
      ENV: production
    restart: always
    volumes:
      - app_logs:/app/logs
    ports:
      - "8000:8000"

  nginx:
    depends_on:
      - backend
    build: ./nginx
    image: backend_nginx_image
    ports:
      - "5001:80"
      - "5002:81"
    volumes:
      - nginx_logs:/var/log/nginx

volumes:
  mariadb_data:
  redis_data:
  app_logs:
  nginx_logs: