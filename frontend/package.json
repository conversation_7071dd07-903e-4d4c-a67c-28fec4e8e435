{"name": "frontend-web", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build --dest ../nginx/dist"}, "dependencies": {"@codemirror/basic-setup": "^0.20.0", "@codemirror/lang-html": "^6.0.0", "@codemirror/lang-javascript": "^6.0.0", "@codemirror/lang-json": "^6.0.0", "@codemirror/lang-python": "^6.0.0", "@codemirror/lang-xml": "^6.0.0", "@codemirror/lint": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/theme-one-dark": "^6.0.0", "@codemirror/view": "^6.0.0", "@element-plus/icons-vue": "^2.3.1", "@wcfdehao/code-editor": "^1.0.0", "ace-builds": "^1.32.0", "animate.css": "^4.1.1", "axios": "^0.27.2", "core-js": "^3.8.3", "date-fns": "^4.1.0", "echarts": "^5.3.2", "element-plus": "^2.9.8", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "nprogress": "^0.2.0", "sortablejs": "^1.15.0", "ssh2": "^1.15.0", "vue": "^3.5.13", "vue-animate-number": "^0.4.2", "vue-resizable": "^2.1.7", "vue-router": "^4.0.3", "vue3-ace-editor": "^2.2.2", "vuex": "^4.0.0", "xterm": "^5.3.0", "xterm-addon-attach": "^0.9.0", "xterm-addon-fit": "^0.8.0", "zdog": "^1.1.3"}, "devDependencies": {"@iconify/vue": "^4.1.1", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "sass": "^1.89.2", "sass-loader": "^16.0.5", "vue-cli-plugin-element-plus": "0.0.13", "webpack": "^5.100.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}