<template>
  <el-config-provider :locale="locale">
    <router-view />
  </el-config-provider>
</template>

<script>
import { ElConfigProvider } from 'element-plus'
// ✅ 新版中文包导入方式（Element Plus v2.x）
import zhCn from 'element-plus/es/locale/lang/zh-cn'

export default {
  components: {
    [ElConfigProvider.name]: ElConfigProvider,
  },
  setup() {
    return {
      locale: zhCn, // 直接返回 zhCn
    }
  },
  created() {
    // 在页面刷新时将 vuex 状态存入 sessionStorage
    window.addEventListener('beforeunload', () => {
      sessionStorage.setItem('messageStore', JSON.stringify(this.$store.state))
    })

    // 在页面加载时恢复 vuex 状态
    const savedState = sessionStorage.getItem('messageStore')
    if (savedState) {
      this.$store.replaceState(
        Object.assign(this.$store.state, JSON.parse(savedState))
      )
    }
  },
}
</script>

<style></style>