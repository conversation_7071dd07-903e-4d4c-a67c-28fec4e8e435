import axios from 'axios'
import router from '../router/index.js'
import {
	ElMessage
} from 'element-plus';
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'


// 设置后台域名
// const baseURL = 'http://192.168.105.209:8001'
//  const baseURL = 'http://192.168.1.94:8001'
const baseURL = 'http://119.29.101.15:5001'
axios.defaults.baseURL = baseURL

axios.defaults.timeout = 80000    //请求超时
axios.defaults.validateStatus = function(status) {
	return true
}
export const config = {
  baseURL: baseURL   // WebSocket 地址
};
// 自动携带cookies
axios.defaults.withCredentials = true;

axios.interceptors.request.use(config => {
  NProgress.start()
  // 最后必须return config
  return config
})
// 在 response 拦截器中，隐藏进度条 NProgress.done();
axios.interceptors.response.use(config => {
  NProgress.done()
  return config
})




// 通过requests拦截器，获取sessionStirage中的token，添加到请求头中
axios.interceptors.request.use(config => {
  const url = config.url;
  const regex = /^\/records\/\d+(?:\/report)?\/?/; // 匹配 /records/ 下的所有请求
  if (!regex.test(url) && url !== '/users/login/' && url !== '/users/user/') {
    config.headers.Authorization = 'Bearer ' + window.sessionStorage.getItem('token');
  }
  return config;
});
// 添加响应拦截器
axios.interceptors.response.use(function(response) {

	//响应状态码正常不做处理
	if (response.status === 200) return response
	if (response.status === 201) return response
	if (response.status === 204) return response
	// 异常响应状态码的处理
	// 判断响应状态码是否为401,并且不是登录接口或注册接口
	if (response.status === 401 && (response.config.url !== '/users/login/') && !response.config.url.match(/^\/records\/\d+(?:\/report)?\/?/)) {
		window.localStorage.removeItem('token')
		console.log(response.config.url)
		// 路由跳转到登录页面
		router.push({
			name: 'login'
		})
		ElMessage({
			message: '您未登录,请先进行登录!',
			type: 'warning',
			duration: 1000
		});
	} else if (response.status === 400) {
		ElMessage({
			message: response.data.message,
			type: 'warning',
			duration: 1000
		});
	} else if (response.status === 401) {
		ElMessage({
			message: response.data.message,
			type: 'warning',
			duration: 1000
		});
	} else if (response.status === 500) {
		ElMessage({
			message: '服务异常，请联系开发人员处理！',
			type: 'error',
			duration: 1000
		});
	} else if (response.status === 404) {
	} else {
		// 其他的响应状态码提示
		ElMessage({
			message: response.data,
			type: 'warning',
			duration: 1000
		});
	}
	return response;
});

export default {
	// 上传文件接口信息
	uploadApi: {
		url: axios.defaults.baseURL + '/upload/',

	},

	//--------------------用户登录----------------------------------
	// 登录接口
	login(params) {
		return axios.post('/users/login/', params)
	},

	// ==========================用户管理接口================
	// 获取所有用户
	getAllUsers(url, projectId) {
		return axios.get(url,{
			params: {
				project: projectId
			}
		})
	},
	// 获取项目外的用户
	getExcludeUsers(projectId) {
		return axios.get('/users/user/exclude_project/',{
			params: {
				project: projectId
			}
		})
	},
	addExcludeUser(params) {
		return axios.post('/users/user/add_exclude/',params)
	},
	// 新增用户
	createUser(params) {
		return axios.post('/users/user/',params)
	},
	// 修改用户
	updateUser(id,params) {
		return axios.patch(`/users/user/${id}/`,params)
	},

	// 删除用户
	deleteUser(id) {
		return axios.delete(`/users/user/${id}/`)
	},


	// -------------------项目增删查改-------------------------------
	// 获取所有项目
	getProjects() {
		return axios.get('/projects/')
	},
	// 获取单个项目详情
	getProject(id) {
		return axios.get(`/projects/${id}/`)
	},
	// 删除项目
	delProject(id) {
		return axios.delete(`/projects/${id}/`)
	},
	// 添加项目
	createProjects(params) {
		return axios.post('/projects/', params)
	},
	// 编辑项目项目
	updateProjects(id, params) {
		return axios.patch(`/projects/${id}/`, params)
	},
	// ================接口增删查改===================
	// 获取所有接口
	getInterfaces(projectId, type, page, size, name, method, url) {
		return axios.get(`/interfaces/`, {
			params: {
				project: projectId,
				type: type,
				page: page,
				size: size,
				name: name,
				method: method,
				url: url
			}
		})
	},
	// 删除接口
	delInterface(id) {
		return axios.delete(`/interfaces/${id}/`)
	},
	// 添加接口
	createInterface(params) {
		return axios.post('/interfaces/', params)
	},
	// 修改接口
	updateInterface(id, params) {
		return axios.patch(`/interfaces/${id}/`, params)
	},

	// ================new结构树增删查改===================
	// 获取所有treeNode
	getTreeNode(params) {
		return axios.get(`/treeNode/`,{
			params: params
		})
	},
	// 删除treeNode
	deleteTreeNode(id) {
		return axios.delete(`/treeNode/${id}/`)
	},
	// 添加treeNode
	createTreeNode(params) {
		return axios.post('/treeNode/', params)
	},
	// 修改treeNode
	updateTreeNode(id, params) {
		return axios.patch(`/treeNode/${id}/`, params)
	},



	// ================new接口增删查改===================

	// 获取所有接口
	getNewInterfaces(treeNodeId, projectId, name, status, creator) {
		return axios.get(`/newinterfaces/`, {
			params: {
				treenode_id: treeNodeId,
				project: projectId,
				name: name,
				creator: creator,
				status: status
			}
		})
	},

	// 获取单个测试步骤
	getNewInterface(id) {
		return axios.get(`/newinterfaces/${id}/`)
	},

	// 删除单个接口
	deleteNewInterface(id) {
		return axios.delete(`/newinterfaces/${id}/`)
	},

	// 批量删除接口
	deleteAllNewInterfaces(params) {
		return axios.post('/newinterfaces/delete_batch/', params)
	},
	// 添加接口
	createNewInterface(params) {
		return axios.post('/newinterfaces/', params)
	},
	// 修改接口
	updateNewInterface(id, params) {
		return axios.patch(`/newinterfaces/${id}/`, params)
	},


	// 运行单用例的接口
	runNewCase(params) {
		return axios.post('/newinterfaces/run/', params)
	},




	// ================hook推送增删查改===================
	// 获取所有hook
	getHooks(projectId, page, size) {
		return axios.get(`/wxPush/`, {
			params: {
				project_id: projectId,
				page: page,
				size: size
			}
		})
	},
	// 删除hook
	deleteHook(id) {
		return axios.delete(`/wxPush/${id}/`)
	},
	// 添加hook
	createHook(params) {
		return axios.post('/wxPush/', params)
	},
	// 修改hook
	updateHook(id, params) {
		return axios.patch(`/wxPush/${id}/`, params)
	},

	// ============测试场景相关的接口====================
	// 获取项目所有测试场景
	getTestScenes(params) {
		return axios.get(`/test_scenes/`, {
			params: params
		})
	},
	// 获取单个测试场景下的详细数据
	getSceneInfo(sceneId) {
		return axios.get(`/test_scenes/${sceneId}/`)
	},
	// 删除测试场景
	deleteTestScene(id) {
		return axios.delete(`/test_scenes/${id}/`)
	},
	// 添加测试场景
	createTestScene(params) {
		return axios.post('/test_scenes/', params)
	},
	// 修改测试场景
	updateTestScene(id, params) {
		return axios.patch(`/test_scenes/${id}/`, params)
	},
	// ==============测试场景中的数据==================
	// 修改测试场景中的执行步骤顺序
	updateSceneDataOrder(params) {
		return axios.put('/test_scene_steps/order/', params)
	},
	// 获取测试场景数据
	getSceneData(sceneId) {
		return axios.get(`/test_scene_steps/`, {
			params: {
				scene: sceneId
			}
		})
	},

	// 添加步骤到测试场景中
	addSceneData(params) {
		return axios.post('/test_scene_steps/', params)
	},
	// 删除测试场景中的步骤
	deleteSceneData(id) {
		return axios.delete(`/test_scene_steps/${id}/`)
	},

	// ==============测试步骤相关的接口================
	// 获取测试步骤
	getTestSteps(params) {
		return axios.get(`/test_steps/`, {
			params: params
		})
	},
	// 获取单个测试步骤
	getTestStepInfo(id) {
		return axios.get(`/test_steps/${id}/`)
	},
	// 删除测试步骤
	deleteTestStep(id) {
		return axios.delete(`/test_steps/${id}/`)
	},
	// 创建测试步骤
	createTestStep(params) {
		return axios.post('/test_steps/', params)
	},
	// 修改测试步骤
	updateTestStep(id, params) {
		return axios.patch(`/test_steps/${id}/`, params)
	},


	// ============测试计划相关的接口====================
	// 获取项目所有测试计划
	getTestPlans(projectId, name) {
		return axios.get(`/test_plans/`, {
			params: {
				project: projectId,
				name: name
			}
		})
	},
	// 删除测试计划
	deleteTestPlan(id) {
		return axios.delete(`/test_plans/${id}/`)
	},
	// 添加测试计划
	createTestPlan(params) {
		return axios.post('/test_plans/', params)
	},
	// 添加测试计划下的场景
	createTestPlanScene(id, params) {
		return axios.post(`/test_plans/${id}/add_new_scenes/`, params)
	},
	// 删除测试计划下的场景
	deleteTestPlanScene(id, params) {
		return axios.post(`/test_plans/${id}/remove_new_scene/`, params)
	},
	// 修改测试计划
	updateTestPlan(id, params) {
		return axios.patch(`/test_plans/${id}/`, params)
	},
	// ============测试环境相关的接口====================
	// 获取项目所有测试环境
	getTestEnvs(projectId) {
		return axios.get(`/test_envs/`, {
			params: {
				project: projectId
			}
		})
	},
	getEnvInfo(id, projectId) {
		return axios.get(`/test_envs/${id}/`, {
			params: {
				project: projectId
			}
		})
	},
	// 删除测试环境
	deleteTestEnv(id) {
		return axios.delete(`/test_envs/${id}/`)
	},
	// 添加测试环境
	createTestEnv(params) {
		return axios.post('/test_envs/', params)
	},
	// 修改测试环境
	updateTestEnv(id, params) {
		return axios.patch(`/test_envs/${id}/`, params)
	},
	// ==========================定时任务接口================
	// 获取所有定时任务
	getCrons(projectId) {
		return axios.get(`/crontab_tasks/`, {
			params: {
				project: projectId
			}
		})
	},
	// 删除定时任务
	deleteCron(id) {
		return axios.delete(`/crontab_tasks/${id}/`)
	},
	// 添加定时任务
	createCron(params) {
		return axios.post('/crontab_tasks/', params)
	},
	// 修改定时任务
	updateCron(id, params) {
		return axios.patch(`/crontab_tasks/${id}/`, params)
	},


	// ===================测试记录==========================
	// 获取所有的测试记录
	getTestRecord(params) {
		return axios.get(`/records/`, {
			params: params,
		})
	},
	getRecordInfo(id) {
		return axios.get(`/records/${id}/`)
	},
	// 获取测试报告信息
	getTestReport(id) {
		return axios.get(`/records/${id}/report/`)
	},
	//=====================bug管理======================
	// 获取所有bug
	getBugs(params) {
		return axios.get('/bugs/', {
			params: params
		})
	},
	// 添加bug记录
	createBugs(params) {
		return axios.post('/bugs/', params)
	},
	// 修改bug记录
	updateBug(id, params) {
		return axios.patch(`/bugs/${id}/`, params)
	},
	// 删除bug
	deleteBug(id) {
		return axios.delete(`/bugs/${id}/`)
	},
	//=====================获取bug处理记录列表======================
	getBugLogs(params) {
		return axios.get('/blogs/', {
			params: params
		})
	},


	// =================用例运行===========================
	// 运行测试的接口
	runTest(params) {
		return axios.post('/runTest/', params)
	},
	// 运行单用例的接口
	runCase(params) {
		return axios.post('/test_steps/run/', params)
	},
	// 运行单个场景的接口
	runScene(id, params) {
		return axios.post(`/test_scenes/${id}/run/`, params)
	},
	// 运行单个场景的接口
	runCases(id, params) {
		return axios.post(`/TestCase/${id}/run/`, params)
	},
	// 运行测试计划的接口
	runPlan(id, params) {
		return axios.post(`/test_plans/${id}/run/`, params)
	},

	// ================文件上传操作========================
	// 上传文件
	uploadFile(params) {
		// 功能待完善
		return axios.post('/upload/', params)
	},
	// 获取文件列表
	getFiles() {
		return axios.get('/upload/')
	},
	// 删除文件
	deleteFile(id) {
		return axios.delete(`/upload/${id}/`)
	},

	// ================测试用例增删查改===================
	// 获取用例信息
	getTestCase(project_id,page,name,username) {
		return axios.get(`/TestCase/`, {
			params: {
				project_id: project_id,
				page: page,
				name: name,
				creator: username

			}
		})
	},
		// 获取单个用例信息
	getTestCase_(params) {
		return axios.get(`/TestCase/`, {
			params: params
		})
	},
	// 删除用例
	delTestCase(id) {
		return axios.delete(`/TestCase/${id}/`)
	},
	// 添加用例
	createTestCase(params) {
		return axios.post('/TestCase/', params)
	},
	// 修改用例
	updateTestCase(id, params) {
		return axios.patch(`/TestCase/${id}/`, params)
	},
	// 进入用例详情
	detailTestCase(id) {
		return axios.patch(`/TestCase/${id}/`)
	},

	// ================测试用例步骤的增删查改===================
	// 获取用例步骤
	getTestCaseStep(cases) {
		return axios.get(`/TestCase_Setp/`, {
			params: {
				case: cases,
			}
		})
	},
	// 批量添加用例步骤
	createsTestCaseStep(params) {
		return axios.post('/TestCase_Setp/batch_create/', params)
	},
	// 修改单个用例步骤
	updateTestCaseStep(id, params) {
		return axios.patch(`/TestCase_Setp/${id}/`, params)
	},
	// 单个添加用例步骤
	createTestCaseStep(params) {
		return axios.post('/TestCase_Setp/', params)
	},
	// 删除用例步骤
	delTestCaseStep(id) {
		return axios.delete(`/TestCase_Setp/${id}/delete_node`)
	},

	// 修改用例步骤的执行步骤顺序
	updateCaseStepOrder(params) {
		return axios.put('/TestCase_Setp/order/', params)
	},

	// ================测试用例控制器步骤的增删查改===================
	// 新增控制器步骤
	createStepControll(params) {
		return axios.post('/StepControll/', params)
	},
	// copy步骤
	copyStepControll(params) {
		return axios.post('/StepControll/copyStep/', params)
	},
	// 删除控制器步骤
	delStepControll(id) {
		return axios.delete(`/StepControll/${id}/`)
	},

	// 修改控制器步骤
	updateStepControll(id, params) {
		return axios.patch(`/StepControll/${id}/`, params)
	},

	// 批量修改控制器步骤
	updatesStepControll(params) {
		return axios.put('/StepControll/batch_updateStep/', params)
	},

	// ================接口导入操作===================
	// YApi接口导入
	getYApiImport(params) {
		return axios.post('/yapi/', params)
	},
	// Curl接口导入
	getCurlImport(params) {
		return axios.post('/curl/', params)
	},
	// Postman接口导入
	getPostmanImport(formData) {
		return axios.post('/postman/', formData, {
			headers: {
				'Content-Type': 'multipart/form-data'
			}
		})
	},
	// Apipost接口导入
	getApipostImport(formData) {
		return axios.post('/apipost/', formData, {
			headers: {
				'Content-Type': 'multipart/form-data'
			}
		})
	},
	// Swagger接口导入
	getSwaggerImport(params) {
		if (params instanceof FormData) {
			return axios.post('/swagger/file/', params, {
				headers: {
					'Content-Type': 'multipart/form-data'
				}
			})
		} else {
			return axios.post('/swagger/url/', params)
		}
	},
	// JS fetch接口导入
	getJsFetchImport(params) {
		return axios.post('/jsfetch/', params)
	},


	// ================项目看板===================

	getProjectBoard(params) {
		return axios.post('/ProjectBoard/', params)
	},

	// ================接口mock===================

	// 获取单个mock信息
	getMock(id) {
		return axios.get(`/mock/${id}/`)
	},

	// 新增mock接口
	createMock(params) {
		return axios.post('/mock/', params)
	},

	// 修改mock接口
	updateMock(id, params) {
		return axios.patch(`/mock/${id}/`, params)
	},

	// 新增mock期望
	createDetail(params) {
		return axios.post('/mock_detail/', params)
	},

	// 修改mock期望
	updateDetail(id, params) {
		return axios.patch(`/mock_detail/${id}/`, params)
	},

	// 删除mock期望
	delDetail(id) {
		return axios.delete(`/mock_detail/${id}/`)
	},

	// ================机器管理===================
	// 获取单个server信息
	getServer(id) {
		return axios.get(`/server/${id}/`)
	},
	// 获取server列表
	getServers(project_id,page) {
		return axios.get(`/server/`, {
			params: {
				project_id: project_id,
				page: page,

			}
		})
	},
	// 新增server信息
	createServer(params) {
		return axios.post('/server/', params)
	},

	// 修改server信息
	updateServer(id, params) {
		return axios.patch(`/server/${id}/`, params)
	},

	// 删除server信息
	delServer(id) {
		return axios.delete(`/server/${id}/`)
	},


	// ================预设置===================

	// 获取配置列表
	getPresetting(params) {
		return axios.get(`/presetting/`, {
			params: params
		})
	},
	// 新增配置信息
	createPresetting(params) {
		return axios.post('/presetting/', params)
	},

	// 修改配置信息
	updatePresetting(id, params) {
		return axios.patch(`/presetting/${id}/`, params)
	},

	// 保存任务配置
	setPresetting(params) {
		return axios.post(`/presetting/save_presetting/`, params)
	},

	// 删除配置信息
	delPresetting(id) {
		return axios.delete(`/presetting/${id}/`)
	},


	// ================性能任务===================
	// 获取性能任务列表
	getPerformanceTask(project_id,page,taskName) {
		return axios.get(`/performanceTask/`, {
			params: {
				project_id: project_id,
				page: page,
				taskName: taskName,

			}
		})
	},
	// 获取不分页的性能任务列表
	getPerformanceTasks(params) {
		return axios.get('/performanceTask/', {
			params: {
				...params,
				no_page: true // 不分页标记
			}
		})
	},
	// 新增性能任务
	createPerformanceTask(params) {
		return axios.post('/performanceTask/', params)
	},

	// 修改性能任务
	updatePerformanceTask(id, params) {
		return axios.patch(`/performanceTask/${id}/`, params)
	},

	// 删除性能任务
	delPerformanceTask(id) {
		return axios.delete(`/performanceTask/${id}/`)
	},

	// 运行性能任务
	runTask(id, params) {
		return axios.post(`/performanceTask/${id}/run/`, params)
	},

	// 运行优化版性能测试
	runPerformanceTestOptimized(taskId, params) {
		return axios.post(`/performanceTask/${taskId}/run_optimized/`, params)
	},

	// 停止性能测试
	stopPerformanceTest(taskId) {
		return axios.post(`/performanceTask/${taskId}/stop/`)
	},

	// 获取性能测试报告列表
	getTaskReports(params) {
		return axios.get('/taskReport/', {
			params: params
		})
	},

	// 获取性能测试报告详情
	getTaskReportDetail(id) {
		return axios.get(`/taskReport/${id}/`)
	},

	// 获取性能测试报告日志
	getTaskReportLogs(id, params) {
		return axios.get(`/taskReport/${id}/logs/`, {
			params: params
		})
	},

	// 更新性能测试报告详情
	updateTaskReportDetail(id, params) {
		return axios.patch(`/taskReport/${id}/`, params)
	},

	// 获取性能测试报告统计
	getTaskReport(params) {
		return axios.get('/taskReport/statistics/', {
			params: params
		})
	},

	// 导出性能测试报告
	taskReport(params, responseType) {
		const config = {
			params: params
		};
		if (responseType) {
			config.responseType = responseType;
		}
		return axios.get('/taskReport/export_all/', config)
	},

	// 删除性能测试报告
	delTaskReport(id) {
		return axios.delete(`/taskReport/${id}/`)
	},

	// 获取目标服务状态
	getTargetServiceStatus(reportId) {
		return axios.get(`/taskReport/${reportId}/target_service_status/`)
	},

	// 获取系统资源状态
	getSystemResourceStatus() {
		return axios.get('/systemResource/current_status/')
	},

	// ================性能任务对比和高级功能===================

	// 性能任务对比
	compareTaskPerformance(params) {
		return axios.post('/taskReport/compare_task_performance/', params)
	},

	// 生成对比报告
	generateComparisonReport(params) {
		return axios.post('/taskReport/generate_comparison_report/', params)
	},

	// 获取性能仪表板数据
	getPerformanceDashboard(params) {
		return axios.get('/taskReport/performance_dashboard/', {
			params: params
		})
	},

	// 分析性能报告
	analyzePerformanceReport(reportId, params) {
		return axios.post(`/taskReport/${reportId}/analyze_performance/`, params)
	},

	// 生成HTML报告
	generateReportHtml(reportId, params) {
		return axios.post(`/taskReport/${reportId}/generate_report_html/`, params)
	},

	// 获取报告模板
	getReportTemplates() {
		return axios.get('/taskReport/report_templates/')
	},

	// 导出单个报告
	exportSingleReport(reportId) {
		return axios.get(`/taskReport/${reportId}/export/`, {
			responseType: 'blob'
		})
	},

	// ================基准线管理===================



	// 获取基准线详情
	getBaselineDetail(baselineId) {
		return axios.get(`/taskReport/${baselineId}/get_baseline/`)
	},


	// 与基准线对比
	compareWithBaseline(reportId, params) {
		return axios.post(`/taskReport/${reportId}/compare_with_baseline/`, params)
	},

	// 自动创建基准线
	autoCreateBaseline(reportId, params) {
		return axios.post(`/taskReport/${reportId}/auto_create_baseline/`, params)
	},

	// 获取基准线统计
	getBaselineStatistics(params) {
		return axios.get('/taskReport/baseline_statistics/', {
			params: params
		})
	},

	// ================告警管理===================

	// 获取告警状态
	getAlertStatus(params) {
		return axios.get('/taskReport/alert_status/', {
			params: params
		})
	},

	// 添加告警规则
	addAlertRule(params) {
		return axios.post('/taskReport/add_alert_rule/', params)
	},

	// 启动告警监控
	startAlertMonitoring() {
		return axios.post('/taskReport/start_alert_monitoring/')
	},

	// 停止告警监控
	stopAlertMonitoring() {
		return axios.post('/taskReport/stop_alert_monitoring/')
	},

	// 确认告警
	acknowledgeAlert(params) {
		return axios.post('/taskReport/acknowledge_alert/', params)
	},

	// 获取告警规则
	getAlertRules(params) {
		return axios.get('/taskReport/alert_rules/', {
			params: params
		})
	},

	// 获取告警历史
	getAlertHistory(params) {
		return axios.get('/taskReport/alert_history/', {
			params: params
		})
	},

	// 更新告警规则
	updateAlertRule(ruleId, params) {
		return axios.patch(`/taskReport/${ruleId}/update_alert_rule/`, params)
	},

	// 删除告警规则
	deleteAlertRule(ruleId) {
		return axios.delete(`/taskReport/${ruleId}/delete_alert_rule/`)
	},

	// 获取基准线列表 - 修复路径
	getBaselines(params) {
		return axios.get('/taskReport/list_baselines/', {
			params: params
		})
	},

	// 创建基准线 - 修复路径
	createBaseline(params) {
		return axios.post('/taskReport/create_baseline/', params)
	},

	// 更新基准线 - 修复路径
	updateBaseline(baselineId, params) {
		return axios.patch(`/taskReport/${baselineId}/update_baseline/`, params)
	},

	// 删除基准线 - 修复路径
	deleteBaseline(baselineId) {
		return axios.delete(`/taskReport/${baselineId}/delete_baseline/`)
	},

	// ================工作流管理===================

	// 创建工作流
	createWorkflow(params) {
		return axios.post('/taskReport/create_workflow/', params)
	},

	// 获取工作流列表
	getWorkflows(params) {
		return axios.get('/taskReport/list_workflows/', {
			params: params
		})
	},

	// 获取工作流详情
	getWorkflowDetail(workflowId) {
		return axios.get(`/taskReport/${workflowId}/get_workflow/`)
	},

	// 添加工作流步骤
	addWorkflowStep(workflowId, params) {
		return axios.post(`/taskReport/${workflowId}/add_workflow_step/`, params)
	},

	// 添加工作流触发器
	addWorkflowTrigger(workflowId, params) {
		return axios.post(`/taskReport/${workflowId}/add_workflow_trigger/`, params)
	},

	// 执行工作流
	executeWorkflow(workflowId, params) {
		return axios.post(`/taskReport/${workflowId}/execute_workflow/`, params)
	},

	// 停止工作流
	stopWorkflow(workflowId) {
		return axios.post(`/taskReport/${workflowId}/stop_workflow/`)
	},

	// 获取执行历史
	getWorkflowExecutionHistory(params) {
		return axios.get('/taskReport/execution_history/', {
			params: params
		})
	},

	// 获取工作流统计
	getWorkflowStatistics(params) {
		return axios.get('/taskReport/workflow_statistics/', {
			params: params
		})
	},

	// 获取工作流模板
	getWorkflowTemplates() {
		return axios.get('/taskReport/workflow_templates/')
	},

	// ================数据管理===================

	// 导出测试数据
	exportTestData(params) {
		return axios.post('/taskReport/export_data/', params, {
			responseType: 'blob'
		})
	},

	// 导入测试数据
	importTestData(formData) {
		return axios.post('/taskReport/import_data/', formData, {
			headers: {
				'Content-Type': 'multipart/form-data'
			}
		})
	},

	// 获取导出模板
	getExportTemplate(params) {
		return axios.get('/taskReport/export_template/', {
			params: params
		})
	},

	// ================系统监控高级功能===================

	// 获取系统监控历史
	getSystemResourceHistory(params) {
		return axios.get('/systemResource/history/', {
			params: params
		})
	},

	// 获取进程状态
	getProcessStatus(params) {
		return axios.get('/systemResource/process_status/', {
			params: params
		})
	},

	// ================服务器管理扩展===================

	// 测试服务器连接
	testServerConnection(serverId) {
		return axios.post(`/server/${serverId}/test_connection/`)
	},

	// 获取服务器系统信息
	getServerSystemInfo(serverId) {
		return axios.post(`/server/${serverId}/get_system_info/`)
	},

	// 获取集群状态
	getClusterStatus(params) {
		return axios.get('/server/cluster_status/', {
			params: params
		})
	},

	// ================压测任务高级功能===================

	// 停止性能测试
	stopPerformanceTask(taskId) {
		return axios.post(`/performanceTask/${taskId}/stop/`)
	},

	// 获取分布式测试状态
	getDistributedTestStatus(taskId) {
		return axios.get(`/performanceTask/${taskId}/distributed_status/`)
	},

	// 停止分布式测试
	stopDistributedTest(taskId) {
		return axios.post(`/performanceTask/${taskId}/stop_distributed_test/`)
	},

	// 测试协议连接
	testProtocolConnection(params) {
		return axios.post('/performanceTask/test_protocol/', params)
	},

	// 运行协议测试
	runProtocolTest(taskId, params) {
		return axios.post(`/performanceTask/${taskId}/run_protocol_test/`, params)
	},

	// 获取协议测试状态
	getProtocolTestStatus(taskId) {
		return axios.get(`/performanceTask/${taskId}/protocol_test_status/`)
	},

	// ================性能场景===================
	// 获取性能场景
	getTaskScenes(id, name) {
		return axios.get(`/taskScence/`, {
			params: {
				task: id,
				name: name
			}
		})
	},
	// 获取单个性能场景
	getTaskScene(id) {
		return axios.get(`/taskScence/${id}/`)
	},
	// 新增性能场景
	createTaskScene(params) {
		return axios.post('/taskScence/', params)
	},

	// 修改性能场景
	updateTaskScene(id, params) {
		return axios.patch(`/taskScence/${id}/`, params)
	},

	// 删除性能场景
	deleteTaskScene(id) {
		return axios.delete(`/taskScence/${id}/`)
	},

	// 导出场景
	exportTaskScene() {
		return axios.get('/taskScence/export/')
	},



	// ================性能场景步骤===================

	// 新增场景步骤
	createSceneStep(params) {
		return axios.post('/taskScenceStep/', params)
	},
	// 获取单个场景步骤
	getSceneStep(sceneId) {
		return axios.get('/taskScenceStep/', {
			params: {type: 'api', scence: sceneId}
		})
	},
	// 修改性能场景步骤
	updateSceneStep(id, params) {
		return axios.patch(`/taskScenceStep/${id}/`, params)
	},

	// 批量修改场景步骤
	batchUpdateSceneStep(params) {
		return axios.post(`/taskScenceStep/batchSaveSetp/`, params)
	},

	// 批量同步接口步骤
	batchSaveApiStep(params) {
		return axios.post(`/taskScenceStep/batchSaveApiSetp/`, params)
	},

	// 删除场景步骤
	deleteSceneStep(id) {
		return axios.delete(`/taskScenceStep/${id}/`)
	},

	// ================通知功能===================

	// 发送报告通知
	sendReportNotification(params) {
		return axios.post('/taskReport/send_notification/', params)
	},

	// 获取通知配置
	getNotificationSettings(params) {
		return axios.get('/taskReport/notification_settings/', {
			params: params
		})
	},

	// 更新通知配置
	updateNotificationSettings(params) {
		return axios.put('/taskReport/notification_settings/', params)
	},

	// 测试通知连接
	testNotificationConnection(params) {
		return axios.post('/taskReport/test_notification/', params)
	},

	// 获取通知历史
	getNotificationHistory(params) {
		return axios.get('/taskReport/notification_history/', {
			params: params
		})
	},


	// ================任务场景步骤接口（多表）===================

	// 获取任务场景步骤
	getTaskSceneStep(sceneId) {
		return axios.get(`/performanceScenceStep/`, {
			params: {scence: sceneId}
		})
	},
	// 新增任务场景步骤
	createTaskSceneStep(params) {
		return axios.post('/performanceScenceStep/', params)
	},

	// 修改任务场景步骤
	updateTaskSceneStep(id, params) {
		return axios.patch(`/performanceScenceStep/${id}/`, params)
	},

	// 删除任务场景步骤
	deleteTaskSceneStep(id, sceneId) {
		return axios.delete(`/performanceScenceStep/${id}/`,{
			params: {scence: sceneId}
		})
	},

	// 删除任务场景步骤
	batchTaskSceneStep(params) {
		return axios.post('/performanceScenceStep/batchTaskScenceStep/', params)
	},

	// 场景调试
	debugScenario(params) {
		return axios.post('/scenarioDebug/debug_scenario/', params)
	},

	// 测试服务器连接
	testServerConnections(params, serverId) {
		return axios.post(`/server/${serverId}/test_connection/`, params)
	},

	// ================性能服务器管理===================
	
	// 获取性能服务器列表
	getPerformanceServers(params) {
		return axios.get('/server/', {
			params: params
		})
	},

	// 检查端口可用性
	checkPortAvailability(params) {
		return axios.post('/server/check_port/', params)
	},

	// 获取服务器状态
	getServerStatus(serverId) {
		return axios.get(`/server/${serverId}/status/`)
	},

	// 获取用于执行的服务器列表
	getServersForExecution(params) {
		return axios.get('/server/', {
			params: params
		})
	},


}