/**
 * 性能测试相关常量定义
 */

// 任务类型映射
export const TASK_TYPE_MAP = {
  '10': '普通任务',
  '20': '定时任务'
};

// 日志模式映射
export const LOG_MODE_MAP = {
  '0': '关闭',
  '10': '开启-全部日志',
  '20': '开启-仅成功日志',
  '30': '开启-仅失败日志'
};

// 测试状态映射
export const TEST_STATUS_MAP = {
  '0': '未开始',
  '1': '运行中',
  '2': '已完成',
  '3': '已终止',
  '4': '失败'
};

// 比较操作符映射
export const COMPARISON_OPERATORS = [
  { value: 'equal', label: '等于' },
  { value: 'notEqual', label: '不等于' },
  { value: 'contains', label: '包含' },
  { value: 'notContains', label: '不包含' },
  { value: 'greaterThan', label: '大于' },
  { value: 'lessThan', label: '小于' },
  { value: 'greaterThanOrEqual', label: '大于等于' },
  { value: 'lessThanOrEqual', label: '小于等于' },
  { value: 'empty', label: '空' },
  { value: 'notEmpty', label: '非空' }
];

// While循环比较操作符
export const WHILE_OPERATORS = [
  { label: '<', value: 'lt' },
  { label: '<=', value: 'lte' },
  { label: '>', value: 'gt' },
  { label: '>=', value: 'gte' },
  { label: '==', value: 'eq' },
  { label: '!=', value: 'ne' },
  { label: '包含', value: 'contains' },
  { label: '不包含', value: 'not_contains' }
];

// HTTP方法颜色映射
export const HTTP_METHOD_COLORS = {
  POST: '#49cc90',
  GET: '#61affe',
  PUT: '#fca130',
  PATCH: '#50e3c2',
  DELETE: '#f93e3e',
  HEAD: '#9012fe',
  OPTIONS: '#0d5aa7'
};

// 步骤类型名称映射
export const STEP_TYPE_NAMES = {
  api: 'HTTP请求',
  if: '条件控制器',
  for: '循环控制器',
  script: '自定义脚本',
  py: '导入PY脚本',
  time: '等待控制器'
};

// 步骤类型颜色映射
export const STEP_TYPE_COLORS = {
  api: '#61649f',
  if: '#E6A23C',
  for: '#02A7F0',
  script: '#7B4D12',
  py: '#67C23A',
  time: '#67C23A'
};

// 默认阶梯配置
export const DEFAULT_LADDER = {
  concurrencyNumber: '',
  concurrencyStep: '',
  lastLong: ''
};

// While循环默认配置
export const DEFAULT_WHILE_CONFIG = {
  whileConditionType: "variable",
  whileLeftOperand: "",
  whileOperator: "lt",
  whileRightOperand: "",
  whileExpression: "",
  whileFunctionName: "",
  whileFunctionArgs: "",
  whileMaxIterations: 100,
  whileTimeout: 0,
  whileCounterVar: "loop_counter",
  whileBreakOnError: true,
  whileLogIterations: false,
  whileCheckTiming: "before"
};