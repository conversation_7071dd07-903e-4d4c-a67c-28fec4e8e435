import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import installElementPlus from './plugins/element';
import api from './api/index.js';
import './assets/css/main.css';
import tools from './assets/js/tools.js';
import chart from './chart/index.js';

const app = createApp(App);

// 将请求对象绑定为应用的全局属性 $api
app.config.globalProperties.$api = api;

// 将工具函数绑定为全局的属性 $tools
app.config.globalProperties.$tools = tools;

// 将定义 ECharts 图表对象绑定为全局属性 $chart
app.config.globalProperties.$chart = chart;

// 安装 Element Plus 插件
installElementPlus(app);

// 使用 Vuex 和 Vue Router，并挂载应用
app.use(store).use(router).mount('#app');