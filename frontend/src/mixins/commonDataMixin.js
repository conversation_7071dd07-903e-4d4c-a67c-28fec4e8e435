/**
 * 通用数据处理 Mixin
 * 提供常用的数据处理逻辑和计算属性
 */

import { TASK_TYPE_MAP, LOG_MODE_MAP, TEST_STATUS_MAP } from '@/constants/performanceConstants';

export default {
  data() {
    return {
      // 分页数据
      pages: {
        count: 0,
        current: 1,
        size: 20
      },
      
      // 加载状态
      loading: false,
      
      // 搜索关键词
      searchKeyword: '',
      
      // 常用映射
      taskTypeMap: TASK_TYPE_MAP,
      logModeMap: LOG_MODE_MAP,
      testStatusMap: TEST_STATUS_MAP
    }
  },

  computed: {
    /**
     * 当前用户名
     */
    username() {
      return window.sessionStorage.getItem('username') || '';
    },

    /**
     * 当前用户ID
     */
    userId() {
      return window.sessionStorage.getItem('userId') || '';
    },

    /**
     * 当前项目信息（需要Vuex状态）
     */
    currentProject() {
      return this.$store?.state?.pro || {};
    },

    /**
     * 当前项目ID
     */
    currentProjectId() {
      return this.currentProject?.id || '';
    },

    /**
     * 当前环境ID（需要Vuex状态）
     */
    currentEnvId() {
      return this.$store?.state?.envId || '';
    },

    /**
     * 是否有搜索关键词
     */
    hasSearchKeyword() {
      return this.searchKeyword && this.searchKeyword.trim().length > 0;
    },

    /**
     * 总页数
     */
    totalPages() {
      return Math.ceil(this.pages.count / this.pages.size);
    }
  },

  methods: {
    /**
     * 处理分页变化
     * @param {number} currentPage - 当前页码
     * @param {Function} fetchMethod - 获取数据的方法
     */
    handlePageChange(currentPage, fetchMethod) {
      this.pages.current = currentPage;
      if (fetchMethod && typeof fetchMethod === 'function') {
        fetchMethod(currentPage);
      }
    },

    /**
     * 处理分页大小变化
     * @param {number} pageSize - 页面大小
     * @param {Function} fetchMethod - 获取数据的方法
     */
    handleSizeChange(pageSize, fetchMethod) {
      this.pages.size = pageSize;
      this.pages.current = 1;
      if (fetchMethod && typeof fetchMethod === 'function') {
        fetchMethod(1);
      }
    },

    /**
     * 重置分页到第一页
     */
    resetPagination() {
      this.pages.current = 1;
    },

    /**
     * 搜索处理
     * @param {Function} searchMethod - 搜索方法
     */
    handleSearch(searchMethod) {
      this.resetPagination();
      if (searchMethod && typeof searchMethod === 'function') {
        searchMethod(this.searchKeyword);
      }
    },

    /**
     * 清空搜索
     * @param {Function} fetchMethod - 获取数据的方法
     */
    clearSearch(fetchMethod) {
      this.searchKeyword = '';
      this.resetPagination();
      if (fetchMethod && typeof fetchMethod === 'function') {
        fetchMethod();
      }
    },

    /**
     * 格式化时间
     * @param {string|Date} time - 时间字符串或Date对象
     * @param {string} format - 格式化模式
     * @returns {string}
     */
    formatTime(time, format = 'YYYY-MM-DD HH:mm:ss') {
      if (!time) return '-';
      
      // 使用全局工具方法（如果存在）
      if (this.$tools && this.$tools.rTime) {
        return this.$tools.rTime(time);
      }
      
      // 简单的时间格式化
      const date = new Date(time);
      if (isNaN(date.getTime())) return '-';
      
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string}
     */
    formatBytes(bytes) {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * 格式化百分比
     * @param {number} value - 数值
     * @param {number} precision - 精度
     * @returns {string}
     */
    formatPercent(value, precision = 1) {
      if (typeof value !== 'number' || isNaN(value)) return '0%';
      return `${value.toFixed(precision)}%`;
    },

    /**
     * 格式化数字
     * @param {number} value - 数值
     * @param {number} precision - 精度
     * @returns {string}
     */
    formatNumber(value, precision = 2) {
      if (typeof value !== 'number' || isNaN(value)) return '0';
      return value.toFixed(precision);
    },

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间（毫秒）
     * @returns {Function}
     */
    debounce(func, wait = 300) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func.apply(this, args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} limit - 限制时间（毫秒）
     * @returns {Function}
     */
    throttle(func, limit = 300) {
      let inThrottle;
      return function executedFunction(...args) {
        if (!inThrottle) {
          func.apply(this, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      };
    },

    /**
     * 深度复制对象
     * @param {any} obj - 要复制的对象
     * @returns {any}
     */
    deepClone(obj) {
      if (obj === null || typeof obj !== 'object') return obj;
      if (obj instanceof Date) return new Date(obj.getTime());
      if (obj instanceof Array) return obj.map(item => this.deepClone(item));
      if (typeof obj === 'object') {
        const clonedObj = {};
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            clonedObj[key] = this.deepClone(obj[key]);
          }
        }
        return clonedObj;
      }
    },

    /**
     * 获取随机ID
     * @param {string} prefix - 前缀
     * @returns {string}
     */
    generateId(prefix = 'id') {
      return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    },

    /**
     * 获取文件扩展名
     * @param {string} filename - 文件名
     * @returns {string}
     */
    getFileExtension(filename) {
      if (!filename || typeof filename !== 'string') return '';
      const lastDotIndex = filename.lastIndexOf('.');
      return lastDotIndex > -1 ? filename.substring(lastDotIndex + 1).toLowerCase() : '';
    },

    /**
     * 验证URL格式
     * @param {string} url - URL字符串
     * @returns {boolean}
     */
    isValidUrl(url) {
      try {
        new URL(url);
        return true;
      } catch {
        return false;
      }
    },

    /**
     * 验证IP地址格式
     * @param {string} ip - IP地址
     * @returns {boolean}
     */
    isValidIP(ip) {
      const ipRegex = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/;
      return ipRegex.test(ip);
    },

    /**
     * 验证端口号
     * @param {string|number} port - 端口号
     * @returns {boolean}
     */
    isValidPort(port) {
      const portNum = Number(port);
      return Number.isInteger(portNum) && portNum >= 1 && portNum <= 65535;
    },

    /**
     * 获取状态对应的颜色
     * @param {string} status - 状态值
     * @param {object} colorMap - 颜色映射
     * @returns {string}
     */
    getStatusColor(status, colorMap = {}) {
      const defaultColorMap = {
        success: '#67c23a',
        warning: '#e6a23c',
        danger: '#f56c6c',
        info: '#909399',
        primary: '#409eff'
      };
      
      return colorMap[status] || defaultColorMap[status] || defaultColorMap.info;
    }
  },

  // 创建防抖搜索方法
  created() {
    // 创建防抖搜索方法
    this.debouncedSearch = this.debounce((keyword) => {
      if (this.onSearch && typeof this.onSearch === 'function') {
        this.onSearch(keyword);
      }
    }, 500);
  }
};