/**
 * 对话框管理 Mixin
 * 提供通用的对话框状态管理和操作方法
 */

export default {
  data() {
    return {
      dialogVisible: false,
      dialogType: '',
      dialogTitle: '',
      dialogTitles: {
        add: '添加',
        edit: '编辑',
        view: '查看',
        delete: '删除确认'
      }
    }
  },

  methods: {
    /**
     * 打开对话框
     * @param {string} type - 对话框类型 (add/edit/view/delete)
     * @param {object} data - 编辑时的数据对象
     * @param {object} customTitles - 自定义标题映射
     */
    openDialog(type, data = null, customTitles = {}) {
      this.dialogType = type;
      this.dialogVisible = true;
      
      // 合并自定义标题
      const titles = { ...this.dialogTitles, ...customTitles };
      this.dialogTitle = titles[type] || titles.add;
      
      // 如果是编辑模式且有数据，填充表单
      if (type === 'edit' && data) {
        this.populateFormData(data);
      } else if (type === 'add') {
        this.resetFormData();
      }
      
      // 触发对话框打开事件
      this.$emit('dialog-opened', { type, data });
    },

    /**
     * 关闭对话框
     */
    closeDialog() {
      this.dialogVisible = false;
      this.dialogType = '';
      this.dialogTitle = '';
      
      // 重置表单数据
      this.resetFormData();
      
      // 清除表单验证
      this.clearFormValidation();
      
      // 触发对话框关闭事件
      this.$emit('dialog-closed');
    },

    /**
     * 填充表单数据（子组件需要实现）
     * @param {object} data - 要填充的数据
     */
    populateFormData(data) {
      // 默认实现：复制数据到表单对象
      if (this.formData && typeof data === 'object') {
        Object.keys(this.formData).forEach(key => {
          if (data.hasOwnProperty(key)) {
            this.formData[key] = data[key];
          }
        });
      }
    },

    /**
     * 重置表单数据（子组件可以重写）
     */
    resetFormData() {
      if (this.getDefaultFormData && typeof this.getDefaultFormData === 'function') {
        this.formData = this.getDefaultFormData();
      }
    },

    /**
     * 清除表单验证
     */
    clearFormValidation() {
      this.$nextTick(() => {
        // 清除所有表单引用的验证
        const formRefs = ['formRef', 'dialogFormRef', 'form'];
        formRefs.forEach(refName => {
          if (this.$refs[refName] && this.$refs[refName].clearValidate) {
            this.$refs[refName].clearValidate();
          }
        });
      });
    },

    /**
     * 确认操作（处理确定按钮点击）
     */
    async confirmAction() {
      try {
        // 表单验证
        const isValid = await this.validateForm();
        if (!isValid) return;

        // 根据对话框类型执行不同操作
        switch (this.dialogType) {
          case 'add':
            await this.handleAdd();
            break;
          case 'edit':
            await this.handleEdit();
            break;
          case 'delete':
            await this.handleDelete();
            break;
          default:
            console.warn(`未处理的对话框类型: ${this.dialogType}`);
        }
      } catch (error) {
        console.error('操作失败:', error);
        this.$message({
          type: 'error',
          message: error.message || '操作失败',
          duration: 2000
        });
      }
    },

    /**
     * 验证表单
     * @returns {Promise<boolean>}
     */
    async validateForm() {
      const formRefs = ['formRef', 'dialogFormRef', 'form'];
      
      for (const refName of formRefs) {
        const formRef = this.$refs[refName];
        if (formRef && formRef.validate) {
          try {
            await formRef.validate();
            return true;
          } catch (error) {
            console.warn(`表单验证失败 (${refName}):`, error);
            return false;
          }
        }
      }
      
      // 如果没有找到表单引用，默认返回true
      return true;
    },

    /**
     * 处理添加操作（子组件需要实现）
     */
    async handleAdd() {
      throw new Error('子组件必须实现 handleAdd 方法');
    },

    /**
     * 处理编辑操作（子组件需要实现）
     */
    async handleEdit() {
      throw new Error('子组件必须实现 handleEdit 方法');
    },

    /**
     * 处理删除操作（子组件需要实现）
     */
    async handleDelete() {
      throw new Error('子组件必须实现 handleDelete 方法');
    }
  },

  computed: {
    /**
     * 是否显示确定按钮
     */
    showConfirmButton() {
      return ['add', 'edit'].includes(this.dialogType);
    },

    /**
     * 确定按钮文本
     */
    confirmButtonText() {
      const textMap = {
        add: '确定',
        edit: '保存',
        delete: '删除',
        view: '关闭'
      };
      return textMap[this.dialogType] || '确定';
    },

    /**
     * 确定按钮类型
     */
    confirmButtonType() {
      const typeMap = {
        add: 'primary',
        edit: 'primary',
        delete: 'danger',
        view: 'primary'
      };
      return typeMap[this.dialogType] || 'primary';
    }
  }
};