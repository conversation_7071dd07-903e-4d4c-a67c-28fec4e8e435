/**
 * 环境配置管理 Mixin
 * 提供环境选择、配置获取等通用功能
 */

export default {
  data() {
    return {
      // 环境信息
      envInfo: {},
      
      // 环境列表
      testEnvs: [],
      
      // 显示环境详情对话框
      showEnvDialog: false,
      
      // 当前选择的环境ID
      selectedEnvId: null
    }
  },

  computed: {
    /**
     * 当前环境ID（从Vuex获取）
     */
    currentEnvId() {
      return this.$store?.state?.envId || this.selectedEnvId;
    },

    /**
     * 当前环境信息
     */
    currentEnvInfo() {
      if (!this.currentEnvId || !this.testEnvs.length) return null;
      return this.testEnvs.find(env => env.id === this.currentEnvId);
    },

    /**
     * 当前环境名称
     */
    currentEnvName() {
      return this.currentEnvInfo?.name || '未选择环境';
    },

    /**
     * 是否已选择环境
     */
    hasSelectedEnv() {
      return !!this.currentEnvId;
    },

    /**
     * 环境选择选项
     */
    envOptions() {
      return this.testEnvs.map(env => ({
        label: env.name,
        value: env.id,
        disabled: env.status === 'disabled'
      }));
    }
  },

  methods: {
    /**
     * 获取测试环境列表
     * @param {string} projectId - 项目ID
     */
    async fetchTestEnvs(projectId = null) {
      try {
        const pid = projectId || this.currentProjectId;
        if (!pid) {
          console.warn('项目ID未定义，无法获取环境列表');
          return;
        }

        const response = await this.$api.getTestEnvs(pid);
        if (response.status === 200) {
          this.testEnvs = response.data || [];
          
          // 如果当前没有选择环境且有可用环境，选择第一个
          if (!this.currentEnvId && this.testEnvs.length > 0) {
            this.selectEnv(this.testEnvs[0].id);
          }
        }
      } catch (error) {
        console.error('获取环境列表失败:', error);
        this.$message({
          type: 'error',
          message: '获取环境列表失败',
          duration: 2000
        });
      }
    },

    /**
     * 获取环境详细信息
     * @param {string} envId - 环境ID
     * @param {string} projectId - 项目ID
     */
    async fetchEnvInfo(envId = null, projectId = null) {
      try {
        const eid = envId || this.currentEnvId;
        const pid = projectId || this.currentProjectId;
        
        if (!eid || !pid) {
          console.warn('环境ID或项目ID未定义');
          return null;
        }

        const response = await this.$api.getEnvInfo(eid, pid);
        if (response.status === 200) {
          this.envInfo = response.data || {};
          return this.envInfo;
        }
      } catch (error) {
        console.error('获取环境信息失败:', error);
        this.$message({
          type: 'error',
          message: '获取环境信息失败',
          duration: 2000
        });
      }
      return null;
    },

    /**
     * 选择环境
     * @param {string} envId - 环境ID
     */
    selectEnv(envId) {
      this.selectedEnvId = envId;
      
      // 更新Vuex状态（如果有）
      if (this.$store && this.$store.commit) {
        this.$store.commit('selectEnv', envId);
      }
      
      // 触发环境选择事件
      this.$emit('env-selected', envId);
      
      // 自动获取环境详情
      if (envId) {
        this.fetchEnvInfo(envId);
      }
    },

    /**
     * 显示环境详情
     * @param {string} envId - 环境ID（可选）
     */
    async showEnvDetails(envId = null) {
      const eid = envId || this.currentEnvId;
      if (!eid) {
        this.$message({
          type: 'warning',
          message: '请先选择环境',
          duration: 2000
        });
        return;
      }

      // 获取环境详情
      await this.fetchEnvInfo(eid);
      
      // 显示环境详情对话框
      this.showEnvDialog = true;
    },

    /**
     * 关闭环境详情对话框
     */
    closeEnvDialog() {
      this.showEnvDialog = false;
    },

    /**
     * 编辑环境配置
     * @param {object} envInfo - 环境信息
     */
    editEnvConfig(envInfo = null) {
      const info = envInfo || this.envInfo;
      if (!info) {
        this.$message({
          type: 'warning',
          message: '环境信息不存在',
          duration: 2000
        });
        return;
      }

      // 存储环境信息到Vuex（如果有）
      if (this.$store && this.$store.commit) {
        this.$store.commit('selectEnvInfo', info);
      }

      // 跳转到环境配置页面
      this.$router.push({ name: 'testenv' });
    },

    /**
     * 验证环境配置
     * @param {object} envInfo - 环境信息
     * @returns {object} 验证结果
     */
    validateEnvConfig(envInfo = null) {
      const info = envInfo || this.envInfo;
      const errors = [];

      if (!info) {
        errors.push('环境信息不存在');
        return { isValid: false, errors };
      }

      // 检查基本配置
      if (!info.name) {
        errors.push('环境名称未配置');
      }

      // 检查全局变量
      if (!info.global_variable || Object.keys(info.global_variable).length === 0) {
        errors.push('全局变量未配置');
      }

      // 检查基础URL
      const globalVars = info.global_variable || {};
      const baseUrl = globalVars.base_url || globalVars.baseUrl;
      if (!baseUrl) {
        errors.push('基础URL未配置');
      }

      return {
        isValid: errors.length === 0,
        errors
      };
    },

    /**
     * 格式化环境变量显示
     * @param {object} variables - 变量对象
     * @param {string} type - 变量类型
     * @returns {Array}
     */
    formatEnvVariables(variables = {}, type = 'global') {
      if (!variables || typeof variables !== 'object') return [];

      return Object.entries(variables).map(([key, value]) => ({
        key,
        value,
        type,
        displayValue: this.formatVariableValue(value)
      }));
    },

    /**
     * 格式化变量值显示
     * @param {any} value - 变量值
     * @returns {string}
     */
    formatVariableValue(value) {
      if (value === null || value === undefined) return '';
      if (typeof value === 'object') return JSON.stringify(value);
      if (typeof value === 'string' && value.length > 50) {
        return value.substring(0, 50) + '...';
      }
      return String(value);
    },

    /**
     * 获取环境变量
     * @param {string} key - 变量键
     * @param {any} defaultValue - 默认值
     * @returns {any}
     */
    getEnvVariable(key, defaultValue = null) {
      if (!this.envInfo) return defaultValue;

      // 优先从debug变量获取
      if (this.envInfo.debug_global_variable && this.envInfo.debug_global_variable[key] !== undefined) {
        return this.envInfo.debug_global_variable[key];
      }

      // 从全局变量获取
      if (this.envInfo.global_variable && this.envInfo.global_variable[key] !== undefined) {
        return this.envInfo.global_variable[key];
      }

      return defaultValue;
    },

    /**
     * 检查环境是否可用
     * @param {string} envId - 环境ID
     * @returns {boolean}
     */
    isEnvAvailable(envId = null) {
      const eid = envId || this.currentEnvId;
      if (!eid || !this.testEnvs.length) return false;

      const env = this.testEnvs.find(e => e.id === eid);
      return env && env.status !== 'disabled';
    },

    /**
     * 获取环境状态标签类型
     * @param {string} status - 环境状态
     * @returns {string}
     */
    getEnvStatusType(status) {
      const statusMap = {
        active: 'success',
        inactive: 'warning',
        disabled: 'danger',
        pending: 'info'
      };
      return statusMap[status] || 'info';
    }
  },

  // 生命周期钩子
  async created() {
    // 自动获取环境列表
    if (this.currentProjectId) {
      await this.fetchTestEnvs();
    }
  },

  watch: {
    // 监听项目变化，重新获取环境列表
    currentProjectId: {
      handler(newProjectId) {
        if (newProjectId) {
          this.fetchTestEnvs(newProjectId);
        }
      },
      immediate: false
    }
  }
};