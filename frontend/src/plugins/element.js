import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn' // 新版路径
import * as ElementPlusIconsVue from '@element-plus/icons-vue' // 直接导入

export default (app) => {
  app.use(ElementPlus, {
    locale: zhCn,
    size: 'default'
  })

  // 注册全局图标组件
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
}