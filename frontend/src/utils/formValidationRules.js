/**
 * 通用表单验证规则
 */

// 基础验证规则
export const baseRules = {
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  
  description: [
    { required: false, message: '请输入描述', trigger: 'blur' },
    { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
  ],

  url: [
    { required: true, message: '请输入接口地址', trigger: 'blur' },
    { 
      pattern: /^(https?:\/\/)?([\w\-]+\.)+[\w\-]+(\/[\w\-._~:/?#[\]@!$&'()*+,;=]*)?$/,
      message: '请输入有效的URL地址',
      trigger: 'blur'
    }
  ],

  ip: [
    { required: true, message: '请输入IP地址', trigger: 'blur' },
    {
      pattern: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
      message: '请输入有效的IP地址',
      trigger: 'blur'
    }
  ],

  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    {
      pattern: /^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/,
      message: '请输入有效的端口号(1-65535)',
      trigger: 'blur'
    }
  ],

  required: [
    { required: true, message: '此字段为必填项', trigger: 'blur' }
  ],

  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],

  positiveNumber: [
    { required: true, message: '请输入正数', trigger: 'blur' },
    {
      pattern: /^[1-9]\d*$/,
      message: '请输入大于0的整数',
      trigger: 'blur'
    }
  ],

  nonNegativeNumber: [
    { required: true, message: '请输入非负数', trigger: 'blur' },
    {
      pattern: /^\d+$/,
      message: '请输入大于等于0的整数',
      trigger: 'blur'
    }
  ]
};

// 服务器相关验证规则
export const serverRules = {
  name: baseRules.name,
  host_ip: [
    { required: true, message: '请输入服务器IP', trigger: 'blur' },
    ...baseRules.ip.slice(1)
  ],
  host_port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    ...baseRules.port.slice(1)
  ],
  sys_user_name: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  sys_user_passwd: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
  ]
};

// 任务相关验证规则
export const taskRules = {
  taskName: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  desc: [
    { required: false, message: '请输入任务描述', trigger: 'blur' },
    { max: 500, message: '长度不能超过 500 个字符', trigger: 'blur' }
  ]
};

// 场景相关验证规则
export const sceneRules = {
  name: [
    { required: true, message: '请输入场景名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
};

// 接口相关验证规则
export const apiRules = {
  name: baseRules.name,
  url: baseRules.url,
  method: [
    { required: true, message: '请选择请求方法', trigger: 'change' }
  ]
};

// 循环控制器验证规则
export const loopRules = {
  cycleIndex: [
    { required: true, message: '请输入循环次数', trigger: 'blur' },
    ...baseRules.positiveNumber.slice(1)
  ],
  variable: [
    { required: true, message: '请输入变量', trigger: 'blur' },
    {
      pattern: /^{{.*}}$/,
      message: '变量格式应为 {{variable_name}}',
      trigger: 'blur'
    }
  ],
  variableName: [
    { required: true, message: '请输入变量名', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
      message: '变量名只能包含字母、数字和下划线，且不能以数字开头',
      trigger: 'blur'
    }
  ]
};

// 生成特定字段的验证规则
export function createFieldRules(fieldType, options = {}) {
  const rules = [...(baseRules[fieldType] || [])];
  
  if (options.required !== undefined) {
    if (options.required && !rules.some(rule => rule.required)) {
      rules.unshift({ required: true, message: `请输入${options.label || '内容'}`, trigger: 'blur' });
    } else if (!options.required) {
      const requiredIndex = rules.findIndex(rule => rule.required);
      if (requiredIndex > -1) {
        rules.splice(requiredIndex, 1);
      }
    }
  }
  
  if (options.min || options.max) {
    rules.push({
      min: options.min || 0,
      max: options.max || 255,
      message: `长度在 ${options.min || 0} 到 ${options.max || 255} 个字符`,
      trigger: 'blur'
    });
  }
  
  return rules;
}