<template>
  <div class="bug-dashboard">
    <el-scrollbar height="calc(100vh - 55px)">
      <div class="dashboard-container">
        <!-- 顶部区域 -->
        <div class="dashboard-header">
          <div class="header-left">
            <h1 class="page-title">Bug 管理</h1>
          </div>
          <div class="header-right">
            <el-badge :value="bugCounts.pending" :hidden="bugCounts.pending === 0" class="pending-badge">
              <el-button type="danger" size="large" class="action-btn">
                <el-icon><Warning /></el-icon>
                <span>待处理 {{ bugCounts.pending }}</span>
              </el-button>
            </el-badge>
          </div>
        </div>

        <!-- 统计卡片区 -->
        <div class="stats-section">
          <el-row :gutter="20">
            <el-col :span="4" v-for="(item, index) in statCards" :key="index">
              <div class="stat-card" :class="`stat-card-${item.type}`">
                <div class="stat-icon">
                  <el-icon>
                    <component :is="item.icon" />
                  </el-icon>
                </div>
                <div class="stat-data">
                  <div class="stat-value">{{ item.value }}</div>
                  <div class="stat-label">{{ item.label }}</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 图表区域 -->
        <div class="charts-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="chart-wrapper">
                <div class="chart-header">
                  <h3>Bug 分布</h3>
                  <div class="chart-actions">
                    <el-button size="small" text>
                      <el-icon><Refresh /></el-icon>
                    </el-button>
                  </div>
                </div>
                <div class="chart-content" ref="chart1Box"></div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="chart-wrapper">
                <div class="chart-header">
                  <h3>状态占比</h3>
                  <div class="chart-actions">
                    <el-button size="small" text>
                      <el-icon><Refresh /></el-icon>
                    </el-button>
                  </div>
                </div>
                <div class="chart-content" ref="chart2Box"></div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- Bug列表区域 -->
        <div class="bug-list-section">
          <div class="list-header">
            <div class="title-section">
              <h2>BUG 列表</h2>
              <span class="bug-counter">共 {{ bugs.length }} 项</span>
            </div>

            <div class="filter-section">
              <el-radio-group v-model="currentFilter" size="large" @change="(val) => filterBugs(val)">
                <el-radio-button label="all">全部</el-radio-button>
                <el-radio-button label="pending">待处理</el-radio-button>
                <el-radio-button label="inProgress">处理中</el-radio-button>
                <el-radio-button label="completed">已完成</el-radio-button>
                <el-radio-button label="unnecessary">无需处理</el-radio-button>
                <el-radio-button label="closed">已关闭</el-radio-button>
              </el-radio-group>

              <el-input
                v-model="searchQuery"
                placeholder="搜索Bug..."
                class="search-input"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
          </div>

          <el-table
            :data="displayBugs"
            style="width: 100%"
            row-key="id"
            v-loading="tableLoading"
            :row-class-name="getRowClass"
            @row-click="showBugInfo"
            class="bug-table"
          >
            <el-table-column type="expand">
              <template #default="props">
                <div class="bug-expand-detail">
                  <p><strong>Bug描述:</strong> {{ props.row.desc }}</p>
                  <p><strong>所属接口:</strong> {{ props.row.interface_url }}</p>
                  <p><strong>提交时间:</strong> {{ rTime(props.row.create_time) }}</p>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="" width="50">
              <template #default="scope">
                <div class="bug-status-dot" :class="'bug-status-' + getStatusClass(scope.row.status)"></div>
              </template>
            </el-table-column>

            <el-table-column prop="id" label="接口名称"   />

            <el-table-column prop="desc" label="Bug描述" show-overflow-tooltip>
              <template #default="scope">
                <div class="bug-title">{{ scope.row.desc }}</div>
              </template>
            </el-table-column>

            <el-table-column prop="interface_url" label="所属接口" min-width="150" show-overflow-tooltip />

            <el-table-column label="提交时间" >
              <template #default="scope">
                <div class="time-info">
                  <el-icon><Time /></el-icon>
                  <span>{{ rTime(scope.row.create_time) }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="状态" width="120" align="center">
              <template #default="scope">
                <el-tag
                  :type="getStatusType(scope.row.status)"
                  effect="light"
                  class="status-tag"
                  :class="'status-' + getStatusClass(scope.row.status)"
                >
                  <span>{{ scope.row.status }}</span>
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column width="140" align="center" fixed="right" label="操作">
              <template #default="scope">
                <div class="action-buttons">
                  <el-tooltip content="查看详情" placement="top">
                    <el-button
                      circle
                      type="primary"
                      plain
                      @click.stop="showBugInfo(scope.row)"
                    >
                      <el-icon><View /></el-icon>
                    </el-button>
                  </el-tooltip>

                  <el-tooltip content="更新状态" placement="top">
                    <el-button
                      circle
                      type="warning"
                      plain
                      @click.stop="openUpdateDialog(scope.row)"
                    >
                      <el-icon><Edit /></el-icon>
                    </el-button>
                  </el-tooltip>

                  <el-tooltip content="删除Bug" placement="top">
                    <el-popconfirm
                      title="确定要删除此Bug吗?"
                      confirm-button-text="确定"
                      cancel-button-text="取消"
                      @confirm="deleteBug(scope.row.id)"
                    >
                      <template #reference>
                        <el-button
                          circle
                          type="danger"
                          plain
                          @click.stop
                        >
                          <el-icon><Delete /></el-icon>
                        </el-button>
                      </template>
                    </el-popconfirm>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 30, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="filteredBugs.length"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>

      <!-- Bug详情抽屉 -->
      <el-drawer
        v-model="bugDetailVisible"
        direction="rtl"
        size="55%"
        destroy-on-close
        custom-class="bug-detail-drawer"
      >
        <template #header>
          <div class="drawer-header">
            <h2 class="drawer-title">Bug详情</h2>
            <el-tag
              v-if="selectedBug"
              :type="getStatusType(selectedBug.status)"
              effect="dark"
              class="drawer-status-tag"
            >
              {{ selectedBug.status }}
            </el-tag>
          </div>
        </template>

        <el-scrollbar height="calc(100vh - 120px)">
          <div v-if="selectedBug" class="bug-detail-content">
            <el-card class="detail-card info-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <h3><el-icon><InfoFilled /></el-icon> 基本信息</h3>
                </div>
              </template>

              <el-descriptions :column="2" border>
                <el-descriptions-item label="Bug ID">{{ selectedBug.id }}</el-descriptions-item>
                <el-descriptions-item label="提交者">admin</el-descriptions-item>
                <el-descriptions-item label="所属接口">{{ selectedBug.interface_url }}</el-descriptions-item>
                <el-descriptions-item label="提交时间">{{ rTime(selectedBug.create_time) }}</el-descriptions-item>
                <el-descriptions-item label="Bug描述" :span="2">
                  <div class="bug-description">{{ selectedBug.desc }}</div>
                </el-descriptions-item>
              </el-descriptions>
            </el-card>

            <el-card class="detail-card test-info-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <h3><el-icon><Tickets /></el-icon> 用例执行信息</h3>
                </div>
              </template>
              <Result :result="selectedBug.info" :showbtn="false"></Result>
            </el-card>

            <el-card v-if="bugLogs && bugLogs.length > 0" class="detail-card timeline-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <h3><el-icon><Tickets /></el-icon> 处理记录</h3>
                </div>
              </template>

              <el-timeline>
                <el-timeline-item
                  v-for="(activity, index) in bugLogs"
                  :key="index"
                  :timestamp="rDate(activity.create_time)"
                  :type="getTimelineType(activity.handle)"
                  :hollow="true"
                  :size="index === 0 ? 'large' : 'normal'"
                >
                  <el-card class="timeline-item-card">
                    <h4 class="activity-title">
                      <el-icon><component :is="getActivityIcon(activity.handle)" /></el-icon>
                      {{ activity.handle }}
                    </h4>

                    <p v-if="activity.remark" class="activity-remark">{{ activity.remark }}</p>

                    <div class="activity-footer">
                      <span class="operator">
                        <el-icon><User /></el-icon>
                        {{ activity.update_user }}
                      </span>
                      <span class="time">
                        <el-icon><Time /></el-icon>
                        {{ rTime(activity.create_time) }}
                      </span>
                    </div>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </el-card>

            <el-empty v-else description="暂无处理记录" :image-size="200"></el-empty>

            <div class="drawer-actions">
              <el-button size="default" @click="bugDetailVisible = false">关闭</el-button>
              <el-button size="default" type="primary" @click="openUpdateDialog(selectedBug)">
                <el-icon><Edit /></el-icon> 更新状态
              </el-button>
            </div>
          </div>
        </el-scrollbar>
      </el-drawer>

      <!-- 更新Bug状态对话框 -->
      <el-dialog
        v-model="updateDialogVisible"
        title="更新Bug状态"
        width="50%"
        destroy-on-close
        custom-class="update-dialog"
      >
        <el-form :model="updateForm" label-position="top" status-icon class="update-form">
          <el-form-item label="选择状态">
            <div class="status-options">
              <div
                v-for="(status, key) in statusOptions"
                :key="key"
                class="status-option"
                :class="{ 'active': updateForm.status === status.value }"
                @click="updateForm.status = status.value"
              >
                <el-icon class="option-icon">
                  <component :is="status.icon" />
                </el-icon>
                <div class="option-label">{{ status.label }}</div>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="处理备注">
            <el-input
              v-model="updateForm.remark"
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 6 }"
              placeholder="请输入处理备注..."
              class="remark-input"
            />
          </el-form-item>
        </el-form>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="updateDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="updateBug" :loading="updateLoading">
              <el-icon><Check /></el-icon> 确认更新
            </el-button>
          </div>
        </template>
      </el-dialog>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick, reactive, getCurrentInstance } from 'vue'
import { useStore } from 'vuex'
import Result from '../components/common/caseResult.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Warning, View, Edit, Delete, User, Tickets,
  Loading, CircleCheck, WarningFilled, CircleClose, 
  Remove, Search, Refresh, Check, InfoFilled
} from '@element-plus/icons-vue'

// 获取当前实例，在 setup 阶段保存代理对象
const { proxy } = getCurrentInstance() || { proxy: null }
// 获取工具函数
const tools = proxy?.$tools || {}

// Store
const store = useStore()
const project = computed(() => store.state.pro)

// 数据
const bugs = ref([])
const selectedBug = ref(null)
const bugLogs = ref(null)
const filteredBugs = ref([])
const searchQuery = ref('')
const currentFilter = ref('all')
const tableLoading = ref(false)
const bugDetailVisible = ref(false)
const updateDialogVisible = ref(false)
const updateLoading = ref(false)
const chart1Box = ref(null)
const chart2Box = ref(null)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// Bug状态映射
const STATUS_MAP = {
  '待处理': { type: 'danger', icon: 'Warning', class: 'pending' },
  '处理中': { type: 'warning', icon: 'Loading', class: 'in-progress' },
  '处理完成': { type: 'success', icon: 'CircleCheck', class: 'completed' },
  '无需处理': { type: 'info', icon: 'Remove', class: 'unnecessary' },
  '已关闭': { type: 'info', icon: 'CircleClose', class: 'closed' }
}

// 状态选项
const statusOptions = [
  { label: '待处理', value: '待处理', icon: 'Warning' },
  { label: '处理中', value: '处理中', icon: 'Loading' },
  { label: '处理完成', value: '处理完成', icon: 'CircleCheck' },
  { label: '无需处理', value: '无需处理', icon: 'Remove' },
  { label: '已关闭', value: '已关闭', icon: 'CircleClose' }
]

// 表单数据
const updateForm = ref({
  id: '',
  status: '',
  remark: ''
})

// 计算属性
const bugsByStatus = computed(() => {
  return {
    pending: bugs.value.filter(bug => bug.status === '待处理'),
    inProgress: bugs.value.filter(bug => bug.status === '处理中'),
    completed: bugs.value.filter(bug => bug.status === '处理完成'),
    unnecessary: bugs.value.filter(bug => bug.status === '无需处理'),
    closed: bugs.value.filter(bug => bug.status === '已关闭')
  }
})

const bugCounts = computed(() => {
  return {
    total: bugs.value.length,
    pending: bugsByStatus.value.pending.length,
    inProgress: bugsByStatus.value.inProgress.length,
    completed: bugsByStatus.value.completed.length,
    unnecessary: bugsByStatus.value.unnecessary.length,
    closed: bugsByStatus.value.closed.length
  }
})

// 统计卡片数据
const statCards = computed(() => [
  { type: 'total', icon: 'Tickets', value: bugCounts.value.total, label: 'Bug总数' },
  { type: 'pending', icon: 'Warning', value: bugCounts.value.pending, label: '待处理' },
  { type: 'in-progress', icon: 'Loading', value: bugCounts.value.inProgress, label: '处理中' },
  { type: 'completed', icon: 'CircleCheck', value: bugCounts.value.completed, label: '已完成' },
  { type: 'unnecessary', icon: 'Remove', value: bugCounts.value.unnecessary, label: '无需处理' },
  { type: 'closed', icon: 'CircleClose', value: bugCounts.value.closed, label: '已关闭' }
])

// 搜索过滤
const searchFilteredBugs = computed(() => {
  if (!searchQuery.value) return filteredBugs.value

  const query = searchQuery.value.toLowerCase()
  return filteredBugs.value.filter(bug =>
    bug.desc.toLowerCase().includes(query) ||
    bug.interface_url.toLowerCase().includes(query) ||
    String(bug.id).includes(query)
  )
})

// 分页展示的Bug
const displayBugs = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  const endIndex = startIndex + pageSize.value
  return searchFilteredBugs.value.slice(startIndex, endIndex)
})

// 方法
const fetchBugs = async () => {
  if (!project.value?.id) return

  tableLoading.value = true
  try {
    if (!proxy || !proxy.$api) {
      ElMessage.error('API 初始化失败')
      return
    }

    const response = await proxy.$api.getBugs({
      project: project.value.id
    })

    if (response.status === 200) {
      bugs.value = response.data
      filterBugs(currentFilter.value)
    }
  } catch (error) {
    ElMessage.error('获取Bug列表失败')
    console.error('Failed to fetch bugs:', error)
  } finally {
    tableLoading.value = false
  }
}

const filterBugs = (filter) => {
  currentFilter.value = filter
  currentPage.value = 1 // 重置分页

  switch (filter) {
    case 'pending':
      filteredBugs.value = bugsByStatus.value.pending
      break
    case 'inProgress':
      filteredBugs.value = bugsByStatus.value.inProgress
      break
    case 'completed':
      filteredBugs.value = bugsByStatus.value.completed
      break
    case 'unnecessary':
      filteredBugs.value = bugsByStatus.value.unnecessary
      break
    case 'closed':
      filteredBugs.value = bugsByStatus.value.closed
      break
    default:
      filteredBugs.value = bugs.value
  }
}

const showBugInfo = async (bug) => {
  selectedBug.value = bug
  bugDetailVisible.value = true

  try {
    if (!proxy || !proxy.$api) return

    const response = await proxy.$api.getBugLogs({ bug: bug.id })
    if (response.status === 200 && response.data.length > 0) {
      bugLogs.value = response.data
    } else {
      bugLogs.value = []
    }
  } catch (error) {
    ElMessage.error('获取Bug处理记录失败')
    console.error('Failed to fetch bug logs:', error)
    bugLogs.value = []
  }
}

const openUpdateDialog = (bug) => {
  updateForm.value = {
    id: bug.id,
    status: bug.status,
    remark: ''
  }
  updateDialogVisible.value = true
}

const updateBug = async () => {
  if (!updateForm.value.id) return

  if (!updateForm.value.remark.trim()) {
    ElMessage.warning('请输入处理备注')
    return
  }

  updateLoading.value = true
  try {
    if (!proxy || !proxy.$api) return

    const response = await proxy.$api.updateBug(updateForm.value.id, updateForm.value)

    if (response.status === 200) {
      ElMessage.success({
        message: 'Bug状态更新成功',
        type: 'success',
        duration: 2000
      })
      updateDialogVisible.value = false

      // 刷新数据
      await fetchBugs()

      // 如果详情抽屉打开，刷新日志
      if (bugDetailVisible.value && selectedBug.value) {
        const logs = await proxy.$api.getBugLogs({ bug: selectedBug.value.id })
        if (logs.status === 200) {
          bugLogs.value = logs.data

          // 更新选中的Bug信息
          const updatedBug = bugs.value.find(b => b.id === selectedBug.value.id)
          if (updatedBug) {
            selectedBug.value = updatedBug
          }
        }
      }
    }
  } catch (error) {
    ElMessage.error('更新Bug状态失败')
    console.error('Failed to update bug:', error)
  } finally {
    updateLoading.value = false
  }
}

const deleteBug = async (id) => {
  tableLoading.value = true
  try {
    if (!proxy || !proxy.$api) return

    const response = await proxy.$api.deleteBug(id)

    if (response.status === 204) {
      ElMessage.success({
        message: '删除成功',
        type: 'success',
        duration: 2000
      })
      await fetchBugs()

      // 如果删除的Bug正在显示详情，关闭抽屉
      if (selectedBug.value && selectedBug.value.id === id) {
        bugDetailVisible.value = false
      }
    }
  } catch (error) {
    ElMessage.error('删除Bug失败')
    console.error('Failed to delete bug:', error)
  } finally {
    tableLoading.value = false
  }
}

const renderCharts = () => {
  if (!chart1Box.value || !chart2Box.value) return
  if (!proxy || !proxy.$chart) return

  // 准备图表数据
  const chartData = [
    bugs.value.length,
    bugsByStatus.value.completed.length,
    bugsByStatus.value.inProgress.length,
    bugsByStatus.value.pending.length,
    bugsByStatus.value.unnecessary.length,
    bugsByStatus.value.closed.length
  ]

  const chartLabels = ['Bug总数', '处理完成', '处理中', '待处理', '无需处理', '已关闭']

  // 渲染柱状图
  proxy.$chart.chart1(chart1Box.value, chartData, chartLabels)

  // 渲染饼图
  proxy.$chart.chart2(chart2Box.value, [
    { value: bugsByStatus.value.completed.length, name: '处理完成' },
    { value: bugsByStatus.value.inProgress.length, name: '处理中' },
    { value: bugsByStatus.value.pending.length, name: '待处理' },
    { value: bugsByStatus.value.unnecessary.length, name: '无需处理' },
    { value: bugsByStatus.value.closed.length, name: '已关闭' }
  ])
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 辅助函数
const getStatusType = (status) => {
  return STATUS_MAP[status]?.type || 'info'
}

const getStatusIcon = (status) => {
  return STATUS_MAP[status]?.icon || 'InfoFilled'
}

const getStatusClass = (status) => {
  return STATUS_MAP[status]?.class || 'default'
}

const getTimelineType = (handle) => {
  if (handle.includes('处理完成')) return 'success'
  if (handle.includes('处理中')) return 'warning'
  if (handle.includes('待处理')) return 'danger'
  if (handle.includes('关闭')) return 'info'
  return 'primary'
}

const getActivityIcon = (handle) => {
  if (handle.includes('处理完成')) return 'CircleCheck'
  if (handle.includes('处理中')) return 'Loading'
  if (handle.includes('待处理')) return 'Warning'
  if (handle.includes('关闭')) return 'CircleClose'
  if (handle.includes('无需处理')) return 'Remove'
  return 'InfoFilled'
}

const getRowClass = ({ row }) => {
  return `bug-row bug-status-${getStatusClass(row.status)}`
}

// 生命周期钩子
onMounted(async () => {
  await fetchBugs()
  nextTick(() => {
    renderCharts()
  })
})

// 监听bugs变化更新图表
watch(() => bugs.value, () => {
  nextTick(() => {
    renderCharts()
  })
}, { deep: true })

// 监听搜索，重置分页
watch(searchQuery, () => {
  currentPage.value = 1
})

// 在模板中使用 $tools.rTime 的地方，替换为我们的本地函数
const rTime = (time) => {
  if (!proxy || !proxy.$tools) return time
  return proxy.$tools.rTime(time)
}

// 在模板中使用 $tools.rDate 的地方，替换为我们的本地函数
const rDate = (time) => {
  if (!proxy || !proxy.$tools) return time
  return proxy.$tools.rDate ? proxy.$tools.rDate(time) : proxy.$tools.rTime(time)
}
</script>

<style scoped>
/* 整体样式 */
.bug-dashboard {
  height: 100%;
  background-color: #f8fafc;
  color: #334155;
}

.dashboard-container {
  padding: 24px;
}

/* 头部区域 */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.header-left {
  display: flex;
  flex-direction: column;
}

.page-title {
  margin: 0;
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
  background: linear-gradient(90deg, #3b82f6, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.sub-title {
  color: #64748b;
  font-size: 16px;
}

.pending-badge :deep(.el-badge__content) {
  background-color: #ef4444;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  font-weight: 600;
}

/* 统计卡片区域 */
.stats-section {
  margin-bottom: 32px;
}

.stat-card {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100px;
  overflow: hidden;
  position: relative;
  border-left: 5px solid #3b82f6;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.stat-card-total {
  border-left-color: #3b82f6;
}

.stat-card-pending {
  border-left-color: #ef4444;
}

.stat-card-in-progress {
  border-left-color: #f59e0b;
}

.stat-card-completed {
  border-left-color: #10b981;
}

.stat-card-unnecessary {
  border-left-color: #64748b;
}

.stat-card-closed {
  border-left-color: #475569;
}

.stat-icon {
  background: rgba(59, 130, 246, 0.1);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-right: 16px;
  flex-shrink: 0;
}

.stat-card-total .stat-icon {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.stat-card-pending .stat-icon {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.stat-card-in-progress .stat-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.stat-card-completed .stat-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.stat-card-unnecessary .stat-icon {
  background: rgba(100, 116, 139, 0.1);
  color: #64748b;
}

.stat-card-closed .stat-icon {
  background: rgba(71, 85, 105, 0.1);
  color: #475569;
}

.stat-icon :deep(.el-icon) {
  font-size: 24px;
}

.stat-data {
  flex-grow: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 32px;
}

.chart-wrapper {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  height: 360px;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.chart-content {
  flex-grow: 1;
  width: 100%;
}

/* Bug列表区域 */
.bug-list-section {
  margin-bottom: 32px;
}

.list-header {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 16px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-section h2 {
  margin: 0;
  font-size: 24px;
  color: #1e293b;
  font-weight: 600;
}

.bug-counter {
  background-color: #e2e8f0;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  color: #475569;
  font-weight: 500;
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.search-input {
  width: 300px;
}

/* Bug表格 */
.bug-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.bug-table :deep(th.el-table__cell) {
  background-color: #f1f5f9;
  color: #475569;
  font-weight: 600;
}

.bug-table :deep(.el-table__row) {
  transition: all 0.2s ease;
}

.bug-table :deep(.el-table__row:hover) {
  background-color: #f8fafc;
}

/* 确保表格内容垂直居中 */
.bug-table :deep(.el-table__cell) {
  vertical-align: middle;
}

/* 确保固定列正确显示 */
.bug-table :deep(.el-table__fixed-right) {
  height: auto !important;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.05);
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .bug-table {
    width: 100%;
    overflow-x: auto;
  }
  
  .action-buttons {
    justify-content: center;
  }

  /* 固定列样式优化 */
  .bug-table :deep(.el-table__fixed-right) {
    right: 0 !important;
  }
  
  /* 确保按钮在小屏幕上间距合理且对齐 */
  .action-buttons :deep(.el-button) {
    margin: 0;
    height: 30px;
    width: 30px;
  }
}

/* 超小屏幕设备 */
@media screen and (max-width: 480px) {
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .action-buttons :deep(.el-button) {
    height: 28px;
    width: 28px;
  }
}

.bug-status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin: 0 auto;
}

.bug-status-pending {
  background-color: #ef4444;
}

.bug-status-in-progress {
  background-color: #f59e0b;
}

.bug-status-completed {
  background-color: #10b981;
}

.bug-status-unnecessary {
  background-color: #64748b;
}

.bug-status-closed {
  background-color: #475569;
}

.bug-row {
  cursor: pointer;
}

.bug-row.bug-status-pending:hover {
  background-color: rgba(239, 68, 68, 0.05) !important;
}

.bug-row.bug-status-in-progress:hover {
  background-color: rgba(245, 158, 11, 0.05) !important;
}

.bug-row.bug-status-completed:hover {
  background-color: rgba(16, 185, 129, 0.05) !important;
}

.bug-row.bug-status-unnecessary:hover {
  background-color: rgba(100, 116, 139, 0.05) !important;
}

.bug-row.bug-status-closed:hover {
  background-color: rgba(71, 85, 105, 0.05) !important;
}

.bug-title {
  font-weight: 500;
  color: #1e293b;
}

.time-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #64748b;
}

.status-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  min-width: 90px;
  font-weight: 600;
}

.status-tag :deep(.el-icon) {
  font-size: 14px;
  display: flex;
  align-items: center;
}

.status-pending {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
}

.status-in-progress {
  color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
}

.status-completed {
  color: #10b981;
  background-color: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.2);
}

.status-unnecessary {
  color: #64748b;
  background-color: rgba(100, 116, 139, 0.1);
  border-color: rgba(100, 116, 139, 0.2);
}

.status-closed {
  color: #475569;
  background-color: rgba(71, 85, 105, 0.1);
  border-color: rgba(71, 85, 105, 0.2);
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.action-buttons :deep(.el-button) {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  height: 32px;
  width: 32px;
  position: relative;
}

.action-buttons :deep(.el-button .el-icon) {
  font-size: 16px;
  margin: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 确保Popconfirm组件内的按钮也正确对齐 */
:deep(.el-popconfirm__action) {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 12px;
}

.bug-expand-detail {
  padding: 16px;
  background-color: #f8fafc;
  border-radius: 8px;
  margin: 0 20px 20px;
}

.bug-expand-detail p {
  margin: 8px 0;
  line-height: 1.6;
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
}

/* Bug详情抽屉 */
:deep(.bug-detail-drawer .el-drawer__header) {
  margin-bottom: 0;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.drawer-title {
  margin: 0;
  font-size: 24px;
  color: #1e293b;
  font-weight: 600;
}

.drawer-status-tag {
  font-size: 14px;
  font-weight: 600;
  padding: 6px 12px;
}

.bug-detail-content {
  padding: 24px;
}

.detail-card {
  margin-bottom: 24px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 6px;
}

.card-header :deep(.el-icon) {
  font-size: 18px;
}

.bug-description {
  line-height: 1.6;
  color: #475569;
}

.timeline-item-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.activity-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.activity-title :deep(.el-icon) {
  font-size: 18px;
}

.activity-remark {
  color: #475569;
  line-height: 1.6;
  margin: 12px 0;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 6px;
  font-size: 14px;
}

.activity-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  font-size: 14px;
  color: #64748b;
}

.operator {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #3b82f6;
  font-weight: 500;
}

.time {
  display: flex;
  align-items: center;
  gap: 6px;
}

.drawer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
}

.drawer-actions :deep(.el-button .el-icon) {
  margin-right: 4px;
  font-size: 16px;
}

/* 状态更新对话框 */
:deep(.update-dialog .el-dialog__header) {
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
}

:deep(.update-dialog .el-dialog__body) {
  padding: 24px;
}

.update-form {
  margin-top: 12px;
}

.status-options {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 20px;
  width: 100%;
}

.status-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
  width: 100%;
  background-color: #f8fafc;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.status-option:hover {
  background-color: #f1f5f9;
}

.status-option.active {
  background-color: #eff6ff;
  border-color: #3b82f6;
}

.option-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.option-label {
  font-weight: 500;
  font-size: 14px;
}

.remark-input {
  margin-top: 8px;
}

:deep(.el-textarea__inner) {
  padding: 12px;
  line-height: 1.6;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.dialog-footer :deep(.el-button .el-icon) {
  margin-right: 4px;
  font-size: 16px;
}

/* Element Plus 图标样式修复 */
:deep(.el-icon) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 针对Element Plus按钮的特殊处理 */
:deep(.el-button.is-circle) {
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-buttons :deep(.el-button .el-icon) {
  font-size: 16px;
  margin: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1em;
  height: 1em;
}
</style>
