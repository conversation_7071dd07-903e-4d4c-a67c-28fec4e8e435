<template>
	<div class="cron-container">
		<!-- 页面标题区域 -->
		<div class="cron-header">
			<div class="title-container">
				<h2 class="page-title">定时任务管理</h2>
				<p class="page-subtitle">创建和管理自动化测试任务</p>
			</div>
			<el-button type="primary" class="add-task-btn" @click='addDialog=true'>
				<el-icon><Plus /></el-icon> 添加定时任务
			</el-button>
		</div>

		<!-- 统计卡片区域 -->
		<div class="stats-cards" v-if="cronList">
			<div class="stat-card">
				<div class="stat-value">{{ cronList.length }}</div>
				<div class="stat-label">总任务数</div>
			</div>
			<div class="stat-card">
				<div class="stat-value">{{ cronList.filter(item => item.status).length }}</div>
				<div class="stat-label">运行中任务</div>
			</div>
			<div class="stat-card">
				<div class="stat-value">{{ cronList.filter(item => !item.status).length }}</div>
				<div class="stat-label">暂停任务</div>
			</div>
		</div>

		<!-- 表格区域 -->
		<div class="table-container">
			<el-card shadow="hover" class="cron-table-card">
				<template #header>
					<div class="table-header">
						<span class="table-title">定时任务列表</span>
						<div class="table-actions">
							<el-tooltip content="刷新数据" placement="top">
								<el-button type="primary" text :icon="Refresh" circle @click="getAllCron"></el-button>
							</el-tooltip>
						</div>
					</div>
				</template>

				<el-table 
					:data="cronList" 
					style="width: 100%" 
					size="default" 
					empty-text="暂无数据"
					border
					stripe
					highlight-current-row
					class="cron-table">
					<el-table-column label="序号" align="center" width="60">
						<template #default="scope">
							<span class="row-index">{{ scope.$index + 1 }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="name" label="名称" align="center"></el-table-column>
					<el-table-column prop="plan_name" label="执行任务" align="center"></el-table-column>
					<el-table-column prop="env_name" label="执行环境" align="center"></el-table-column>
					<el-table-column prop="rule" label="时间配置" align="center">
						<template #default="scope">
							<el-tag size="small" type="info">{{ scope.row.rule }}</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="创建时间" align="center">
						<template #default="scope">
							<span class="time-info">{{ $tools.rTime(scope.row.create_time) }}</span>
						</template>
					</el-table-column>
					<el-table-column label="状态" align="center" width="100">
						<template #default="scope">
							<el-switch 
								@change='switchCronStatus(scope.row)' 
								v-model="scope.row.status" 
								active-color="#13ce66" 
								inactive-color="#b1b1b1">
							</el-switch>
						</template>
					</el-table-column>
					<el-table-column label="操作" align="center" width="200">
						<template #default="scope">
							<div class="action-buttons">
								<el-button 
									@click='showUpdateCronDlg(scope.row)' 
									type="primary" 
									size="small">
									<el-icon><Edit /></el-icon>
									编辑
								</el-button>
								<el-button 
									@click="delCron(scope.row.id)" 
									type="danger" 
									size="small">
									<el-icon><Delete /></el-icon>
									删除
								</el-button>
							</div>
						</template>
					</el-table-column>
				</el-table>
			</el-card>
		</div>

		<!-- 创建定时任务的窗口 -->
		<el-dialog 
			v-model="addDialog" 
			width="50%" 
			title="新增定时执行任务" 
			:before-close="closeDialogCron" 
			custom-class="modern-dialog"
			destroy-on-close>
			<el-tabs type="border-card" v-model="currentTab" class="modern-tabs">
				<el-tab-pane label="测试计划自动运行" name="first">
					<el-form 
						:model="cronTabData" 
						:rules="rulescronTab" 
						ref="cronTabRef" 
						label-width="90px"
						class="modern-form">
						<el-form-item label="任务名称" prop="name">
							<el-input 
								v-model="cronTabData.name" 
								placeholder="请输入任务名称">
								<template #prefix>
									<el-icon><Notebook /></el-icon>
								</template>
							</el-input>
						</el-form-item>
						<el-form-item label="测试环境" prop="env">
							<el-select 
								v-model="cronTabData.env" 
								placeholder="请选择环境" 
								style="width: 100%;" 
								no-data-text="暂无数据">
								<el-option 
									v-for="item in testEnvs" 
									:key="item.id" 
									:label="item.name" 
									:value="item.id">
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="时间配置" prop="rule">
							<el-popover
								v-model:visible="cronVisible"
								placement="bottom-start"
								:width="650"
								trigger="manual"
								:teleported="false"
								:append-to-body="false"
								popper-class="cron-popover"
								:popper-style="{ minHeight: '320px' }">
								<template #reference>
									<el-input
										v-model="cronTabData.rule"
										clearable
										readonly
										placeholder="请选择时间配置"
										@click.stop="openCronPopover('first')">
										<template #prefix>
											<el-icon><Timer /></el-icon>
										</template>
									</el-input>
								</template>
								<timerTaskCron
									:runTimeStr="cronTabData.rule"
									@closeTime="handleCloseTime"
									@runTime="handleRunTime">
								</timerTaskCron>
							</el-popover>
						</el-form-item>
						<el-form-item label="执行计划" prop="plan">
							<el-select 
								style="width: 100%;" 
								v-model="cronTabData.plan" 
								placeholder="请选择" 
								no-data-text="暂无数据">
								<el-option
									v-for="item in testPlans"
									:key="item.id"
									:label="item.name"
									:value="item.id">
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="是否开启">
							<el-switch
								v-model="cronTabData.status"
								active-color="#13ce66"
								inactive-color="#c3c3c3"
								active-text="开启"
								inactive-text="关闭">
							</el-switch>
						</el-form-item>
					</el-form>
					<div class="dialog-footer">
						<el-button @click="closeDialogCron" plain>取 消</el-button>
						<el-button type="primary" @click="createCron(currentTab)">确 定</el-button>
					</div>
				</el-tab-pane>
				<el-tab-pane label="YApi自动导入" name="second">
					<el-form 
						:model="cronTabData" 
						:rules="rulescronTab" 
						ref="cronTabRef" 
						label-width="100px"
						class="modern-form">
						<el-form-item label="任务名称" prop="name">
							<el-input 
								v-model="cronTabData.name" 
								placeholder="请输入任务名称">
								<template #prefix>
									<el-icon><Notebook /></el-icon>
								</template>
							</el-input>
						</el-form-item>
						<el-form-item label="时间配置" prop="rule">
							<el-popover
								v-model:visible="cronVisibleYApi"
								placement="bottom-start"
								:width="650"
								trigger="manual"
								:teleported="false"
								:append-to-body="false"
								popper-class="cron-popover"
								:popper-style="{ minHeight: '320px' }">
								<template #reference>
									<el-input
										v-model="cronTabData.rule"
										clearable
										readonly
										placeholder="请选择时间配置"
										@click.stop="openCronPopover('second')">
										<template #prefix>
											<el-icon><Timer /></el-icon>
										</template>
									</el-input>
								</template>
								<timerTaskCron
									:runTimeStr="cronTabData.rule"
									@closeTime="handleCloseTime"
									@runTime="handleRunTime">
								</timerTaskCron>
							</el-popover>
						</el-form-item>
						<el-divider content-position="left">YApi平台配置</el-divider>
						<el-form-item label="平台地址" prop='url'>
							<el-input 
								v-model="cronTabData.yapi.url" 
								placeholder="请输入YApi平台项目地址" 
								clearable>
								<template #prefix>
									<el-icon><Link /></el-icon>
								</template>
							</el-input>
						</el-form-item>
						<el-form-item label="平台TOKEN" prop='token'>
							<el-input 
								v-model="cronTabData.yapi.token" 
								placeholder="请输入YApi平台项目token" 
								clearable 
								show-password>
								<template #prefix>
									<el-icon><Key /></el-icon>
								</template>
							</el-input>
						</el-form-item>
						<el-form-item label="平台项目ID" prop='YApiId'>
							<el-input 
								v-model="cronTabData.yapi.YApiId" 
								placeholder="请输入YApi平台项目id" 
								clearable>
								<template #prefix>
									<el-icon><InfoFilled /></el-icon>
								</template>
							</el-input>
						</el-form-item>
						<el-form-item label="节点/模块" prop='treenode'>
							<el-cascader
								v-model="cronTabData.yapi.treenode"
								:options="treeOptions"
								:props="{label:'name', value:'id',checkStrictly: true}"
								@change="removeCascaderAriaOwns"
								@visible-change="removeCascaderAriaOwns"
								@expand-change="removeCascaderAriaOwns"
								clearable
								collapse-tags
								filterable
								placeholder="请选择节点/模块" />
						</el-form-item>
						<el-form-item label="是否开启">
							<el-switch
								v-model="cronTabData.status"
								active-color="#13ce66"
								inactive-color="#c3c3c3"
								active-text="开启"
								inactive-text="关闭">
							</el-switch>
						</el-form-item>
					</el-form>
					<div class="dialog-footer">
						<el-button @click="closeDialogCron" plain>取 消</el-button>
						<el-button type="primary" @click="createCron(currentTab)">确 定</el-button>
					</div>
				</el-tab-pane>
			</el-tabs>
		</el-dialog>

		<!-- 修改定时任务的窗口 -->
		<el-dialog 
			v-model="editDialog" 
			width="50%" 
			title="修改定时执行任务" 
			:before-close="closeDialogCron" 
			custom-class="modern-dialog"
			destroy-on-close>
			<el-form 
				v-if="cronTabData.type===10" 
				:model="cronTabData" 
				:rules="rulescronTab" 
				ref="cronTabRef" 
				label-width="90px"
				class="modern-form">
				<el-form-item label="名称" prop="name">
					<el-input 
						v-model="cronTabData.name">
						<template #prefix>
							<el-icon><Notebook /></el-icon>
						</template>
					</el-input>
				</el-form-item>
				<el-form-item label="测试环境" prop="env">
					<el-select 
						v-model="cronTabData.env" 
						placeholder="请选择环境" 
						style="width: 100%;" 
						no-data-text="暂无数据">
						<el-option 
							v-for="item in testEnvs" 
							:key="item.id" 
							:label="item.name" 
							:value="item.id">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="时间配置">
					<el-popover
						v-model:visible="cronVisibleEdit"
						placement="bottom-start"
						:width="650"
						trigger="manual"
						:teleported="false"
						:append-to-body="false"
						popper-class="cron-popover"
						:popper-style="{ minHeight: '320px' }">
						<template #reference>
							<el-input
								v-model="cronTabData.rule"
								clearable
								readonly
								placeholder="请选择时间"
								@click.stop="openCronPopover(10)">
								<template #prefix>
									<el-icon><Timer /></el-icon>
								</template>
							</el-input>
						</template>
						<timerTaskCron
							:runTimeStr="cronTabData.rule"
							@closeTime="handleCloseTime"
							@runTime="handleRunTime">
						</timerTaskCron>
					</el-popover>
				</el-form-item>
				<el-form-item label="执行计划" prop="plan">
					<el-select 
						style="width: 100%;" 
						v-model="cronTabData.plan" 
						placeholder="请选择" 
						no-data-text="暂无数据">
						<el-option
							v-for="item in testPlans"
							:key="item.id"
							:label="item.name"
							:value="item.id">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="是否开启">
					<el-switch
						v-model="cronTabData.status"
						active-color="#13ce66"
						inactive-color="#c3c3c3"
						active-text="开启"
						inactive-text="关闭">
					</el-switch>
				</el-form-item>
			</el-form>
			<el-form 
				v-else 
				:model="cronTabData" 
				:rules="rulescronTab" 
				ref="cronTabRef" 
				label-width="100px"
				class="modern-form">
				<el-form-item label="任务名称" prop="name">
					<el-input 
						v-model="cronTabData.name" 
						placeholder="请输入任务名称">
						<template #prefix>
							<el-icon><Notebook /></el-icon>
						</template>
					</el-input>
				</el-form-item>
				<el-form-item label="时间配置" prop="rule">
					<el-popover
						v-model:visible="cronVisibleYApiEdit"
						placement="bottom-start"
						:width="650"
						trigger="manual"
						:teleported="false"
						:append-to-body="false"
						popper-class="cron-popover"
						:popper-style="{ minHeight: '320px' }">
						<template #reference>
							<el-input
								v-model="cronTabData.rule"
								clearable
								readonly
								placeholder="请选择时间配置"
								@click.stop="openCronPopover(20)">
								<template #prefix>
									<el-icon><Timer /></el-icon>
								</template>
							</el-input>
						</template>
						<timerTaskCron
							:runTimeStr="cronTabData.rule"
							@closeTime="handleCloseTime"
							@runTime="handleRunTime">
						</timerTaskCron>
					</el-popover>
				</el-form-item>
				<el-divider content-position="left">YApi平台配置</el-divider>
				<el-form-item label="平台地址" prop='url'>
					<el-input 
						v-model="cronTabData.yapi.url" 
						placeholder="请输入YApi平台项目地址" 
						clearable>
						<template #prefix>
							<el-icon><Link /></el-icon>
						</template>
					</el-input>
				</el-form-item>
				<el-form-item label="平台TOKEN" prop='token'>
					<el-input 
						v-model="cronTabData.yapi.token" 
						placeholder="请输入YApi平台项目token" 
						clearable 
						show-password>
						<template #prefix>
							<el-icon><Key /></el-icon>
						</template>
					</el-input>
				</el-form-item>
				<el-form-item label="平台项目ID" prop='YApiId'>
					<el-input 
						v-model="cronTabData.yapi.YApiId" 
						placeholder="请输入YApi平台项目id" 
						clearable>
						<template #prefix>
							<el-icon><InfoFilled /></el-icon>
						</template>
					</el-input>
				</el-form-item>
				<el-form-item label="节点/模块" prop='treenode'>
					<el-cascader
						v-model="cronTabData.yapi.treenode"
						:options="treeOptions"
						:props="{label:'name', value:'id',checkStrictly: true}"
						@change="removeCascaderAriaOwns"
						@visible-change="removeCascaderAriaOwns"
						@expand-change="removeCascaderAriaOwns"
						clearable
						collapse-tags
						filterable
						placeholder="请选择节点/模块" />
				</el-form-item>
				<el-form-item label="是否开启">
					<el-switch
						v-model="cronTabData.status"
						active-color="#13ce66"
						inactive-color="#c3c3c3"
						active-text="开启"
						inactive-text="关闭">
					</el-switch>
				</el-form-item>
			</el-form>
			<div class="dialog-footer">
				<el-button @click="closeDialogCron" plain>取 消</el-button>
				<el-button type="primary" @click="UpdateCron">提交修改</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import timerTaskCron from '../components/common/timerTaskCron';
import { mapState } from 'vuex';
import { Plus, Edit, Delete, Refresh, Notebook, Timer, Link, Key, InfoFilled } from '@element-plus/icons-vue';

export default{
	data(){
		return{
     currentTab:'first',
     treeOptions:[],
     cronVisible: false,
     cronVisibleYApi: false,
     cronVisibleEdit: false,
     cronVisibleYApiEdit: false,
     Refresh,
     rulescronTab: {
				name: [
					{
						required: true,
						message: '请输入名称',
						trigger: 'blur'
					}
				],
				env: [
					{
						required: true,
						message: '请选择环境',
						trigger: 'blur'
					}
				],
        plan: [
					{
						required: true,
						message: '请选择执行计划',
						trigger: 'blur'
					}
				],
			},
			// 定时任务列表
			cronList:null,
			editDialog:false,
			addDialog:false,
			// 添加定时任务
			cronTabData:{
				name: "",
				status: true,
				plan: null,
				env:null,
        rule: "",
        yapi:{
          token:'',
          YApiId:null,
          treenode:null,
          format:'list',
          project:null,
          url:'http://121.37.2.117:8081'
        },
        type:''
			  },
		}
	},
  components: {
	  timerTaskCron,
    Edit,
    Delete,
    Refresh,
    Notebook,
    Timer,
    Link,
    Key,
    InfoFilled
  },

	computed:{
		...mapState(['pro','testEnvs','testPlans'])
	},
	methods:{
    // 打开时间配置弹窗
    openCronPopover(type) {
      // 阻止事件冒泡
      event && event.stopPropagation();
      
      // 先关闭所有弹窗
      this.cronVisible = false;
      this.cronVisibleYApi = false;
      this.cronVisibleEdit = false;
      this.cronVisibleYApiEdit = false;
      
      // 根据类型打开对应弹窗
      setTimeout(() => {
        if (type === 'first') {
          this.cronVisible = true;
        }
        else if (type === 'second') {
          this.cronVisibleYApi = true;
        }
        else if (type === 10) {
          this.cronVisibleEdit = true;
        }
        else if (type === 20) {
          this.cronVisibleYApiEdit = true;
        }
        else {
          console.error('未知的值:', type);
        }
        
        // 重置其他弹窗相关的状态
        this.$nextTick(() => {
          // 确保弹窗内容正确渲染
          document.querySelectorAll('.cron-popover').forEach(el => {
            if (el) el.style.overflow = 'visible';
          });
        });
      }, 100);
      
      // 添加全局点击事件，用于关闭弹窗
      document.removeEventListener('click', this.handleDocumentClick); // 先移除避免重复
      document.addEventListener('click', this.handleDocumentClick);
    },
    
    // 处理全局点击事件
    handleDocumentClick(e) {
      // 如果点击的是时间选择器相关元素，不做处理
      if (e.target.closest('.el-time-panel') || 
          e.target.closest('.el-picker-panel') ||
          e.target.closest('.time-picker-popper') ||
          e.target.closest('.cron-wrapper') ||
          e.target.closest('.el-popover') ||
          e.target.closest('.cron-popover')) {
        return;
      }
      
      // 关闭所有弹窗
      this.closeRunTimeCron();
    },
    
    // 旧方法，保留兼容性
    cronFun(value) {
      console.log("cronFun调用", value);
    },
    
    // 处理关闭时间选择器弹窗
    handleCloseTime(isClose) {
      if (isClose) {
        this.closeRunTimeCron();
      }
    },
    
    // 处理设置时间
    handleRunTime(cron) {
      // 设置时间
      this.cronTabData.rule = cron;
      this.$message({
        type: 'success',
        message: '时间配置已设置',
        duration: 2000
      });
      // 设置后延迟关闭
      setTimeout(() => {
        this.closeRunTimeCron();
      }, 500);
    },
    
    // 关闭所有时间选择器弹窗
    closeRunTimeCron() {
      // 移除全局点击事件
      document.removeEventListener('click', this.handleDocumentClick);
      
      setTimeout(() => {
        this.cronVisible = false;
        this.cronVisibleYApi = false;
        this.cronVisibleEdit = false;
        this.cronVisibleYApiEdit = false;
      }, 300); // 增加延迟时间，避免和时间选择器的点击事件冲突
    },
    
    runTimeCron(cron) {
      this.cronTabData.rule = cron;
    },

		async getAllCron(){
			const response =await  this.$api.getCrons(this.pro.id)
			if (response.status ===200){
				this.cronList = response.data
			}
		},
		delCron(id) {
			this.$confirm('此操作将永久删除该定时任务, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(async () => {
					// 删除定时任务
					const response = await this.$api.delCron(id)
					if(response.status ===204){
						this.$message({
							type: 'success',
							message: '删除成功!'
						});
						// 刷新页面定时任务
						this.getAllCron()
					}
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消删除'
					});
				});
		},
		// 任务开启和关闭
		async switchCronStatus(cron){
			const response = await this.$api.updateCron(cron.id, cron)
			if (response.status === 200) {
				if (cron.status == true) {
					this.$message({
						type: 'success',
						message: '定时运行已开启',
						duration: 1000
					})
				} else {
					this.$message({
						type: 'warning',
						message: '定时运行已关闭',
						duration: 1000
					})
				}
			} else {
				this.$message({
					type: 'error',
					message: '修改状态失败',
					duration: 1000
				})
			}
		},
    // 取消按钮时重置输入信息
    closeDialogCron() {
      this.editDialog = false;
      this.addDialog = false;
      this.cronTabData = {
            name: "",
            rule: "",
            status: true,
            plan: null,
            env: null,
            yapi:{
              token:'',
              YApiId:null,
              treenode:null,
              format:'list',
              project:null,
              url:'http://121.37.2.117:8081'
            },
            type:null
          };
      this.$refs.cronTabRef.clearValidate();
      },


		// 添加定时任务
		async createCron(currentTab){
        if (currentTab==='first') {
          delete this.cronTabData.yapi;
          const params = {
            ...this.cronTabData,
            type:10,
            project:this.pro.id
          }
          const response = await this.$api.createCron(params)
            if (response.status ===201){
              this.$message({
                type: 'success',
                message: '定时任务添加成功',
                duration: 1000
              })
              this.closeDialogCron();
              this.getAllCron()
            }

        }
        else if(currentTab==='second'){
          let params = { ...this.cronTabData.yapi};
          params.project = this.pro.id;
          // 获取最后一个节点的id
          if (params.treenode && params.treenode.length > 0) {
            const lastValue = params.treenode[params.treenode.length - 1];  // 获取最后一个值
            params.treenode = lastValue
          }
          const data = {
            ...this.cronTabData,
            project:this.pro.id,
            yapi:params,
            type:20,
          }
          console.log(data)
          const response = await this.$api.createCron(data)
            if (response.status ===201){
              this.$message({
                type: 'success',
                message: '定时任务添加成功',
                duration: 1000
              })
              this.closeDialogCron();
              this.getAllCron()
            }

        }
        else {
          console.log('待完善')
        }
		},
		//显示修改定时任务的窗口
		showUpdateCronDlg(cron){
			this.cronTabData = JSON.parse(JSON.stringify(cron));
			this.editDialog = true
		},
		// 修改定时任务
		async UpdateCron(){
      let params = { ...this.cronTabData.yapi};
      params.project = this.pro.id;
      // 获取最后一个节点的id
      if (params.treenode && params.treenode.length > 0) {
        const lastValue = params.treenode[params.treenode.length - 1];  // 获取最后一个值
        params.treenode = lastValue
      }
      const data = {
            ...this.cronTabData,
            yapi:params
          }
      console.log(data)
			const response = await this.$api.updateCron(this.cronTabData.id,data)
			if (response.status ===200){
				this.$message({
					type: 'success',
					message: '修改成功',
					duration: 1000
				})
        this.closeDialogCron();
				this.getAllCron()
			}
		},
    // 解决el-cascader组件页面卡顿问题
    removeCascaderAriaOwns() {
      this.$nextTick(() => {
        const $el = document.querySelectorAll(
                '.el-cascader-panel .el-cascader-node[aria-owns]'
        );
        Array.from($el).map(item => item.removeAttribute('aria-owns'));
      });
        },
    // 树结构列表接口
    async allTree() {
      const response = await this.$api.getTreeNode()
      if (response.status === 200) {
        this.treeOptions = response.data.result}
     },

	},
	created() {
		this.getAllCron();
		this.allTree()
	},
  beforeUnmount() {
    // 移除全局事件监听器
    document.removeEventListener('click', this.handleDocumentClick);
  }
}	
	
</script>

<style scoped>
.cron-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.cron-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.title-container {
  display: flex;
  flex-direction: column;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-subtitle {
  margin: 5px 0 0 0;
  font-size: 14px;
  color: #909399;
}

.add-task-btn {
  padding: 12px 20px;
  font-weight: 500;
}

.stats-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 25px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 15px 20px;
  flex: 1;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.table-container {
  margin-bottom: 20px;
}

.cron-table-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.cron-table {
  margin-top: 10px;
}

.row-index {
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  background-color: #f2f6fc;
  color: #606266;
  border-radius: 4px;
}

.time-info {
  color: #606266;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.modern-dialog {
  border-radius: 8px;
  overflow: hidden;
}

.modern-tabs {
  border-radius: 8px;
  box-shadow: none;
  border: none;
}

.modern-form {
  padding: 15px 0;
}

.dialog-footer {
  margin-top: 20px;
  text-align: center;
}

.el-divider__text {
  font-size: 15px;
  font-weight: 600;
  color: #606266;
}

/* 弹窗样式修复 */
:deep(.cron-popover) {
  max-width: 700px !important;
  min-width: 650px !important;
  min-height: 320px !important;
  max-height: none !important;
  overflow: visible !important;
}

:deep(.el-popover.el-popper) {
  min-width: 650px !important;
  min-height: 320px !important;
  overflow: visible !important;
  padding: 0 !important;
}

:deep(.footer) {
  margin-top: 15px;
  text-align: right;
}

:deep(.footer .el-button) {
  margin-left: 10px;
}

:deep(.el-time-panel) {
  position: absolute !important;
  z-index: 10000 !important;
}

:deep(.radio-container) {
  width: 580px;
  margin: 0 auto;
  max-height: 200px !important;
  overflow-y: auto !important;
}

:deep(.el-radio-group) {
  width: 100%;
}

/* 修复时间选择器的嵌套层级问题 */
:deep(.el-tabs__content) {
  overflow: visible !important;
}

:deep(.el-tabs__content .el-tab-pane) {
  overflow: visible !important;
}

:deep(.el-form-item) {
  overflow: visible !important;
}
</style>
