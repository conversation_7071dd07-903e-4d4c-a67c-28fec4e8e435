<template>
	<LoginBack>
		<div class="login_box">
			<div class="logo_box"><img src="../assets/images/logo.png" /></div>
        <el-tabs v-model="activeName" class="login-tabs">
          <el-tab-pane label="登 录" name="first">
            <el-form ref="loginRef" class="login_from" :model="loginForm" :rules="rulesLogin">
              <el-form-item prop="username" class="custom-form-item">
                <el-input 
                  size="large" 
                  v-model="loginForm.username" 
                  prefix-icon="User" 
                  placeholder="请输入账号"
                  class="custom-input"
                ></el-input>
              </el-form-item>
              <el-form-item prop="password" class="custom-form-item">
                <el-input 
                  type="password" 
                  size="large" 
                  v-model="loginForm.password" 
                  placeholder="请输入密码" 
                  prefix-icon="Lock" 
                  show-password
                  class="custom-input"
                ></el-input>
              </el-form-item>
              <div class="login-options">
                  <el-checkbox v-model="status" class="remember-checkbox">记住用户</el-checkbox>
                  <div class="register-link">
                      没有账号?
                      <span @click="clickRegister(activeName)" class="action-link">去注册</span>
                  </div>
              </div>
              <!-- 按钮 -->
              <el-form-item>
                <el-button size="large" type="primary" class="login-btn" @click="login">登 录</el-button>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          <el-tab-pane label="注 册" name="second">
            <el-form class="login_from" :model="createForm">
              <el-form-item class="custom-form-item">
                <el-input 
                  clearable 
                  :readonly="readonlyInput" 
                  @focus="cancelReadOnly" 
                  size="large" 
                  v-model="createForm.username" 
                  prefix-icon="User" 
                  placeholder="请输入账号"
                  class="custom-input"
                ></el-input>
              </el-form-item>
              <el-form-item class="custom-form-item">
                <el-input 
                  clearable 
                  :readonly="readonlyInput" 
                  @focus="cancelReadOnly"  
                  type="password" 
                  size="large" 
                  v-model="createForm.password" 
                  placeholder="请输入密码" 
                  prefix-icon="Lock" 
                  show-password
                  class="custom-input"
                ></el-input>
              </el-form-item>
              <div class="login-options">
                  <el-checkbox v-model="status" class="remember-checkbox">记住用户</el-checkbox>
                  <div class="register-link">
                      已有账号?
                      <span @click="clickRegister(activeName)" class="action-link">去登录</span>
                  </div>
              </div>
              <el-form-item>
                <el-button size="large" type="primary" class="login-btn" @click="createClick">注 册</el-button>
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
		</div>
	</LoginBack>
</template>

<script type="text/javascript">
import LoginBack from '../components/common/LoginBack.vue';
import {ElNotification} from "element-plus";
export default {
	components: {
		LoginBack
	},
	data() {
    return {
			// 登录的数据对象
			loginForm: {
				username: '',
				password: ''
			},
      createForm: {
        username: '',
				password: '',
        project_id: 1,
        weChat_name: ''
      },
			status: false,
      readonlyInput: true,
			rulesLogin: {
				// 验证用户名是否合法
				username: [
					{
						required: true,
						message: '请输入登录账号',
						trigger: 'blur'
					}
				],
				// 验证密码是否合法
				password: [
					{
						required: true,
						message: '请输入登录密码',
						trigger: 'blur'
					}
				]
			},
      userIcon:[
        {id:1,Emojis:"streamline-emojis:amusing-face"},
        {id:2,Emojis:"streamline-emojis:amazed-face"},
        {id:3,Emojis:"streamline-emojis:anxious-face"},
        {id:4,Emojis:"streamline-emojis:rolling-on-the-floor-laughing-1"},
        {id:5,Emojis:"streamline-emojis:beaming-face-with-smiling-eyes"},
        {id:6,Emojis:"streamline-emojis:astonished-face"},
        {id:7,Emojis:"streamline-emojis:face-screaming-in-fear"},
        {id:8,Emojis:"streamline-emojis:face-with-raised-eyebrow"},
        {id:9,Emojis:"streamline-emojis:face-with-rolling-eyes"},
        {id:10,Emojis:"streamline-emojis:face-with-tongue"},
        {id:11,Emojis:"streamline-emojis:face-without-mouth"},
        {id:12,Emojis:"streamline-emojis:drooling-face-1"},
        {id:13,Emojis:"streamline-emojis:grimacing-face"},
        {id:14,Emojis:"streamline-emojis:grinning-face-with-sweat"},
        {id:15,Emojis:"streamline-emojis:face-blowing-a-kiss"},
        {id:16,Emojis:"streamline-emojis:hushed-face-2"},
        {id:17,Emojis:"streamline-emojis:lying-face"},
        {id:18,Emojis:"streamline-emojis:star-struck-1"},
        {id:19,Emojis:"streamline-emojis:winking-face"},
        {id:20,Emojis:"streamline-emojis:upside-down-face"}
      ],
      activeName: 'first'
		};
	},
	methods: {
		clickRegister(activeName) {
		  if (activeName ==='first') {
        this.activeName = 'second'
      }else {
		    this.activeName = 'first'
      }
		},
    cancelReadOnly() {
      this.readonlyInput = false;
    },
    async createClick() {
        const params = {...this.createForm}
        if (params.weChat_name === '') {params.weChat_name = params.username}
        const response = await this.$api.createUser(params)
        if (response.status===201) {
          ElNotification({
              duration: 1000,
              title: '创建成功，可以登录咯',
              type: 'success',
            })
          this.activeName = 'first'
          this.createForm = {
              username: '',
              password: '',
              project_id: 1,
              weChat_name: ''
            };
        }
    },
    userAvatar() {
      const randomIndex = Math.floor(Math.random() * this.userIcon.length);
      const selectedEmojis = this.userIcon[randomIndex];
      window.sessionStorage.setItem('avatar', selectedEmojis.Emojis);

    },
		// 登录的方法
		login() {
			// 通过表单的validate方法来验证表单，验证的结果会传递到validate的回调函数中
			this.$refs.loginRef.validate(async vaild => {
				if (!vaild) return;
				// 发送请求
				const response = await this.$api.login(this.loginForm);
				// 判断是否登录失败
				if (response.status != 200) return;
				const result = response.data;
        ElNotification({
              duration: 1000,
              title: '登录成功',
              type: 'success',
            })
				// 2、获取token,保存到客户端的sessionStorage中
        // 保存用户头像到sessionStorage
        this.userAvatar()
				window.sessionStorage.setItem('token', result.token);
				window.sessionStorage.setItem('username', this.loginForm.username);
				if (this.status) {
					window.localStorage.setItem('userinfo', JSON.stringify(this.loginForm));
				} else {
					window.localStorage.removeItem('userinfo');
				}
				// 3、通过编程式导航跳转到登录之后的页面中
				this.$router.push({ name: 'allProject' })
			});
		}
	},
	mounted() {
		const userinfo = window.localStorage.getItem('userinfo');
		if (userinfo) {
			this.loginForm = JSON.parse(userinfo);
			this.status = true;
		}
	}
};
</script>

<style scoped>
	/* 登录框的样式 */
	.login_box {
		color: #fff;
		width: 500px;
		margin: 0 auto;
    /* 移除背景色，使用更微妙的透明效果 */
    background-color: rgba(0, 0, 0, 0.2); 
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(8px);
    /* 使用flexbox居中布局 */
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 1px solid rgba(255, 255, 255, 0.08);
	}

	/* logo居中 */
	.logo_box {
    margin-top: 0;
		text-align: center;
    height: 100px;
    margin-bottom: 20px;
	}

  /* 自定义表单项 */
  .custom-form-item {
    margin-bottom: 20px;
  }

  /* 全局强制覆盖Element Plus的输入框样式 */
  :deep(.el-input__wrapper) {
    background-color: transparent !important;
    box-shadow: none !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  /* 自定义输入框 */
  .custom-input :deep(.el-input__wrapper) {
    background-color: transparent !important;
    border-radius: 8px;
    box-shadow: none !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    padding: 12px 15px;
    transition: all 0.3s;
  }

  /* 当输入框有值时强制保持透明背景 */
  .custom-input :deep(.el-input__wrapper.is-focus),
  .custom-input :deep(.el-input__wrapper:hover),
  .custom-input :deep(.el-input__wrapper.is-focus) {
    border-color: #1296db !important;
    box-shadow: 0 0 0 1px rgba(18, 150, 219, 0.3) !important;
    background-color: transparent !important;
  }

  /* 对已填写内容的输入框强制应用透明背景 */
  .custom-input :deep(.is-filled .el-input__wrapper) {
    background-color: transparent !important;
  }

  /* Element Plus在验证后会给输入框添加额外类，确保这些状态下也保持透明 */
  .custom-input :deep(.is-success .el-input__wrapper),
  .custom-input :deep(.is-error .el-input__wrapper),
  .custom-input :deep(.is-validating .el-input__wrapper) {
    background-color: transparent !important;
  }

  .custom-input :deep(.el-input__inner) {
    color: #fff !important;
    height: 40px;
    background-color: transparent !important;
  }

  /* 确保输入后文字显示为白色 */
  .custom-input :deep(input) {
    color: #fff !important;
    background-color: transparent !important;
  }

  .custom-input :deep(.el-input__prefix) {
    color: rgba(255, 255, 255, 0.7);
    font-size: 18px;
  }
  
  /* 解决密码输入框图标的问题 */
  .custom-input :deep(.el-input__suffix) {
    color: rgba(255, 255, 255, 0.7);
  }
  
  /* 选项区域 */
  .login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  /* 记住用户复选框 */
  .remember-checkbox {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
  }
  
  .remember-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: #1296db;
    border-color: #1296db;
  }
  
  .remember-checkbox :deep(.el-checkbox__inner) {
    background-color: transparent;
    border-color: rgba(255, 255, 255, 0.5);
  }

  .register-link {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
  }

  .action-link {
    color: #1296db;
    cursor: pointer;
    font-weight: bold;
    margin-left: 5px;
    position: relative;
    transition: all 0.3s;
  }

  .action-link:hover {
    color: #39aae4;
    text-decoration: underline;
  }

  /* 登录按钮 */
  .login-btn {
    width: 100%;
    height: 45px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 2px;
    background: linear-gradient(90deg, #1296db, #2a88d4);
    border: none;
    transition: all 0.3s;
  }

  .login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(18, 150, 219, 0.3);
    background: linear-gradient(90deg, #17a7f0, #3b99e5);
  }

  /* 标签页样式 */
  .login-tabs :deep(.el-tabs__nav) {
    width: 100%;
    display: flex;
  }

  .login-tabs :deep(.el-tabs__item) {
    flex: 1;
    text-align: center;
    height: 45px;
    line-height: 45px;
    font-size: 18px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s;
  }

  .login-tabs :deep(.el-tabs__item.is-active) {
    color: #fff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  }

  .login-tabs :deep(.el-tabs__active-bar) {
    background-color: #1296db;
    height: 3px;
    border-radius: 3px;
  }
</style>
