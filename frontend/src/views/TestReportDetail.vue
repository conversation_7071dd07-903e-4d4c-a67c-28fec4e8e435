<!-- 这是一个临时测试页面，用于测试报告详情页面的路由 -->
<template>
  <div style="padding: 20px;">
    <h3>性能报告详情页面测试</h3>
    <p>请输入报告ID进行测试：</p>
    <el-input 
      v-model="testReportId" 
      placeholder="输入报告ID" 
      style="width: 200px; margin-right: 10px;">
    </el-input>
    <el-button type="primary" @click="goToReportDetail">查看报告详情</el-button>
    
    <div style="margin-top: 20px;">
      <h4>测试链接：</h4>
      <ul>
        <li><a href="#/PerformanceResult-Detail/1">报告ID=1</a></li>
        <li><a href="#/PerformanceResult-Detail/2">报告ID=2</a></li>
        <li><a href="#/PerformanceResult-Detail/3">报告ID=3</a></li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TestReportDetail',
  data() {
    return {
      testReportId: '1'
    }
  },
  methods: {
    goToReportDetail() {
      if (!this.testReportId) {
        this.$message.error('请输入报告ID')
        return
      }
      this.$router.push({ 
        name: 'PerformanceResult-Detail', 
        params: { id: this.testReportId }
      })
    }
  }
}
</script>