<template>
  <div class="user-management">
      <!-- Page Header -->
      <div class="page-header">
        <div class="action-buttons">
          <el-button @click="clickAdd" type="primary" :icon="Plus">
            <span>新增用户</span>
          </el-button>
          <el-button @click="clickAddPro" type="success" :icon="UserFilled">
            <span>添加项目成员</span>
          </el-button>
        </div>
      </div>
      <el-scrollbar height="calc(100vh - 125px)">
      <!-- Search Card -->
      <el-card class="search-card">
        <template #header>
          <div class="card-header">
            <span class="header-title">
              <el-icon><Search /></el-icon>
              搜索条件
            </span>
          </div>
        </template>
        <el-form :model="QueryCondition" label-width="80px" label-position="left" inline class="search-form">
          <div class="search-inputs">
            <el-form-item label="用户名">
              <el-input v-model="QueryCondition.username" placeholder="请输入用户名" clearable>
                <template #prefix>
                  <el-icon><User /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="手机号">
              <el-input v-model="QueryCondition.mobile" placeholder="请输入手机号码" clearable>
                <template #prefix>
                  <el-icon><Iphone /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="邮箱">
              <el-input v-model="QueryCondition.email" placeholder="请输入邮箱" clearable>
                <template #prefix>
                  <el-icon><Message /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="所属项目">
              <el-input v-model="QueryCondition.project_name" placeholder="请输入项目名称" clearable>
                <template #prefix>
                  <el-icon><Folder /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </div>
          <el-form-item class="query-buttons">
            <div class="button-group">
              <el-button @click="resetForm" :icon="Refresh">重置</el-button>
              <el-button type="primary" @click="submitForm" :icon="Search">查询</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- User Table Card -->
      <el-card class="table-card">
        <template #header>
          <div class="card-header">
            <span class="header-title">
              <el-icon><List /></el-icon>
              用户列表
            </span>
            <span class="header-count" v-if="Pager.count">
              共 <el-tag type="info" effect="plain">{{ Pager.count }}</el-tag> 条记录
            </span>
          </div>
        </template>
        <el-table 
          :data="UserLsit" 
          stripe 
          border 
          v-loading="tableLoading"
          element-loading-text="加载中..."
          element-loading-background="rgba(255, 255, 255, 0.8)"
          empty-text="暂无数据"
          row-key="id"
          class="user-table"
          :header-cell-style="{background:'#f6f9fc',color:'#2c3e50'}"
        >
          <el-table-column label="序号" align="center" width="80">
            <template #default="scope">
              <el-tag type="info" effect="plain" class="index-tag">{{ scope.$index + 1 }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="用户名" prop="username" align="center" >
            <template #default="scope">
              <div class="username-cell">
                <el-avatar :size="28" :icon="User" class="user-avatar"></el-avatar>
                <span class="username-text">{{ scope.row.username }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="用户标签" prop="weChat_name" align="center">
            <template #default="scope">
              <el-tag effect="light" class="wechat-tag" v-if="scope.row.weChat_name">{{ scope.row.weChat_name }}</el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="手机号码" prop="mobile" align="center">
            <template #default="scope">
              <div class="mobile-cell">
                <el-icon><Iphone /></el-icon>
                <span>{{ scope.row.mobile || '-' }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="邮箱" prop="email" align="center" show-overflow-tooltip>
            <template #default="scope">
              <div class="email-cell">
                <el-icon><Message /></el-icon>
                <span>{{ scope.row.email || '-' }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="所属项目" show-overflow-tooltip align="center">
            <template #default="scope">
              <div class="project-tags">
                <el-tag
                  v-for="(project, index) in scope.row.project"
                  :key="index"
                  size="small"
                  effect="plain"
                  class="project-tag"
                >
                  <span>{{ project.name }}</span>
                </el-tag>
                <el-tag v-if="!scope.row.project || scope.row.project.length === 0" type="info" size="small" effect="plain">
                  暂无项目
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template #default="scope">
              <div class="action-column">
                <el-tooltip content="编辑用户" placement="top" :show-after="500">
                  <el-button 
                    @click="clickEdit(scope.row)" 
                    size="small" 
                    type="primary"
                    circle
                    plain
                    class="action-btn"
                  >编辑</el-button>
                </el-tooltip>
                <el-tooltip content="删除用户" placement="top" :show-after="500">
                  <el-button 
                    @click="delUser(scope.row.id)" 
                    size="small" 
                    type="danger"
                    circle
                    plain
                    class="action-btn"
                  >删除</el-button>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- Pagination -->
        <div class="pagination-container">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 30, 50, 100]"
            @size-change="sizes"
            @current-change="currentPages"
            :total="Pager.count"
            :current-page="Pager.current"
            :page-size="Pager.size || 10"
          ></el-pagination>
        </div>
      </el-card>

      <!-- Add User Dialog -->
      <el-dialog
        v-model="addDlg"
        title="新增用户"
        width="500px"
        destroy-on-close
        :close-on-click-modal="false"
        @closed="clearValidation"
      >
        <div class="dialog-content">
          <el-form :model="addForm" :rules="rulesUser" ref="UserRef" label-width="100px">
            <el-form-item prop="username" label="用户名">
              <el-input v-model="addForm.username" maxlength="18" minlength="3" placeholder="请输入用户名" show-word-limit>
                <template #prefix>
                  <el-icon><User /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="用户标签">
              <el-input v-model="addForm.weChat_name" maxlength="50" minlength="1" placeholder="请输入用户标签名称">
                <template #prefix>
                  <el-icon><Stamp /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="手机号码" prop="mobile">
              <el-input v-model="addForm.mobile" maxlength="11" minlength="11" placeholder="请输入手机号">
                <template #prefix>
                  <el-icon><Iphone /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="邮箱地址">
              <el-input v-model="addForm.email" placeholder="请输入邮箱地址" readonly onfocus="this.removeAttribute('readonly');">
                <template #prefix>
                  <el-icon><Message /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="所属项目" required>
              <el-input v-model="addForm.project_name" disabled>
                <template #prefix>
                  <el-icon><Folder /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="addForm.password"
                type="password"
                show-password
                maxlength="18"
                minlength="3"
                placeholder="请输入密码"
              >
                <template #prefix>
                  <el-icon><Lock /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="clearValidation">取消</el-button>
            <el-button type="primary" @click="AddInter" :loading="submitLoading">确定</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- Edit User Dialog -->
      <el-dialog
        v-model="editDlg"
        title="修改用户"
        width="500px"
        destroy-on-close
        :close-on-click-modal="false"
        @closed="clearValidation"
      >
        <div class="dialog-content">
          <el-form :model="editForm" :rules="rulesUser" ref="UserRef" label-width="100px">
            <el-form-item prop="username" label="用户名">
              <el-input v-model="editForm.username" maxlength="18" minlength="3" placeholder="请输入用户名" disabled>
                <template #prefix>
                  <el-icon><User /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="用户标签">
              <el-input v-model="editForm.weChat_name" maxlength="50" minlength="1" placeholder="请输入用户标签名称">
                <template #prefix>
                  <el-icon><Stamp /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="手机号码" prop="mobile">
              <el-input v-model="editForm.mobile" maxlength="11" minlength="11" placeholder="请输入手机号">
                <template #prefix>
                  <el-icon><Iphone /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="邮箱地址">
              <el-input
                v-model="editForm.email"
                placeholder="请输入邮箱地址"
                maxlength="30"
                readonly
                onfocus="this.removeAttribute('readonly');"
              >
                <template #prefix>
                  <el-icon><Message /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="所属项目" required>
              <el-input v-model="editForm.project_name" disabled>
                <template #prefix>
                  <el-icon><Folder /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item v-if="showResetPassword" label="新密码" prop="password">
              <el-input
                v-model="editForm.password"
                type="password"
                show-password
                maxlength="18"
                minlength="3"
                placeholder="请输入密码"
                readonly
                onfocus="this.removeAttribute('readonly');"
              >
                <template #prefix>
                  <el-icon><Lock /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="warning" @click="resetPassword" :icon="Key">
              {{ showResetPassword ? '取消修改密码' : '重置密码' }}
            </el-button>
            <el-button type="primary" @click="UpdateInter" :loading="submitLoading">确定</el-button>
            <el-button @click="clearValidation">取消</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- Add Project Member Dialog -->
      <el-dialog
        v-model="addProDlg"
        title="添加其他项目成员"
        width="500px"
        destroy-on-close
        :close-on-click-modal="false"
        @closed="clearValidation"
      >
        <div class="dialog-content">
          <el-form :model="addProForm" ref="UserRef" label-width="100px">
            <el-form-item label="所属项目" required>
              <el-input v-model="addProForm.project_name" disabled>
                <template #prefix>
                  <el-icon><Folder /></el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="选择用户">
              <el-select
                v-model="addProForm.users"
                multiple
                filterable
                placeholder="请选择用户"
                style="width: 100%"
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option
                  v-for="iter in usersExclude"
                  :key="iter.id"
                  :value="iter.id"
                  :label="iter.username"
                >
                  <div class="user-option">
                    <el-avatar :size="24" :icon="User" class="user-avatar"></el-avatar>
                    <span>{{ iter.username }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="clearValidation">取消</el-button>
            <el-button type="primary" @click="clickExcludeUser" :loading="submitLoading">确定</el-button>
          </div>
        </template>
      </el-dialog>
    </el-scrollbar>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { mapGetters, mapState } from 'vuex'
import { 
  Plus, 
  UserFilled, 
  Search, 
  Refresh, 
  Edit, 
  Delete, 
  Key,
  User,
  Iphone,
  Message,
  Folder,
  Lock,
  List,
  Stamp
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'UserManagement',
  components: {},
  setup() {
    // Icons
    return {
      Plus,
      UserFilled,
      Search,
      Refresh,
      Edit,
      Delete,
      Key,
      User,
      Iphone,
      Message,
      Folder,
      Lock,
      List,
      Stamp
    }
  },
  data() {
    return {
      UserLsit: [],
      QueryCondition: {
        username: '',
        mobile: '',
        email: '',
        project_name: ''
      },
      Pager: {},
      addDlg: false,
      editDlg: false,
      addProDlg: false,
      tableLoading: false,
      submitLoading: false,
      showResetPassword: false,
      addForm: {
        username: '',
        mobile: '',
        email: '',
        project_id: '',
        project_name: '',
        password: '',
        weChat_name: ''
      },
      editForm: {
        username: '',
        mobile: '',
        email: '',
        project_id: '',
        project_name: '',
        password: '',
        weChat_name: ''
      },
      addProForm: {
        project_id: '',
        project_name: '',
        users: []
      },
      usersExclude: [],
      rulesUser: {
        username: [
          {
            required: true,
            message: '请输入用户名',
            trigger: 'blur'
          },
          {
            min: 3,
            max: 18,
            message: '用户名长度在3到18个字符之间',
            trigger: 'blur'
          }
        ],
        password: [
          {
            required: true,
            message: '请输入密码',
            trigger: 'blur'
          },
          {
            min: 3,
            max: 18,
            message: '密码长度在3到18个字符之间',
            trigger: 'blur'
          }
        ],
        mobile: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur'
          },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号格式',
            trigger: 'blur'
          }
        ]
      },
      readonlyInput: true
    }
  },
  computed: {
    ...mapState(['pro', 'interfaces']),
    ...mapGetters(['interfaces1', 'interfaces2'])
  },
  methods: {
    // 列表数据展示
    async getAllUser(page, size) {
      const username = this.QueryCondition.username.trim()
      const mobile = this.QueryCondition.mobile.trim()
      const email = this.QueryCondition.email.trim()
      const project_name = this.QueryCondition.project_name.trim()

      // 构造查询参数
      let params = []
      if (username) {
        params.push(`&username=${encodeURIComponent(username)}`)
      }
      if (mobile) {
        params.push(`&mobile=${encodeURIComponent(mobile)}`)
      }
      if (email) {
        params.push(`&email=${encodeURIComponent(email)}`)
      }
      if (project_name) {
        params.push(`&project_name=${encodeURIComponent(project_name)}`)
      }

      let url = '/users/user/'
      if (page && size) {
        url += `?page=${page}&size=${size}${params.join('')}`
      } else if (page) {
        url += `?page=${page}&size=${size}${params.join('')}`
      } else if (size) {
        url += `?size=${size}${params.join('')}`
      }

      try {
        const response = await this.$api.getAllUsers(url, this.pro.id)
        if (response.status === 200) {
          this.UserLsit = response.data.result
          this.Pager = response.data
        }
      } catch (error) {
        ElMessage.error('获取用户列表失败')
        console.error(error)
      } finally {
      }
    },

    async getExcludeUser() {
      try {
        const response = await this.$api.getExcludeUsers(this.pro.id)
        if (response.status === 200) {
          const userData = response.data
          this.usersExclude = userData.map(user => {
            return {
              id: user.id,
              username: user.username
            }
          })
          
          if (this.usersExclude.length === 0) {
            ElMessage({
              type: 'info',
              message: '没有可添加的用户',
              duration: 1500
            })
          }
        }
      } catch (error) {
        ElMessage.error('获取可选用户列表失败')
        console.error(error)
      }
    },

    async clickExcludeUser() {
      if (!this.addProForm.users || this.addProForm.users.length === 0) {
        ElMessage.warning('请至少选择一名用户')
        return
      }
      
      this.submitLoading = true
      try {
        const params = { ...this.addProForm }
        const response = await this.$api.addExcludeUser(params)
        if (response.status === 200) {
          ElMessage({
            type: 'success',
            message: `成功添加 ${params.users.length} 名用户到项目`,
            duration: 1500
          })
          this.addProDlg = false
          this.getAllUser(1, this.Pager.size)
        }
      } catch (error) {
        ElMessage.error('添加用户失败')
        console.error(error)
      } finally {
        this.submitLoading = false
      }
    },

    resetForm() {
      this.QueryCondition = {
        username: '',
        mobile: '',
        email: '',
        project_name: ''
      }
      ElMessage({
        type: 'info',
        message: '已重置搜索条件',
        duration: 1000
      })
      this.getAllUser(1, this.Pager.size)
    },

    submitForm() {
      this.getAllUser(1, this.Pager.size)
    },

    clickAdd() {
      this.addDlg = true
      this.addForm = {
        username: '',
        mobile: '',
        email: '',
        password: '',
        project_id: this.pro.id,
        project_name: this.pro.name,
        weChat_name: ''
      }
    },

    clickAddPro() {
      this.addProDlg = true
      this.addProForm = {
        project_id: this.pro.id,
        project_name: this.pro.name,
        users: []
      }
      this.getExcludeUser()
    },

    clearValidation() {
      this.addDlg = false
      this.editDlg = false
      this.addProDlg = false
      this.showResetPassword = false
      if (this.$refs.UserRef) {
        this.$refs.UserRef.clearValidate()
      }
    },

    AddInter() {
      this.$refs.UserRef.validate(async valid => {
        if (!valid) {
          ElMessage.warning('请正确填写表单')
          return
        }
        
        this.submitLoading = true
        try {
          const params = { ...this.addForm }
          if (params.weChat_name === '') {
            params.weChat_name = params.username
          }
          const response = await this.$api.createUser(params)
          if (response.status === 201) {
            ElMessage({
              type: 'success',
              message: '用户添加成功',
              duration: 1500
            })
            this.addForm = {
              username: '',
              mobile: '',
              email: '',
              password: '',
              project_id: '',
              project_name: '',
              weChat_name: ''
            }
            this.addDlg = false
            this.showResetPassword = false
            this.getAllUser(1, this.Pager.size)
          }
        } catch (error) {
          ElMessage.error('添加用户失败')
          console.error(error)
        } finally {
          this.submitLoading = false
        }
      })
    },

    UpdateInter() {
      this.$refs.UserRef.validate(async valid => {
        if (!valid) {
          ElMessage.warning('请正确填写表单')
          return
        }
        
        this.submitLoading = true
        try {
          const params = this.editForm
          const response = await this.$api.updateUser(params.id, params)
          if (response.status === 200) {
            ElMessage({
              type: 'success',
              message: '用户修改成功',
              duration: 1500
            })
            this.addForm = {
              username: '',
              mobile: '',
              email: '',
              password: '',
              project_id: '',
              project_name: '',
              weChat_name: ''
            }
            this.editDlg = false
            this.showResetPassword = false
            this.getAllUser(1, this.Pager.size)
          }
        } catch (error) {
          ElMessage.error('修改用户失败')
          console.error(error)
        } finally {
          this.submitLoading = false
        }
      })
    },

    resetPassword() {
      this.showResetPassword = !this.showResetPassword
      if (this.showResetPassword) {
        ElMessage({
          type: 'info',
          message: '请输入新密码',
          duration: 1500
        })
      } else {
        this.editForm.password = ''
      }
    },

    clickEdit(info) {
      this.editDlg = true
      this.editForm = { ...info }
      this.editForm.project_id = this.pro.id
      this.editForm.project_name = this.pro.name
    },

    delUser(id) {
      ElMessageBox.confirm('此操作将永久删除该用户, 是否继续?', '删除确认', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        draggable: true,
        closeOnClickModal: false
      })
        .then(async () => {
          try {
            const response = await this.$api.deleteUser(id)
            if (response.status === 204) {
              ElMessage({
                type: 'success',
                message: '删除成功!',
                duration: 1500
              })
              this.getAllUser(1, this.Pager.size)
            }
          } catch (error) {
            ElMessage.error('删除用户失败')
            console.error(error)
          }
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '已取消删除',
            duration: 1500
          })
        })
    },

    currentPages(currentPage) {
      this.getAllUser(currentPage, this.Pager.size)
      this.Pager.page = currentPage
    },

    sizes(size) {
      this.getAllUser(1, size)
    }
  },
  created() {
    this.getAllUser(1, 10)
  }
}
</script>

<style scoped>
.user-management {
  padding: 24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #eef2f7 100%);
  min-height: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.page-header h2 {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  background: linear-gradient(90deg, #3a8ee6 0%, #5f42f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.5px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.action-buttons .el-button {
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 10px 18px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-buttons .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.header-title .el-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.header-count {
  font-size: 14px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border: none;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 8px;
  transition: all 0.3s ease;
}

.search-card:hover {
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
}

.search-card .el-form {
  display: flex;
  flex-direction: column;
  padding: 8px;
}

.search-inputs {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.search-card .el-input {
  width: 220px;
  --el-input-height: 38px;
}

.query-buttons {
  margin-top: 16px;
  align-self: flex-end;
}

.button-group {
  display: flex;
  gap: 12px;
}

.query-buttons .el-button {
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 80px;
  justify-content: center;
  border-radius: 6px;
  padding: 8px 16px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.query-buttons .el-button:hover {
  transform: translateY(-2px);
}

.query-buttons .el-button:first-child {
  background: #f5f7fa;
  color: #606266;
  border-color: #dcdfe6;
}

.query-buttons .el-button:last-child {
  background: #409eff;
  border-color: #409eff;
  color: white;
}

.table-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border: none;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 8px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.table-card:hover {
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
}

.user-table {
  margin-bottom: 20px;
  --el-table-border-color: rgba(0, 0, 0, 0.05);
  --el-table-header-bg-color: #f5f7fa;
  --el-table-row-hover-bg-color: #f0f5ff;
}

:deep(.el-table th) {
  font-weight: 600;
  color: #2c3e50;
  padding: 12px 8px;
  background-color: #f6f9fc;
  border-bottom: 2px solid #e9ecef;
}

:deep(.el-table td) {
  padding: 14px 8px;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #f8fafc;
}

:deep(.el-table .el-table__row:hover) {
  box-shadow: inset 0 0 0 1px #e6f1fc;
}

.project-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
}

.project-tag {
  margin: 3px;
  font-size: 12px;
  border-radius: 6px;
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
  border: 1px solid #dcdfe6;
  background-color: #f5f7fa;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.project-tag .el-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.project-tag:hover {
  border-color: #409eff;
  background-color: #ecf5ff;
  transform: translateY(-1px);
}

.index-tag {
  background-color: #f0f2f5;
  border-color: #e4e7ed;
  color: #606266;
  font-weight: 600;
  border-radius: 4px;
  min-width: 28px;
}

.username-cell, .mobile-cell, .email-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 200px;
  margin: 0 auto;
}

.username-cell .el-icon, 
.mobile-cell .el-icon, 
.email-cell .el-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.user-avatar {
  background: linear-gradient(45deg, #36d1dc, #5b86e5);
}

.username-text {
  font-weight: 500;
  flex: 1;
  text-align: left;
  min-width: 0;
}

.wechat-tag {
  background-color: #f0f9eb;
  color: #67c23a;
  border-color: #e1f3d8;
}

.action-column {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.action-btn {
  width: 32px;
  height: 32px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn .el-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.action-btn:hover {
  transform: translateY(-2px);
}

.action-btn::before {
  display: none;
}

.user-option {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 100%;
}

.user-option .el-avatar {
  flex-shrink: 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

:deep(.el-pagination button), :deep(.el-pagination .el-pager li) {
  background-color: white;
  border-radius: 6px;
  margin: 0 2px;
  transition: all 0.3s ease;
}

:deep(.el-pagination .el-pager li.is-active) {
  background-color: #409eff;
  color: white;
  font-weight: bold;
}




:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  background-color: #f8f9fa;
}

.dialog-content {
  padding: 0 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.dialog-footer .el-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-weight: 500;
  min-width: 90px;
  border-radius: 8px;
  padding: 10px 20px;
  transition: all 0.3s ease;
}

.dialog-footer .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dialog-footer .el-button .el-icon {
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #2c3e50;
}

:deep(.el-form-item) {
  margin-bottom: 22px;
}

:deep(.el-input) {
  --el-input-border-radius: 8px;
}

:deep(.el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
  box-shadow: none;
}

:deep(.el-button--success) {
  background-color: #67c23a;
  border-color: #67c23a;
  box-shadow: none;
}

:deep(.el-button--warning) {
  background-color: #e6a23c;
  border-color: #e6a23c;
  box-shadow: none;
}

:deep(.el-button--danger) {
  background-color: #f56c6c;
  border-color: #f56c6c;
  box-shadow: none;
}

:deep(.el-button--primary:hover), 
:deep(.el-button--success:hover), 
:deep(.el-button--warning:hover), 
:deep(.el-button--danger:hover) {
  opacity: 0.9;
  transform: translateY(-2px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

:deep(.el-button--primary:active), 
:deep(.el-button--success:active), 
:deep(.el-button--warning:active), 
:deep(.el-button--danger:active) {
  transform: translateY(0);
}

:deep(.el-button--primary.is-plain) {
  color: #409eff;
  background: #ecf5ff;
  border-color: #b3d8ff;
}

:deep(.el-button--danger.is-plain) {
  color: #f56c6c;
  background: #fef0f0;
  border-color: #fab6b6;
}

/* 修复图标对齐问题 */
:deep(.el-input__prefix) {
  display: flex;
  align-items: center;
}

:deep(.el-input__prefix .el-icon) {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

:deep(.el-tag) {
  display: inline-flex;
  align-items: center;
}

/* 普通按钮的通用样式增强 */
:deep(.el-button) {
  position: relative;
  overflow: hidden;
  font-weight: 500;
}

.action-buttons .el-button .el-icon,
.query-buttons .el-button .el-icon,
.dialog-footer .el-button .el-icon {
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media screen and (max-width: 768px) {
  .user-management {
    padding: 16px;
  }
  
  .search-card .el-form {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-card .el-input {
    width: 100%;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .action-buttons {
    width: 100%;
    justify-content: space-between;
  }
}
</style>