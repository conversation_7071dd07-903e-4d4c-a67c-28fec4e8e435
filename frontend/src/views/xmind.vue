<template>
  <div class="feature-closed">
    <el-icon :size="60" color="#909399"><Lock /></el-icon>
    <h3>功能暂不对外开放</h3>
  </div>
</template>

<script>
import { Lock } from '@element-plus/icons-vue'
export default {
  components: { Lock }
}
</script>

<style scoped>
.feature-closed {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;
}
</style>