{"version": 3, "file": "js/296.59b2f0de.js", "mappings": "oNAEOA,MAAM,S,GACJA,MAAM,O,GAGFA,MAAM,yB,GAEJA,MAAM,qB,GACJA,MAAM,mB,GACJA,MAAM,wB,GA8BNA,MAAM,oB,GACFA,MAAM,a,GAadA,MAAM,mB,GACJA,MAAM,e,GACJA,MAAM,e,GAMNA,MAAM,kB,GAEJA,MAAM,iB,GACJA,MAAM,e,GAEHA,MAAM,gB,GAETA,MAAM,e,GAEHA,MAAM,gB,GAMfA,MAAM,e,GACJA,MAAM,e,GAMNA,MAAM,kB,GAEJA,MAAM,iB,GACJA,MAAM,e,GAEHA,MAAM,gB,GAETA,MAAM,e,GAEHA,MAAM,gB,GAMfA,MAAM,e,GACJA,MAAM,e,GAMNA,MAAM,kB,GAEJA,MAAM,iB,GACJA,MAAM,e,GAEHA,MAAM,gB,GAETA,MAAM,e,GAEHA,MAAM,gB,GAMfA,MAAM,e,GACJA,MAAM,e,GAMNA,MAAM,kB,GAEJA,MAAM,iB,GACJA,MAAM,e,GAEHA,MAAM,gB,GAETA,MAAM,e,GAEHA,MAAM,gB,GAUrBA,MAAM,4B,GAeJA,MAAM,kB,SA+DmBA,MAAM,Q,GAC/BC,MAAA,2E,GACED,MAAM,c,GAINC,MAAA,yC,GAWFD,MAAM,W,IAaHC,MAAA,uB,IAWHA,MAAA,0B,IAmBWD,MAAM,kB,IA8BjBA,MAAM,oB,IACJA,MAAM,iB,IAYNA,MAAM,iB,IAYNA,MAAM,iB,IAYNA,MAAM,iB,IAYNA,MAAM,iB,IAYNA,MAAM,iB,IAaRA,MAAM,aAAaC,MAAA,uB,UAcrBD,MAAM,SAA0DC,MAAA,2C,IAEnCD,MAAM,Q,IACjCA,MAAM,gB,IACJA,MAAM,c,UAWNA,MAAM,wB,UAWcA,MAAM,oB,UAWTA,MAAM,oB,qBAOJA,MAAM,gB,IAiBvBC,MAAA,6C,UASGD,MAAM,kB,UAMYA,MAAM,Q,IAC/BA,MAAM,c,IAKJA,MAAM,uB,IAeRA,MAAM,gB,IACJA,MAAM,gB,IAEJA,MAAM,e,IAINA,MAAM,e,IAINA,MAAM,e,IAINA,MAAM,e,IAKRA,MAAM,gB,IAEJA,MAAM,e,IAINA,MAAM,e,IAINA,MAAM,e,IAINA,MAAM,e,IAKRA,MAAM,gB,IAEJA,MAAM,e,IAINA,MAAM,e,IAINA,MAAM,e,IAINA,MAAM,e,UAOeA,MAAM,Q,UAePC,MAAA,uB,qBAQCD,MAAM,Q,IAC/BA,MAAM,c,IAYNA,MAAM,gB,IA4BNA,MAAM,iB,IAEDA,MAAM,Y,IACNA,MAAM,a,UACNA,MAAM,gB,IAMJA,MAAM,W,UAIDA,MAAM,e,UAWwBA,MAAM,e,UAIbA,MAAM,W,IACrCA,MAAM,qB,IAmBdA,MAAM,2B,IACJA,MAAM,oB,IAEJA,MAAM,iB,IAQCA,MAAM,c,IAMfA,MAAM,oB,eAgBNA,MAAM,oB,eAGHA,MAAM,wB,UAuBXA,MAAM,yB,IAIAA,MAAM,wB,IAOJA,MAAM,e,IAEHA,MAAM,gB,IAITA,MAAM,e,IAEHA,MAAM,gB,IAITA,MAAM,e,IAEHA,MAAM,gB,IAITA,MAAM,e,IAEHA,MAAM,gB,IAITA,MAAM,e,IAEHA,MAAM,gB,IAITA,MAAM,e,IAEHA,MAAM,gB,IASXA,MAAM,wB,IAOJA,MAAM,e,IAEHA,MAAM,gB,IAITA,MAAM,e,IAEHA,MAAM,gB,IAITA,MAAM,e,IAEHA,MAAM,gB,IAITA,MAAM,e,IAEHA,MAAM,gB,IASXA,MAAM,wB,IAOJA,MAAM,e,IAEHA,MAAM,gB,IAITA,MAAM,e,IAEHA,MAAM,gB,IAITA,MAAM,e,IAEHA,MAAM,gB,IAITA,MAAM,e,IAEHA,MAAM,gB,IAqBjBA,MAAM,+B,IACJA,MAAM,8B,IACJA,MAAM,sB,IAmBJA,MAAM,8B,IAMFA,MAAM,0B,IASNA,MAAM,4B,IASNA,MAAM,6B,IASNA,MAAM,2B,IAyCZA,MAAM,+B,IACJA,MAAM,kB,IAINA,MAAM,mB,IACJA,MAAM,gB,IAEHA,MAAM,c,IAETA,MAAM,gB,IAEHA,MAAM,c,IAMTA,MAAM,mB,IACJA,MAAM,e,IACJA,MAAM,oB,IAEJA,MAAM,gB,IAERA,MAAM,oB,IAEJA,MAAM,gB,IAGVA,MAAM,e,IACJA,MAAM,oB,IAEJA,MAAM,gB,IAERA,MAAM,oB,IAEJA,MAAM,gB,IAUlBA,MAAM,8B,IAmGNA,MAAM,sB,IAOLA,MAAM,iB,IAsBDC,MAAA,2E,IAEGA,MAAA,mC,IAQRD,MAAM,iB,IAcTA,MAAM,wB,IAGAA,MAAM,4B,IACJA,MAAM,qB,IAINA,MAAM,sB,IACJA,MAAM,c,IAEHA,MAAM,gB,IAETA,MAAM,c,IAEHA,MAAM,gB,IAETA,MAAM,c,IAEHA,MAAM,gB,IAETA,MAAM,c,IAEHA,MAAM,gB,IAETA,MAAM,c,IAEHA,MAAM,gB,IAMbA,MAAM,2B,IACJA,MAAM,qB,IAINA,MAAM,sB,IACJA,MAAM,c,IAEHA,MAAM,gB,IAaTA,MAAM,c,IAEHA,MAAM,gB,IAaTA,MAAM,c,IAEHA,MAAM,gB,IAaTA,MAAM,c,IAEHA,MAAM,gB,IAaTA,MAAM,c,IAEHA,MAAM,gB,IAmBjBA,MAAM,yB,IACJA,MAAM,qB,IAINA,MAAM,sB,0BAQPA,MAAM,iB,k0CA7wChBE,EAAAA,EAAAA,IAupBeC,GAAA,CAvpBDC,OAAO,uBAAqB,C,iBAC1C,IAqpBM,EArpBNC,EAAAA,EAAAA,IAqpBM,MArpBNC,EAqpBM,EAppBJD,EAAAA,EAAAA,IAmpBI,MAnpBJE,EAmpBI,EAjpBFL,EAAAA,EAAAA,IA2IUM,GAAA,CA3IDR,MAAM,sBAAoB,C,iBACjC,IAyIM,EAzINK,EAAAA,EAAAA,IAyIM,MAzINI,EAyIM,EAvIJJ,EAAAA,EAAAA,IA4CM,MA5CNK,EA4CM,EA3CJL,EAAAA,EAAAA,IA8BM,MA9BNM,EA8BM,EA7BJN,EAAAA,EAAAA,IA4BM,MA5BNO,EA4BM,EA3BJV,EAAAA,EAAAA,IAQYW,GAAA,CAPVb,MAAM,mBACNc,KAAK,OACJC,QAAK,CAAEC,GAAAC,a,qBACR,OAAW,a,kBAEX,IAAc,E,iBAAXC,GAAAC,UAAW,IACd,IAAAjB,EAAAA,EAAAA,IAA6CkB,GAAA,CAApCpB,MAAM,aAAW,C,iBAAC,IAAQ,EAARE,EAAAA,EAAAA,IAAQmB,M,4BAGrB,QAARH,GAAAI,W,WADRC,EAAAA,EAAAA,IAKSC,GAAA,C,MAHPxB,MAAM,0BACNyB,OAAO,S,kBACP,IAAY,E,iBAAVP,GAAAI,UAAQ,K,uBAGI,QAARJ,GAAAI,W,WADRC,EAAAA,EAAAA,IAKSC,GAAA,C,MAHPxB,MAAM,0BACNyB,OAAO,S,kBACP,IAAY,E,iBAAVP,GAAAI,UAAQ,K,uBAGI,SAARJ,GAAAI,W,WADRC,EAAAA,EAAAA,IAKSC,GAAA,C,MAHPxB,MAAM,wBACNyB,OAAO,S,kBACP,IAAY,E,iBAAVP,GAAAI,UAAQ,K,4BAIdjB,EAAAA,EAAAA,IAEM,MAFNqB,EAEM,EADFrB,EAAAA,EAAAA,IAA4C,MAA5CsB,EAAuB,SAAKC,EAAAA,EAAAA,IAAGV,GAAAW,MAAI,KAG/BX,GAAAY,UAA6B,aAAjBZ,GAAAa,e,WADpBR,EAAAA,EAAAA,IAQES,GAAA,C,iBANSd,GAAAC,S,qCAAAD,GAAAC,SAAQc,GAChBC,OAAMlB,GAAAmB,cACPC,IAAI,QACJC,KAAK,QACLrC,MAAM,kBACLe,QAAKuB,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,Y,oDAIjBlC,EAAAA,EAAAA,IAwFM,MAxFNmC,EAwFM,EAvFJnC,EAAAA,EAAAA,IAoBM,MApBNoC,EAoBM,EAnBJpC,EAAAA,EAAAA,IAKM,MALNqC,EAKM,EAJJxC,EAAAA,EAAAA,IAGOyC,GAAA,CAFLC,KAAK,4BACL,YAAU,UAGdvC,EAAAA,EAAAA,IAYM,MAZNwC,EAYM,C,eAXJxC,EAAAA,EAAAA,IAAoC,OAA/BL,MAAM,gBAAe,QAAI,KAC9BK,EAAAA,EAAAA,IASM,MATNyC,EASM,EARJzC,EAAAA,EAAAA,IAGM,MAHN0C,EAGM,C,eAFJ1C,EAAAA,EAAAA,IAAwC,QAAlCL,MAAM,gBAAe,UAAM,KACjCK,EAAAA,EAAAA,IAA2E,OAA3E2C,GAA2EpB,EAAAA,EAAAA,KAA5CV,GAAA+B,WAAWC,QAAU,GAAGC,QAAQ,IAAD,MAEhE9C,EAAAA,EAAAA,IAGM,MAHN+C,EAGM,C,eAFJ/C,EAAAA,EAAAA,IAAyC,QAAnCL,MAAM,gBAAe,WAAO,KAClCK,EAAAA,EAAAA,IAAoF,OAApFgD,GAAoFzB,EAAAA,EAAAA,IAAvDZ,GAAAsC,mBAAmBpC,GAAA+B,WAAWM,kBAAe,YAMlFlD,EAAAA,EAAAA,IAoBM,MApBNmD,EAoBM,EAnBJnD,EAAAA,EAAAA,IAKM,MALNoD,EAKM,EAJJvD,EAAAA,EAAAA,IAGOyC,GAAA,CAFLC,KAAK,oBACL,YAAU,UAGdvC,EAAAA,EAAAA,IAYM,MAZNqD,EAYM,C,eAXJrD,EAAAA,EAAAA,IAAoC,OAA/BL,MAAM,gBAAe,QAAI,KAC9BK,EAAAA,EAAAA,IASM,MATNsD,EASM,EARJtD,EAAAA,EAAAA,IAGM,MAHNuD,EAGM,C,eAFJvD,EAAAA,EAAAA,IAA2C,QAArCL,MAAM,gBAAe,aAAS,KACpCK,EAAAA,EAAAA,IAA2D,OAA3DwD,GAA2DjC,EAAAA,EAAAA,IAA9BZ,GAAA8C,sBAAsB,IAAC,MAEtDzD,EAAAA,EAAAA,IAGM,MAHN0D,EAGM,C,eAFJ1D,EAAAA,EAAAA,IAA0C,QAApCL,MAAM,gBAAe,YAAQ,KACnCK,EAAAA,EAAAA,IAA8D,OAA9D2D,GAA8DpC,EAAAA,EAAAA,IAAjCZ,GAAAiD,yBAAyB,IAAC,YAM/D5D,EAAAA,EAAAA,IAoBM,MApBN6D,EAoBM,EAnBJ7D,EAAAA,EAAAA,IAKM,MALN8D,EAKM,EAJJjE,EAAAA,EAAAA,IAGOyC,GAAA,CAFLC,KAAK,UACL,YAAU,UAGdvC,EAAAA,EAAAA,IAYM,MAZN+D,EAYM,C,eAXJ/D,EAAAA,EAAAA,IAAoC,OAA/BL,MAAM,gBAAe,QAAI,KAC9BK,EAAAA,EAAAA,IASM,MATNgE,EASM,EARJhE,EAAAA,EAAAA,IAGM,MAHNiE,EAGM,C,eAFJjE,EAAAA,EAAAA,IAAsC,QAAhCL,MAAM,gBAAe,QAAI,KAC/BK,EAAAA,EAAAA,IAAiE,OAAjEkE,GAAiE3C,EAAAA,EAAAA,IAApCV,GAAA+B,WAAWuB,UAAY,MAAJ,MAElDnE,EAAAA,EAAAA,IAGM,MAHNoE,EAGM,C,eAFJpE,EAAAA,EAAAA,IAAuC,QAAjCL,MAAM,gBAAe,SAAK,KAChCK,EAAAA,EAAAA,IAAwE,OAAxEqE,GAAwE9C,EAAAA,EAAAA,IAA3CZ,GAAA2D,WAAWzD,GAAA+B,WAAW2B,cAAW,YAMtEvE,EAAAA,EAAAA,IAoBM,MApBNwE,EAoBM,EAnBJxE,EAAAA,EAAAA,IAKM,MALNyE,EAKM,EAJJ5E,EAAAA,EAAAA,IAGOyC,GAAA,CAFLC,KAAK,UACL,YAAU,UAGdvC,EAAAA,EAAAA,IAYM,MAZN0E,EAYM,C,eAXJ1E,EAAAA,EAAAA,IAAoC,OAA/BL,MAAM,gBAAe,QAAI,KAC9BK,EAAAA,EAAAA,IASM,MATN2E,EASM,EARJ3E,EAAAA,EAAAA,IAGM,MAHN4E,EAGM,C,eAFJ5E,EAAAA,EAAAA,IAAuC,QAAjCL,MAAM,gBAAe,SAAK,KAChCK,EAAAA,EAAAA,IAAyE,OAAzE6E,GAAyEtD,EAAAA,EAAAA,IAA5CZ,GAAAmE,eAAejE,GAAA+B,WAAWmC,WAAQ,MAEjE/E,EAAAA,EAAAA,IAGM,MAHNgF,EAGM,C,eAFJhF,EAAAA,EAAAA,IAAsC,QAAhCL,MAAM,gBAAe,QAAI,KAC/BK,EAAAA,EAAAA,IAAgE,OAAhEiF,GAAgE1D,EAAAA,EAAAA,IAAnCV,GAAA+B,WAAWsC,WAAa,GAAG,IAAC,iB,OAUvElF,EAAAA,EAAAA,IA6EM,MA7ENmF,EA6EM,EA5EJtF,EAAAA,EAAAA,IAWUuF,GAAA,CAVP,iBAAgBvE,GAAAwE,YACjBC,KAAK,aACJC,SAAQ5E,GAAA6E,aACT7F,MAAM,e,kBAEN,IAA2C,EAA3CE,EAAAA,EAAAA,IAA2C4F,GAAA,CAA7BC,MAAM,KAAG,C,iBAAC,IAAIzD,EAAA,MAAAA,EAAA,M,QAAJ,W,eACxBpC,EAAAA,EAAAA,IAA0C4F,GAAA,CAA5BC,MAAM,KAAG,C,iBAAC,IAAGzD,EAAA,MAAAA,EAAA,M,QAAH,U,eACxBpC,EAAAA,EAAAA,IAA4C4F,GAAA,CAA9BC,MAAM,KAAG,C,iBAAC,IAAKzD,EAAA,MAAAA,EAAA,M,QAAL,Y,eACxBpC,EAAAA,EAAAA,IAA6C4F,GAAA,CAA/BC,MAAM,KAAG,C,iBAAC,IAAMzD,EAAA,MAAAA,EAAA,M,QAAN,a,eACxBpC,EAAAA,EAAAA,IAAyC4F,GAAA,CAA3BC,MAAM,KAAG,C,iBAAC,IAAEzD,EAAA,MAAAA,EAAA,M,QAAF,S,uDAI1BjC,EAAAA,EAAAA,IA6DM,MA7DN2F,EA6DM,EA5DJ9F,EAAAA,EAAAA,IAGYW,GAAA,CAHDC,KAAK,OAAQC,QAAOC,GAAAiF,KAAMjG,MAAM,c,kBACzC,IAAgC,EAAhCE,EAAAA,EAAAA,IAAgCkB,GAAA,M,iBAAvB,IAAa,EAAblB,EAAAA,EAAAA,IAAagG,M,6BAAU,W,6BAGlChG,EAAAA,EAAAA,IAGYW,GAAA,CAHDC,KAAK,UAAWC,QAAOC,GAAAmF,WAAYnG,MAAM,c,kBAClD,IAA4B,EAA5BE,EAAAA,EAAAA,IAA4BkB,GAAA,M,iBAAnB,IAAS,EAATlB,EAAAA,EAAAA,IAASkG,M,6BAAU,W,6BAK9BlG,EAAAA,EAAAA,IAoBcmG,GAAA,CApBDC,QAAQ,QAASC,UAASvF,GAAAwF,oBAAqBxG,MAAM,mB,CAIrDyG,UAAQC,EAAAA,EAAAA,IACjB,IAamB,EAbnBxG,EAAAA,EAAAA,IAamByG,GAAA,M,iBAZjB,IAEmB,EAFnBzG,EAAAA,EAAAA,IAEmB0G,GAAA,CAFDC,QAAQ,SAAO,C,iBAC/B,IAA+B,EAA/B3G,EAAAA,EAAAA,IAA+BkB,GAAA,M,iBAAtB,IAAY,EAAZlB,EAAAA,EAAAA,IAAY4G,M,6BAAU,iB,eAEjC5G,EAAAA,EAAAA,IAEmB0G,GAAA,CAFDC,QAAQ,QAAM,C,iBAC9B,IAA8B,EAA9B3G,EAAAA,EAAAA,IAA8BkB,GAAA,M,iBAArB,IAAW,EAAXlB,EAAAA,EAAAA,IAAW6G,M,6BAAU,gB,eAEhC7G,EAAAA,EAAAA,IAEmB0G,GAAA,CAFDC,QAAQ,OAAK,C,iBAC7B,IAAmC,EAAnC3G,EAAAA,EAAAA,IAAmCkB,GAAA,M,iBAA1B,IAAgB,EAAhBlB,EAAAA,EAAAA,IAAgB8G,M,6BAAU,e,eAErC9G,EAAAA,EAAAA,IAEmB0G,GAAA,CAFDC,QAAQ,OAAK,C,iBAC7B,IAA4B,EAA5B3G,EAAAA,EAAAA,IAA4BkB,GAAA,M,iBAAnB,IAAS,EAATlB,EAAAA,EAAAA,IAAS+G,M,6BAAU,c,yCAflC,IAEY,EAFZ/G,EAAAA,EAAAA,IAEYW,GAAA,CAFDC,KAAK,UAAWoG,UAAWhG,GAAA+B,WAAWkE,GAAInH,MAAM,c,kBAAa,IACpE,C,uBADoE,SACpEE,EAAAA,EAAAA,IAAwDkB,GAAA,CAA/CpB,MAAM,kBAAgB,C,iBAAC,IAAc,EAAdE,EAAAA,EAAAA,IAAckH,M,8DAqBpDlH,EAAAA,EAAAA,IAGYW,GAAA,CAHAE,QAAOC,GAAAqG,uBAAwBvG,KAAK,UAAUd,MAAM,c,kBAC9D,IAA2B,EAA3BE,EAAAA,EAAAA,IAA2BkB,GAAA,M,iBAAlB,IAAQ,EAARlB,EAAAA,EAAAA,IAAQoH,M,6BAAU,W,6BAK7BpH,EAAAA,EAAAA,IAoBcmG,GAAA,CApBDC,QAAQ,QAASC,UAASvF,GAAAuG,kBAAmBvH,MAAM,mB,CAInDyG,UAAQC,EAAAA,EAAAA,IACjB,IAamB,EAbnBxG,EAAAA,EAAAA,IAamByG,GAAA,M,iBAZjB,IAEmB,EAFnBzG,EAAAA,EAAAA,IAEmB0G,GAAA,CAFDC,QAAQ,YAAU,C,iBAClC,IAAkC,EAAlC3G,EAAAA,EAAAA,IAAkCkB,GAAA,M,iBAAzB,IAAe,EAAflB,EAAAA,EAAAA,IAAesH,M,6BAAU,a,eAEpCtH,EAAAA,EAAAA,IAEmB0G,GAAA,CAFDC,QAAQ,WAAS,C,iBACjC,IAAmC,EAAnC3G,EAAAA,EAAAA,IAAmCkB,GAAA,M,iBAA1B,IAAgB,EAAhBlB,EAAAA,EAAAA,IAAgBuH,M,6BAAU,c,eAErCvH,EAAAA,EAAAA,IAEmB0G,GAAA,CAFDC,QAAQ,WAAS,C,iBACjC,IAA+B,EAA/B3G,EAAAA,EAAAA,IAA+BkB,GAAA,M,iBAAtB,IAAY,EAAZlB,EAAAA,EAAAA,IAAYwH,M,6BAAU,Y,eAEjCxH,EAAAA,EAAAA,IAEmB0G,GAAA,CAFDC,QAAQ,UAAQ,C,iBAChC,IAA8B,EAA9B3G,EAAAA,EAAAA,IAA8BkB,GAAA,M,iBAArB,IAAW,EAAXlB,EAAAA,EAAAA,IAAWyH,M,6BAAU,c,yCAfpC,IAEY,EAFZzH,EAAAA,EAAAA,IAEYW,GAAA,CAFDC,KAAK,OAAOd,MAAM,c,kBAAa,IACtC,C,uBADsC,SACtCE,EAAAA,EAAAA,IAAwDkB,GAAA,CAA/CpB,MAAM,kBAAgB,C,iBAAC,IAAc,EAAdE,EAAAA,EAAAA,IAAckH,M,kDAqB7B,MAAhBlG,GAAAwE,c,WAAXkC,EAAAA,EAAAA,IAmLM,MAnLNC,EAmLM,EAlLJxH,EAAAA,EAAAA,IAeM,MAfNyH,EAeM,EAdJzH,EAAAA,EAAAA,IAGM,MAHN0H,EAGM,EAFF7H,EAAAA,EAAAA,IAAmD8H,GAAA,CAAvCC,UAAU,WAAWjI,MAAM,Y,uBAAY,cAGvDK,EAAAA,EAAAA,IASM,MATN6H,EASM,CAR4B,QAAbhH,GAAAI,W,WAAjBC,EAAAA,EAAAA,IAGYV,GAAA,C,MAHyBb,MAAM,UAAUc,KAAK,W,kBACtD,IAAmC,EAAnCZ,EAAAA,EAAAA,IAAmCkB,GAAA,M,iBAA1B,IAAgB,EAAhBlB,EAAAA,EAAAA,IAAgBiI,M,qBACzB9H,EAAAA,EAAAA,IAA0C,QAApCJ,MAAA,uBAAyB,QAAI,M,4BAEvCsB,EAAAA,EAAAA,IAGYV,GAAA,C,MAHMb,MAAM,UAAUc,KAAK,UAAWC,QAAOC,GAAAoH,W,kBACrD,IAAmC,EAAnClI,EAAAA,EAAAA,IAAmCkB,GAAA,M,iBAA1B,IAAgB,EAAhBlB,EAAAA,EAAAA,IAAgBiI,M,qBACzB9H,EAAAA,EAAAA,IAA0C,QAApCJ,MAAA,uBAAyB,QAAI,M,kCAI7CI,EAAAA,EAAAA,IAOM,MAPNgI,EAOM,EANJhI,EAAAA,EAAAA,IAAuF,YAAjF,SAAKuB,EAAAA,EAAAA,IAAEZ,GAAAsH,gBAAgBpH,GAAA+B,WAAW3B,UAAYJ,GAAA+B,WAAWsF,MAAMjH,WAAQ,IAC7EjB,EAAAA,EAAAA,IAA4D,YAAtD,SAAKuB,EAAAA,EAAAA,IAAEZ,GAAAwH,kBAAkBtH,GAAA+B,WAAW3B,WAAQ,IAClDjB,EAAAA,EAAAA,IAA+E,YAAzE,SAAKuB,EAAAA,EAAAA,IAAEZ,GAAAyH,uBAAuBvH,GAAA+B,WAAWsF,MAAMG,mBAAgB,IACrErI,EAAAA,EAAAA,IAA4C,YAAtC,UAAMuB,EAAAA,EAAAA,IAAEZ,GAAA2H,uBAAmB,IACjCtI,EAAAA,EAAAA,IAAmD,YAA7C,SAAKuB,EAAAA,EAAAA,IAAEV,GAAA+B,WAAW2F,eAAiB,GAAJ,IACrCvI,EAAAA,EAAAA,IAAsD,YAAhD,UAAMuB,EAAAA,EAAAA,IAAEV,GAAA+B,WAAW4F,iBAAmB,GAAJ,MAE1C3I,EAAAA,EAAAA,IAMYW,GAAA,CALVZ,MAAA,gEACCa,KAAMI,GAAA4H,aAAe,UAAY,UACjC/H,QAAOC,GAAA+H,kB,kBACR,IAA2B,EAA3B7I,EAAAA,EAAAA,IAA2BkB,GAAA,M,iBAAlB,IAAQ,EAARlB,EAAAA,EAAAA,IAAQ8I,M,OACjB3I,EAAAA,EAAAA,IAAyE,OAAzE4I,IAAyErH,EAAAA,EAAAA,IAAvCV,GAAA4H,aAAe,OAAS,OAAZ,K,0CAEhDzI,EAAAA,EAAAA,IAQM,OARDL,MAAM,cAAY,EACrBK,EAAAA,EAAAA,IAA2B,YAArB,mBACNA,EAAAA,EAAAA,IAA2B,YAArB,mBACNA,EAAAA,EAAAA,IAA2B,YAArB,mBACNA,EAAAA,EAAAA,IAAgC,YAA1B,wBACNA,EAAAA,EAAAA,IAAgC,YAA1B,wBACNA,EAAAA,EAAAA,IAA0B,YAApB,kBACNA,EAAAA,EAAAA,IAA4B,YAAtB,qB,KAERA,EAAAA,EAAAA,IAgDM,MAhDN6I,GAgDM,EA/CJhJ,EAAAA,EAAAA,IA8CWiJ,GAAA,CA7CT/G,IAAI,QACHgH,KAAMpI,GAAAqI,gBACPpJ,MAAA,eACAqJ,OAAA,GACA,aAAW,2BACVC,kBAAkBvI,GAAAwI,sBAClB,UAASC,GAAOA,EAAIC,OAAS,IAAMD,EAAIE,KACvC,qBAAmB,EACnBC,WAAW5I,GAAA6I,gB,kBACZ,IAImB,EAJnB3J,EAAAA,EAAAA,IAImB4J,GAAA,CAHjBhJ,KAAK,YACLiJ,MAAM,KACLC,WAAYP,GAAsB,QAAfA,EAAIC,Q,wBAE1BxJ,EAAAA,EAAAA,IAMkB4J,GAAA,CANDG,KAAK,OAAOC,MAAM,OAAOC,MAAM,SAAS,YAAU,O,CACtDC,SAAO1D,EAAAA,EAAAA,IAGH2D,GAHU,EACvBnK,EAAAA,EAAAA,IAEaoK,GAAA,CAFAC,QAASF,EAAMZ,IAAIE,KAAMa,UAAU,O,kBAC9C,IAAwD,EAAxDnK,EAAAA,EAAAA,IAAwD,OAAxDoK,IAAwD7I,EAAAA,EAAAA,IAAxByI,EAAMZ,IAAIE,MAAI,K,gCAIpDzJ,EAAAA,EAAAA,IAAgG4J,GAAA,CAA/EG,KAAK,gBAAgBC,MAAM,OAAOH,MAAM,MAAMI,MAAM,YACrEjK,EAAAA,EAAAA,IAAmG4J,GAAA,CAAlFG,KAAK,kBAAkBC,MAAM,QAAQC,MAAM,SAASJ,MAAM,SAC3E7J,EAAAA,EAAAA,IAAkG4J,GAAA,CAAjFG,KAAK,iBAAiBC,MAAM,QAAQC,MAAM,SAASJ,MAAM,SAC1E7J,EAAAA,EAAAA,IAIkB4J,GAAA,CAJDG,KAAK,eAAeC,MAAM,QAAQC,MAAM,SAASJ,MAAM,O,CAC3DK,SAAO1D,EAAAA,EAAAA,IACmB2D,GADZ,E,iBACpBrJ,GAAA0J,mBAAmBL,EAAMZ,MAAG,K,OAGnCvJ,EAAAA,EAAAA,IAAqG4J,GAAA,CAApFG,KAAK,kBAAkBC,MAAM,UAAUC,MAAM,SAASJ,MAAM,SAC7E7J,EAAAA,EAAAA,IAAqG4J,GAAA,CAApFG,KAAK,kBAAkBC,MAAM,UAAUC,MAAM,SAASJ,MAAM,SAC7E7J,EAAAA,EAAAA,IAAqG4J,GAAA,CAApFG,KAAK,kBAAkBC,MAAM,UAAUC,MAAM,SAASJ,MAAM,SAC7E7J,EAAAA,EAAAA,IAAwG4J,GAAA,CAAvFG,KAAK,kBAAkBC,MAAM,aAAaC,MAAM,SAASJ,MAAM,SAChF7J,EAAAA,EAAAA,IAAwG4J,GAAA,CAAvFG,KAAK,kBAAkBC,MAAM,aAAaC,MAAM,SAASJ,MAAM,SAChF7J,EAAAA,EAAAA,IAAwG4J,GAAA,CAAvFG,KAAK,kBAAkBC,MAAM,aAAaC,MAAM,SAASJ,MAAM,SAChF7J,EAAAA,EAAAA,IAAwG4J,GAAA,CAAvFG,KAAK,kBAAkBC,MAAM,aAAaC,MAAM,SAASJ,MAAM,SAChF7J,EAAAA,EAAAA,IAA0F4J,GAAA,CAAzEG,KAAK,SAASC,MAAM,QAAQC,MAAM,SAASJ,MAAM,SAClE7J,EAAAA,EAAAA,IAA6F4J,GAAA,CAA5EG,KAAK,YAAYC,MAAM,QAAQC,MAAM,SAASJ,MAAM,SACrE7J,EAAAA,EAAAA,IAMkB4J,GAAA,CANDG,KAAK,YAAYC,MAAM,OAAOC,MAAM,SAASJ,MAAM,O,CACvDK,SAAO1D,EAAAA,EAAAA,IACqG2D,GAD9F,CACsBA,EAAMZ,IAAIlE,UAAY,I,WAAnEhE,EAAAA,EAAAA,IAAqHC,GAAA,C,MAA7GxB,MAAM,yBAAwDc,KAAK,U,kBAAS,IAAuB,E,iBAArBuJ,EAAMZ,IAAIlE,WAAW,IAAC,K,YACzD8E,EAAMZ,IAAIlE,UAAY,I,WAAzEhE,EAAAA,EAAAA,IAA4HC,GAAA,C,MAApHxB,MAAM,0BAA8Dc,KAAK,W,kBAAU,IAAuB,E,iBAArBuJ,EAAMZ,IAAIlE,WAAW,IAAC,K,yBACnHhE,EAAAA,EAAAA,IAA+FC,GAAA,C,MAAvFxB,MAAM,0BAAiCc,KAAK,W,kBAAU,IAAuB,E,iBAArBuJ,EAAMZ,IAAIlE,WAAW,IAAC,K,qFAK9FlF,EAAAA,EAAAA,IAyEM,MAzENsK,GAyEM,EAxEJtK,EAAAA,EAAAA,IAWM,MAXNuK,GAWM,G,WAVJrJ,EAAAA,EAAAA,IASEsJ,GAAA,CARAC,WAAW,SACXC,WAAW,OACXC,YAAY,QACZC,UAAU,SACTC,MAAOhK,GAAAgK,MACPC,WAAYnK,GAAAoK,uBACZC,IAAG,iBAAqBnK,GAAAoK,eACzBC,UAAU,gB,mCAGdlL,EAAAA,EAAAA,IAWM,MAXNmL,GAWM,G,WAVJjK,EAAAA,EAAAA,IASEsJ,GAAA,CARAC,WAAW,aACXC,WAAW,OACXC,YAAY,QACZC,UAAU,QACTC,MAAOhK,GAAAgK,MACPC,WAAYnK,GAAAyK,cACZJ,IAAG,OAAWnK,GAAAoK,eACfC,UAAU,O,mCAGdlL,EAAAA,EAAAA,IAWM,MAXNqL,GAWM,G,WAVJnK,EAAAA,EAAAA,IASEsJ,GAAA,CARAC,WAAW,eACXC,WAAW,OACXC,YAAY,QACZC,UAAU,QACTC,MAAOhK,GAAAgK,MACPC,WAAYnK,GAAA2K,cACZN,IAAG,OAAWnK,GAAAoK,eACfC,UAAU,O,mCAGdlL,EAAAA,EAAAA,IAWM,MAXNuL,GAWM,G,WAVJrK,EAAAA,EAAAA,IASEsJ,GAAA,CARAC,WAAW,QACXC,WAAW,OACXC,YAAY,QACZC,UAAU,QACTC,MAAOhK,GAAAgK,MACPC,WAAYnK,GAAA6K,gBACZR,IAAG,SAAanK,GAAAoK,eACjBC,UAAU,S,mCAGdlL,EAAAA,EAAAA,IAWM,MAXNyL,GAWM,G,WAVJvK,EAAAA,EAAAA,IASEsJ,GAAA,CARAC,WAAW,WACXC,WAAW,OACXC,YAAY,QACZC,UAAU,SACTC,MAAOhK,GAAAgK,MACPC,WAAYnK,GAAA+K,cACZV,IAAG,OAAWnK,GAAAoK,eACfC,UAAU,O,mCAGdlL,EAAAA,EAAAA,IAWM,MAXN2L,GAWM,G,WAVJzK,EAAAA,EAAAA,IASEsJ,GAAA,CARAC,WAAW,WACXC,WAAW,OACXC,YAAY,QACZC,UAAU,SACTC,MAAOhK,GAAAgK,MACPC,WAAYnK,GAAAiL,cACZZ,IAAG,OAAWnK,GAAAoK,eACfC,UAAU,O,qCAIhBlL,EAAAA,EAAAA,IAEM,MAFN6L,GAEM,EAF4ChM,EAAAA,EAAAA,IAAkD8H,GAAA,CAAtCC,UAAU,WAAWjI,MAAM,Y,uBAAW,WAClGE,EAAAA,EAAAA,IAA0HW,GAAA,CAA9GqG,UAAWlG,GAAAmL,OAASpL,QAAOC,GAAAoL,aAActL,KAAK,UAAUuB,KAAK,QAAQpC,MAAA,wB,kBAA2B,IAAEqC,EAAA,MAAAA,EAAA,M,QAAF,S,yCAMpGpB,GAAAY,UAA6B,SAAjBZ,GAAAa,e,WAJtBR,EAAAA,EAAAA,IAUES,GAAA,C,MATE/B,MAAA,yCACCoM,KAAM,EACPvL,KAAK,W,WAEII,GAAAW,K,qCAAAX,GAAAW,KAAII,GACZC,OAAMlB,GAAAsL,aACPlK,IAAI,QACHrB,QAAKuB,EAAA,KAAAA,EAAA,IAAAC,EAAAA,EAAAA,IAAN,OAAW,WACXgK,YAAY,gB,iCAEcrL,GAAAY,UAA6B,SAAjBZ,GAAAa,c,4BAA1C6F,EAAAA,EAAAA,IAA2H,IAA3H4E,IAA2H5K,EAAAA,EAAAA,IAAVV,GAAAW,MAAI,Q,yBAEvHxB,EAAAA,EAAAA,IAyEM,MAzENoM,GAyEM,EAxEJpM,EAAAA,EAAAA,IAoBM,MApBNqM,GAoBM,EAnBJrM,EAAAA,EAAAA,IAUM,MAVNsM,GAUM,EATJzM,EAAAA,EAAAA,IAAmD8H,GAAA,CAAvCC,UAAU,WAAWjI,MAAM,Y,uBAAY,aAEnDE,EAAAA,EAAAA,IAKYW,GAAA,CAJTC,KAAME,GAAA4L,oBACPvK,KAAK,QACLZ,OAAO,S,kBACP,IAAyB,E,iBAAtBT,GAAA6L,qBAAiB,K,mBAIiC,QAAjB7L,GAAA6L,sB,WAAxCjF,EAAAA,EAAAA,IAOM,MAPNkF,GAOM,EANJ5M,EAAAA,EAAAA,IAKYW,GAAA,CAJVC,KAAK,UACJC,QAAOC,GAAA+L,WACR1K,KAAK,S,kBAAQ,IAEfC,EAAA,MAAAA,EAAA,M,QAFe,c,iDAOPtB,GAAAgM,cAWI9L,GAAA+L,S,WAAhBrF,EAAAA,EAAAA,IA8BM,MA9BNsF,GA8BM,EA7BJ7M,EAAAA,EAAAA,IAKS,UAJN8M,IAAKjM,GAAA+L,OACLG,OAAI9K,EAAA,KAAAA,EAAA,OAAA+K,IAAErM,GAAAsM,kBAAAtM,GAAAsM,oBAAAD,IACNE,QAAKjL,EAAA,KAAAA,EAAA,OAAA+K,IAAErM,GAAAwM,mBAAAxM,GAAAwM,qBAAAH,K,YAGCnM,GAAAuM,c,WAAX7F,EAAAA,EAAAA,IAsBM,MAtBN8F,GAsBM,EArBJxN,EAAAA,EAAAA,IAeWyN,GAAA,CAdTC,MAAM,UACLC,YAAa3M,GAAA4M,mBACdhN,KAAK,QACL,gB,CACWsJ,SAAO1D,EAAAA,EAAAA,IAChB,IAOMpE,EAAA,MAAAA,EAAA,MAPNjC,EAAAA,EAAAA,IAOM,OAPDJ,MAAA,uBAAyB,EAC5BI,EAAAA,EAAAA,IAAa,SAAV,WACHA,EAAAA,EAAAA,IAIK,MAJDJ,MAAA,wCAA0C,EAC5CI,EAAAA,EAAAA,IAAkB,UAAd,cACJA,EAAAA,EAAAA,IAAgB,UAAZ,YACJA,EAAAA,EAAAA,IAAuB,UAAnB,sB,+BAKZA,EAAAA,EAAAA,IAIM,MAJN0N,GAIM,EAHJ7N,EAAAA,EAAAA,IAEYW,GAAA,CAFDC,KAAK,UAAWC,QAAOC,GAAAgN,c,kBAAc,IAEhD1L,EAAA,MAAAA,EAAA,M,QAFgD,a,iEAQtDsF,EAAAA,EAAAA,IAIM,MAJNqG,GAIM,EAHJ/N,EAAAA,EAAAA,IAEWgO,GAAA,CAFDL,YAAY,aAAW,C,iBAC/B,IAAgCvL,EAAA,MAAAA,EAAA,MAAhCjC,EAAAA,EAAAA,IAAgC,SAA7B,6BAAyB,M,8BA9ChCuH,EAAAA,EAAAA,IAQM,MARNuG,GAQM,EAPJjO,EAAAA,EAAAA,IAMWyN,GAAA,CALTC,MAAM,QACLC,YAAa7M,GAAAoN,2BACdtN,KAAK,OACL,eACCuN,UAAU,G,wCA9BY,MAAhBnN,GAAAwE,eA0Ec,MAAhBxE,GAAAwE,c,WAAXkC,EAAAA,EAAAA,IAgFM,MAhFN0G,GAgFM,EA/EJjO,EAAAA,EAAAA,IAmBM,MAnBNkO,GAmBM,EAlBJrO,EAAAA,EAAAA,IAAmD8H,GAAA,CAAvCC,UAAU,WAAWjI,MAAM,Y,uBAAY,aAEnDE,EAAAA,EAAAA,IAAsFW,GAAA,CAA3EC,KAAK,UAAUuB,KAAK,QAAStB,QAAOC,GAAAwN,uB,kBAAuB,IAAIlM,EAAA,MAAAA,EAAA,M,QAAJ,W,6BAEtEjC,EAAAA,EAAAA,IAaM,MAbNoO,GAaM,EAZJvO,EAAAA,EAAAA,IAWaoK,GAAA,CAXAC,QAASvJ,GAAA0N,yBAA0BlE,UAAU,O,kBACxD,IASS,EATTtK,EAAAA,EAAAA,IASSsB,GAAA,CARNV,KAAME,GAAA2N,yBACP3O,MAAM,uBACNqC,KAAK,QACLZ,OAAO,QACNV,QAAOC,GAAA4N,2BACR3O,MAAA,oB,kBACA,IAA2B,EAA3BC,EAAAA,EAAAA,IAA2BkB,GAAA,M,iBAAlB,IAAQ,EAARlB,EAAAA,EAAAA,IAAQ8I,M,eAAU,KAC3BpH,EAAAA,EAAAA,IAAGZ,GAAA0N,0BAAsB,K,sDAKjCrO,EAAAA,EAAAA,IA0DM,MA1DNwO,GA0DM,EAzDJxO,EAAAA,EAAAA,IAkBM,MAlBNyO,GAkBM,C,eAjBJzO,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAGM,MAHN0O,GAGM,C,eAFJ1O,EAAAA,EAAAA,IAAoB,YAAd,WAAO,KACbA,EAAAA,EAAAA,IAAuG,QAAhGL,OAAKgP,EAAAA,EAAAA,IAAEhO,GAAAiO,iBAAiB/N,GAAAgO,eAAeC,gB,QAAgBjO,GAAAgO,eAAeC,aAAe,GAAG,IAAC,MAElG9O,EAAAA,EAAAA,IAGM,MAHN+O,GAGM,C,eAFJ/O,EAAAA,EAAAA,IAAmB,YAAb,UAAM,KACZA,EAAAA,EAAAA,IAAgH,QAAzGL,OAAKgP,EAAAA,EAAAA,IAAEhO,GAAAqO,oBAAoBnO,GAAAgO,eAAeI,mB,QAAmBpO,GAAAgO,eAAeI,gBAAkB,GAAG,IAAC,MAE3GjP,EAAAA,EAAAA,IAGM,MAHNkP,GAGM,C,eAFJlP,EAAAA,EAAAA,IAAmB,YAAb,UAAM,KACZA,EAAAA,EAAAA,IAAkD,aAAAuB,EAAAA,EAAAA,IAA1CV,GAAAgO,eAAeM,cAAgB,GAAG,IAAC,MAE7CnP,EAAAA,EAAAA,IAGM,MAHNoP,GAGM,C,eAFJpP,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,KACXA,EAAAA,EAAAA,IAAwG,YAAlG,KAACuB,EAAAA,EAAAA,IAAEZ,GAAA0O,YAAYxO,GAAAgO,eAAeS,eAAe,MAAE/N,EAAAA,EAAAA,IAAEZ,GAAA0O,YAAYxO,GAAAgO,eAAeU,eAAY,QAGlGvP,EAAAA,EAAAA,IAkBM,MAlBNwP,GAkBM,C,eAjBJxP,EAAAA,EAAAA,IAAc,UAAV,SAAK,KACTA,EAAAA,EAAAA,IAGM,MAHNyP,GAGM,C,eAFJzP,EAAAA,EAAAA,IAAmB,YAAb,UAAM,KACZA,EAAAA,EAAAA,IAAuD,aAAAuB,EAAAA,EAAAA,IAA/CV,GAAAgO,eAAea,oBAAsB,GAAJ,MAE3C1P,EAAAA,EAAAA,IAGM,MAHN2P,GAGM,C,eAFJ3P,EAAAA,EAAAA,IAAmB,YAAb,UAAM,KACZA,EAAAA,EAAAA,IAAgD,aAAAuB,EAAAA,EAAAA,IAAxCV,GAAAgO,eAAee,aAAe,GAAJ,MAEpC5P,EAAAA,EAAAA,IAGM,MAHN6P,GAGM,C,eAFJ7P,EAAAA,EAAAA,IAAmB,YAAb,UAAM,KACZA,EAAAA,EAAAA,IAAkD,aAAAuB,EAAAA,EAAAA,IAA1CV,GAAAgO,eAAeiB,eAAiB,GAAJ,MAEtC9P,EAAAA,EAAAA,IAGM,MAHN+P,GAGM,C,eAFJ/P,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVA,EAAAA,EAAAA,IAAsG,QAA/FL,OAAKgP,EAAAA,EAAAA,IAAEhO,GAAAqP,kBAAkBnP,GAAAgO,eAAeoB,e,QAAepP,GAAAgO,eAAeoB,YAAc,GAAG,IAAC,QAGnGjQ,EAAAA,EAAAA,IAkBM,MAlBNkQ,GAkBM,C,eAjBJlQ,EAAAA,EAAAA,IAAc,UAAV,SAAK,KACTA,EAAAA,EAAAA,IAGM,MAHNmQ,GAGM,C,eAFJnQ,EAAAA,EAAAA,IAAmB,YAAb,UAAM,KACZA,EAAAA,EAAAA,IAAuD,aAAAuB,EAAAA,EAAAA,IAA/CV,GAAAgO,eAAeuB,aAAe,UAAJ,MAEpCpQ,EAAAA,EAAAA,IAGM,MAHNqQ,GAGM,C,eAFJrQ,EAAAA,EAAAA,IAAoB,YAAd,WAAO,KACbA,EAAAA,EAAAA,IAAkD,aAAAuB,EAAAA,EAAAA,IAA1CV,GAAAgO,eAAeyB,WAAa,OAAJ,MAElCtQ,EAAAA,EAAAA,IAGM,MAHNuQ,GAGM,C,eAFJvQ,EAAAA,EAAAA,IAAiB,YAAX,QAAI,KACVA,EAAAA,EAAAA,IAAyD,aAAAuB,EAAAA,EAAAA,IAAjDZ,GAAA0O,YAAYxO,GAAAgO,eAAe2B,eAAY,MAEjDxQ,EAAAA,EAAAA,IAGM,MAHNyQ,GAGM,C,eAFJzQ,EAAAA,EAAAA,IAAkB,YAAZ,SAAK,KACXA,EAAAA,EAAAA,IAAsD,aAAAuB,EAAAA,EAAAA,IAA9CZ,GAAAmE,eAAejE,GAAAgO,eAAe6B,SAAM,a,eAKzB,MAAhB7P,GAAAwE,c,WAAXkC,EAAAA,EAAAA,IAsBM,MAtBNoJ,GAsBM,EArBJ3Q,EAAAA,EAAAA,IAYM,aAVJH,EAAAA,EAAAA,IAOE8B,GAAA,C,WANSd,GAAA+P,W,qCAAA/P,GAAA+P,WAAUhP,GACnBsK,YAAY,gBACZlK,KAAK,QACLpC,MAAA,qCACCiR,SAAKC,EAAAA,EAAAA,IAAQnQ,GAAAoQ,oBAAmB,WACjCC,UAAA,I,kCAEFnR,EAAAA,EAAAA,IAA4GW,GAAA,CAAjGC,KAAK,UAAUuB,KAAK,QAAStB,QAAOC,GAAAoQ,oBAAqBnR,MAAA,uB,kBAA0B,IAAEqC,EAAA,MAAAA,EAAA,M,QAAF,S,6BAC9FpC,EAAAA,EAAAA,IAA2GW,GAAA,CAAhGC,KAAK,OAAOuB,KAAK,QAAStB,QAAOC,GAAAsQ,sBAAuBrR,MAAA,uB,kBAA0B,IAAEqC,EAAA,MAAAA,EAAA,M,QAAF,S,8BAGpFpB,GAAAqQ,mB,WAAX3J,EAAAA,EAAAA,IAMM,MANN4J,GAMM,EALJnR,EAAAA,EAAAA,IAIU,UAHP8M,IAAKjM,GAAAqQ,iBACNtR,MAAA,kFACAwR,YAAY,K,iDAIS,MAAhBvQ,GAAAwE,c,WAAXkC,EAAAA,EAAAA,IA8EM,MA9EN8J,GA8EM,EA7EJrR,EAAAA,EAAAA,IAWM,MAXNsR,GAWM,EAVJzR,EAAAA,EAAAA,IAAmD8H,GAAA,CAAvCC,UAAU,WAAWjI,MAAM,Y,uBAAY,YAEnDE,EAAAA,EAAAA,IAA+EW,GAAA,CAApEC,KAAK,UAAUuB,KAAK,QAAStB,QAAOC,GAAA4Q,gB,kBAAgB,IAAItP,EAAA,MAAAA,EAAA,M,QAAJ,W,6BAC/DpC,EAAAA,EAAAA,IAA0EW,GAAA,CAA/DC,KAAK,UAAUuB,KAAK,QAAStB,QAAOC,GAAA6Q,W,kBAAW,IAAIvP,EAAA,MAAAA,EAAA,M,QAAJ,W,6BAC1DpC,EAAAA,EAAAA,IAKY4R,GAAA,C,WAJD5Q,GAAA6Q,Y,qCAAA7Q,GAAA6Q,YAAW9P,GACnB+P,SAAQhR,GAAAiR,kBACT,cAAY,OACZ,gBAAc,Q,qCAGlB5R,EAAAA,EAAAA,IA2BM,MA3BN6R,GA2BM,EA1BJhS,EAAAA,EAAAA,IASW8B,GAAA,C,WARAd,GAAAiR,U,qCAAAjR,GAAAiR,UAASlQ,GAClBsK,YAAY,UACZlK,KAAK,QACLpC,MAAA,sCACAoR,UAAA,I,CACWe,QAAM1L,EAAAA,EAAAA,IACf,IAA6B,EAA7BxG,EAAAA,EAAAA,IAA6BkB,GAAA,M,iBAApB,IAAU,EAAVlB,EAAAA,EAAAA,IAAUmS,M,gCAGvBnS,EAAAA,EAAAA,IAMYoS,GAAA,C,WANQpR,GAAAqR,S,uCAAArR,GAAAqR,SAAQtQ,GAAEsK,YAAY,OAAOlK,KAAK,QAAQpC,MAAA,uC,kBAC5D,IAA8C,EAA9CC,EAAAA,EAAAA,IAA8CsS,GAAA,CAAnCtI,MAAM,KAAKuI,MAAM,SAC5BvS,EAAAA,EAAAA,IAAmDsS,GAAA,CAAxCtI,MAAM,QAAQuI,MAAM,WAC/BvS,EAAAA,EAAAA,IAAiDsS,GAAA,CAAtCtI,MAAM,OAAOuI,MAAM,UAC9BvS,EAAAA,EAAAA,IAAuDsS,GAAA,CAA5CtI,MAAM,UAAUuI,MAAM,aACjCvS,EAAAA,EAAAA,IAAmDsS,GAAA,CAAxCtI,MAAM,QAAQuI,MAAM,Y,wBAEjCvS,EAAAA,EAAAA,IAOYoS,GAAA,C,WAPQpR,GAAAwR,Y,uCAAAxR,GAAAwR,YAAWzQ,GAAEsK,YAAY,OAAOlK,KAAK,QAAQpC,MAAA,uC,kBAE7D,IAAuC,G,aADzC2H,EAAAA,EAAAA,IAKY+K,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAJe1R,GAAA2R,cAAa,CAA9B3I,EAAOuI,M,WADjBlR,EAAAA,EAAAA,IAKYiR,GAAA,CAHTnH,IAAKoH,EACLvI,MAAOA,EACPuI,MAAOA,G,4DAGZvS,EAAAA,EAAAA,IAAuFsB,GAAA,CAA/ExB,MAAM,uBAAuBc,KAAK,Q,kBAAO,IAAE,E,QAAF,MAAEc,EAAAA,EAAAA,IAAEZ,GAAA8R,aAAaC,QAAQ,OAAI,K,SAEhF1S,EAAAA,EAAAA,IAoCM,MApCN2S,GAoCM,G,aAnCJpL,EAAAA,EAAAA,IA2BM+K,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IA3BsB5R,GAAA8R,aAAY,CAA3BG,EAAKlN,M,WAAlB6B,EAAAA,EAAAA,IA2BM,OA3BqCyD,IAAKtF,EAAQ/F,OAAKgP,EAAAA,EAAAA,IAAA,CAAEhO,GAAAkS,YAAYD,EAAIE,OAAc,c,EAC3F9S,EAAAA,EAAAA,IAA6D,OAA7D+S,GAAuB,KAACxR,EAAAA,EAAAA,IAAEZ,GAAA2D,WAAWsO,EAAII,YAAY,IAAC,IACtDhT,EAAAA,EAAAA,IAA4D,OAA5DiT,GAAwB,KAAC1R,EAAAA,EAAAA,IAAEqR,EAAIE,MAAMI,eAAe,IAAC,GACpBN,EAAIO,W,WAArC5L,EAAAA,EAAAA,IAEO,OAFP6L,GAEO,EADLvT,EAAAA,EAAAA,IAAkFkB,GAAA,CAAxEiB,KAAM,IAAE,C,iBAAE,IAAoD,G,WAApDd,EAAAA,EAAAA,KAAoDmS,EAAAA,EAAAA,IAApC1S,GAAA2S,mBAAmBV,EAAIO,e,8BAG7CP,EAAIW,WAAaX,EAAIW,UAAUC,c,WAA/CjM,EAAAA,EAAAA,IAIW+K,EAAAA,GAAA,CAAAtH,IAAA,KAHThL,EAAAA,EAAAA,IAAsG,QAA/FL,OAAKgP,EAAAA,EAAAA,IAAA,CAAEhO,GAAA8S,eAAeb,EAAIW,UAAUlK,QAAe,iB,QAAeuJ,EAAIW,UAAUlK,QAAM,IAC7FrJ,EAAAA,EAAAA,IAAkD,OAAlD0T,IAAkDnS,EAAAA,EAAAA,IAA1BqR,EAAIW,UAAUI,KAAG,GAC7Bf,EAAIW,UAAUK,S,WAA1BrM,EAAAA,EAAAA,IAAkI,Q,MAA/F5H,OAAKgP,EAAAA,EAAAA,IAAA,CAAEhO,GAAAkT,eAAejB,EAAIW,UAAUK,QAAe,iB,QAAehB,EAAIW,UAAUK,QAAM,K,kCAG3HrM,EAAAA,EAAAA,IAAuD,OAAvDuM,IAAuDvS,EAAAA,EAAAA,IAApBqR,EAAImB,SAAO,IAGtCnB,EAAIoB,U,WADZ9S,EAAAA,EAAAA,IAOYV,GAAA,C,MALVC,KAAK,OACLuB,KAAK,QACJtB,QAAKkB,GAAEgR,EAAIqB,aAAerB,EAAIqB,YAC/BtU,MAAM,kB,kBACN,IAAmC,E,iBAAhCiT,EAAIqB,YAAc,KAAO,MAAV,K,wCAGTrB,EAAIoB,SAAWpB,EAAIqB,c,WAA9B1M,EAAAA,EAAAA,IAEM,MAFN2M,GAEM,EADJlU,EAAAA,EAAAA,IAA0B,YAAAuB,EAAAA,EAAAA,IAAnBqR,EAAIoB,SAAO,O,2BAGa,IAAxBrT,GAAA8R,aAAaC,S,WAAxBnL,EAAAA,EAAAA,IAMM,MANN4M,GAMM,EALJnU,EAAAA,EAAAA,IAIM,MAJNoU,GAIM,EAHJvU,EAAAA,EAAAA,IAAsDkB,GAAA,CAA7CpB,MAAM,kBAAgB,C,iBAAC,IAAY,EAAZE,EAAAA,EAAAA,IAAY4G,M,uBAC5CzG,EAAAA,EAAAA,IAAoC,KAAjCL,MAAM,kBAAiB,UAAM,I,iBAChCK,EAAAA,EAAAA,IAA2C,KAAxCL,MAAM,kBAAiB,iBAAa,U,iDAUnDE,EAAAA,EAAAA,IAwDYwU,GAAA,C,WAvDDxT,GAAAyT,mBAAmBC,Q,uCAAnB1T,GAAAyT,mBAAmBC,QAAO3S,GACnC2L,MAAM,SACN7D,MAAM,MACL,oBAAkB,G,kBAEnB,IAiDM,EAjDN1J,EAAAA,EAAAA,IAiDM,MAjDNwU,GAiDM,EAhDJxU,EAAAA,EAAAA,IAcM,MAdNyU,GAcM,C,iBAbJzU,EAAAA,EAAAA,IAAa,UAAT,QAAI,KACRA,EAAAA,EAAAA,IAWM,MAXN0U,GAWM,EAVJ7U,EAAAA,EAAAA,IASc8U,GAAA,CARZlU,KAAK,SACJmU,WAAY/T,GAAAyT,mBAAmBO,kBAC/BC,MAAOnU,GAAAoU,cAAclU,GAAAyT,mBAAmBO,mBACxCnL,MAAO,K,CAEGK,SAAO1D,EAAAA,EAAAA,IAChB,EADoBuO,gBAAU,EAC9B5U,EAAAA,EAAAA,IAAoD,OAApDgV,IAAoDzT,EAAAA,EAAAA,IAAxBqT,GAAa,OAAI,K,oCAMrD5U,EAAAA,EAAAA,IAcM,MAdNiV,GAcM,C,iBAbJjV,EAAAA,EAAAA,IAAc,UAAV,SAAK,IACEa,GAAAyT,mBAAmBY,YAAYxC,OAAS,I,WAAnDnL,EAAAA,EAAAA,IAUM,MAAA4N,GAAA,G,aATJ5N,EAAAA,EAAAA,IAQE+K,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAP8B1R,GAAAyT,mBAAmBY,YAAW,CAApDE,EAAY1P,M,WADtBxE,EAAAA,EAAAA,IAQEoM,GAAA,CANCtC,IAAKtF,EACL6H,MAAO6H,EAAW3U,KAClB+M,YAAa4H,EAAW5H,YACxB/M,KAAME,GAAA0U,gBAAgBD,EAAWE,UAClC,eACA1V,MAAA,0B,+DAGJsB,EAAAA,EAAAA,IAA2C2M,GAAA,C,MAA1BL,YAAY,kBAG/BxN,EAAAA,EAAAA,IAeM,MAfNuV,GAeM,C,iBAdJvV,EAAAA,EAAAA,IAAa,UAAT,QAAI,IACGa,GAAAyT,mBAAmBkB,gBAAgB9C,OAAS,I,WAAvDnL,EAAAA,EAAAA,IAWM,MAAAkO,GAAA,EAVJzV,EAAAA,EAAAA,IASK,KATL0V,GASK,G,aARHnO,EAAAA,EAAAA,IAOK+K,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAN+B1R,GAAAyT,mBAAmBkB,gBAAe,CAA5DG,EAAgBjQ,M,WAD1B6B,EAAAA,EAAAA,IAOK,MALFyD,IAAKtF,EACN/F,MAAM,uB,EAENE,EAAAA,EAAAA,IAAwDkB,GAAA,CAA/CpB,MAAM,uBAAqB,C,iBAAC,IAAS,EAATE,EAAAA,EAAAA,IAASkG,M,eAAU,KACxDxE,EAAAA,EAAAA,IAAGoU,GAAc,O,wBAIvBzU,EAAAA,EAAAA,IAAwC2M,GAAA,C,MAAvBL,YAAY,kB,wBAMnC3N,EAAAA,EAAAA,IA+HYwU,GAAA,C,WA9HDxT,GAAA+U,iBAAiBrB,Q,uCAAjB1T,GAAA+U,iBAAiBrB,QAAO3S,GACjC2L,MAAM,SACN7D,MAAM,MACL,oBAAkB,G,kBAEnB,IAwHM,CAxHmC7I,GAAA+U,iBAAiBC,a,WAA1DtO,EAAAA,EAAAA,IAwHM,MAxHNuO,GAwHM,EAtHJjW,EAAAA,EAAAA,IA6CUM,GAAA,CA7CDR,MAAM,iBAAiBoW,OAAO,S,CAC1BC,QAAM3P,EAAAA,EAAAA,IACf,IAGM,EAHNrG,EAAAA,EAAAA,IAGM,MAHNiW,GAGM,EAFJpW,EAAAA,EAAAA,IAA8BkB,GAAA,M,iBAArB,IAAW,EAAXlB,EAAAA,EAAAA,IAAWyH,M,+BAAU,e,iBAIlC,IAqCS,EArCTzH,EAAAA,EAAAA,IAqCSqW,GAAA,CArCAC,OAAQ,IAAE,C,iBACjB,IAKS,EALTtW,EAAAA,EAAAA,IAKSuW,GAAA,CALAC,KAAM,IAAE,C,iBACf,IAGM,EAHNrW,EAAAA,EAAAA,IAGM,MAHNsW,GAGM,C,iBAFJtW,EAAAA,EAAAA,IAAuC,QAAjCL,MAAM,gBAAe,SAAK,KAChCK,EAAAA,EAAAA,IAAqF,OAArFuW,IAAqFhV,EAAAA,EAAAA,IAAvDV,GAAA+U,iBAAiBC,WAAW/U,UAAY,OAAJ,O,OAGtEjB,EAAAA,EAAAA,IAKSuW,GAAA,CALAC,KAAM,IAAE,C,iBACf,IAGM,EAHNrW,EAAAA,EAAAA,IAGM,MAHNwW,GAGM,C,iBAFJxW,EAAAA,EAAAA,IAAuC,QAAjCL,MAAM,gBAAe,SAAK,KAChCK,EAAAA,EAAAA,IAA6F,OAA7FyW,IAA6FlV,EAAAA,EAAAA,IAA/DZ,GAAAsH,gBAAgBpH,GAAA+U,iBAAiBC,WAAW5U,WAAQ,O,OAGtFpB,EAAAA,EAAAA,IAKSuW,GAAA,CALAC,KAAM,IAAE,C,iBACf,IAGM,EAHNrW,EAAAA,EAAAA,IAGM,MAHN0W,GAGM,C,iBAFJ1W,EAAAA,EAAAA,IAAuC,QAAjCL,MAAM,gBAAe,SAAK,KAChCK,EAAAA,EAAAA,IAAiG,OAAjG2W,IAAiGpV,EAAAA,EAAAA,IAAnEZ,GAAAwH,kBAAkBtH,GAAA+U,iBAAiBC,WAAWe,aAAU,O,OAG1F/W,EAAAA,EAAAA,IAKSuW,GAAA,CALAC,KAAM,IAAE,C,iBACf,IAGM,EAHNrW,EAAAA,EAAAA,IAGM,MAHN6W,GAGM,C,iBAFJ7W,EAAAA,EAAAA,IAAuC,QAAjCL,MAAM,gBAAe,SAAK,KAChCK,EAAAA,EAAAA,IAA4G,OAA5G8W,IAA4GvV,EAAAA,EAAAA,IAA9EZ,GAAAyH,uBAAuBvH,GAAA+U,iBAAiBC,WAAWxN,mBAAgB,O,OAGrGxI,EAAAA,EAAAA,IAKSuW,GAAA,CALAC,KAAM,IAAE,C,iBACf,IAGM,EAHNrW,EAAAA,EAAAA,IAGM,MAHN+W,GAGM,C,iBAFJ/W,EAAAA,EAAAA,IAAsC,QAAhCL,MAAM,gBAAe,QAAI,KAC/BK,EAAAA,EAAAA,IAAmF,OAAnFgX,IAAmFzV,EAAAA,EAAAA,IAArDV,GAAA+U,iBAAiBC,WAAWoB,SAAW,MAAJ,O,OAGrEpX,EAAAA,EAAAA,IAKSuW,GAAA,CALAC,KAAM,IAAE,C,iBACf,IAGM,EAHNrW,EAAAA,EAAAA,IAGM,MAHNkX,GAGM,C,iBAFJlX,EAAAA,EAAAA,IAAuC,QAAjCL,MAAM,gBAAe,SAAK,KAChCK,EAAAA,EAAAA,IAA2F,OAA3FmX,IAA2F5V,EAAAA,EAAAA,IAA7DZ,GAAA2D,WAAWzD,GAAA+U,iBAAiBC,WAAWtR,cAAW,O,uBAOxF1E,EAAAA,EAAAA,IAiCUM,GAAA,CAjCDR,MAAM,iBAAiBoW,OAAO,S,CAC1BC,QAAM3P,EAAAA,EAAAA,IACf,IAGM,EAHNrG,EAAAA,EAAAA,IAGM,MAHNoX,GAGM,EAFJvX,EAAAA,EAAAA,IAAmCkB,GAAA,M,iBAA1B,IAAgB,EAAhBlB,EAAAA,EAAAA,IAAgBuH,M,+BAAU,e,iBAIvC,IAyBS,EAzBTvH,EAAAA,EAAAA,IAyBSqW,GAAA,CAzBAC,OAAQ,IAAE,C,iBACjB,IAKS,EALTtW,EAAAA,EAAAA,IAKSuW,GAAA,CALAC,KAAM,IAAE,C,iBACf,IAGM,EAHNrW,EAAAA,EAAAA,IAGM,MAHNqX,GAGM,C,iBAFJrX,EAAAA,EAAAA,IAAwC,QAAlCL,MAAM,gBAAe,UAAM,KACjCK,EAAAA,EAAAA,IAAkI,OAAlIsX,IAAkI/V,EAAAA,EAAAA,IAApGV,GAAA+U,iBAAiBC,WAAW0B,kBAAoB1W,GAAA+U,iBAAiBC,WAAW2B,OAAS,OAAJ,O,OAGnH3X,EAAAA,EAAAA,IAKSuW,GAAA,CALAC,KAAM,IAAE,C,iBACf,IAGM,EAHNrW,EAAAA,EAAAA,IAGM,MAHNyX,GAGM,C,iBAFJzX,EAAAA,EAAAA,IAAuC,QAAjCL,MAAM,gBAAe,SAAK,KAChCK,EAAAA,EAAAA,IAAsF,OAAtF0X,IAAsFnW,EAAAA,EAAAA,IAAxDV,GAAA+U,iBAAiBC,WAAW9Q,UAAY,OAAQ,IAAC,O,OAGnFlF,EAAAA,EAAAA,IAKSuW,GAAA,CALAC,KAAM,IAAE,C,iBACf,IAGM,EAHNrW,EAAAA,EAAAA,IAGM,MAHN2X,GAGM,C,iBAFJ3X,EAAAA,EAAAA,IAAuC,QAAjCL,MAAM,gBAAe,SAAK,KAChCK,EAAAA,EAAAA,IAA+H,OAA/H4X,IAA+HrW,EAAAA,EAAAA,IAAjGV,GAAA+U,iBAAiBC,WAAWgC,YAAchX,GAAA+U,iBAAiBC,WAAWiC,SAAW,OAAQ,IAAC,O,OAG5HjY,EAAAA,EAAAA,IAKSuW,GAAA,CALAC,KAAM,IAAE,C,iBACf,IAGM,EAHNrW,EAAAA,EAAAA,IAGM,MAHN+X,GAGM,C,iBAFJ/X,EAAAA,EAAAA,IAAwC,QAAlCL,MAAM,gBAAe,UAAM,KACjCK,EAAAA,EAAAA,IAAsF,OAAtFgY,IAAsFzW,EAAAA,EAAAA,IAAxDV,GAAA+U,iBAAiBC,WAAWoC,WAAa,OAAJ,O,sBAOtBpX,GAAA+U,iBAAiBC,WAAWqC,c,WAAjFhX,EAAAA,EAAAA,IAiCUf,GAAA,C,MAjCDR,MAAM,iBAAiBoW,OAAO,S,CAC1BC,QAAM3P,EAAAA,EAAAA,IACf,IAGM,EAHNrG,EAAAA,EAAAA,IAGM,MAHNmY,GAGM,EAFJtY,EAAAA,EAAAA,IAA8BkB,GAAA,M,iBAArB,IAAW,EAAXlB,EAAAA,EAAAA,IAAW6G,M,+BAAU,e,iBAIlC,IAyBS,EAzBT7G,EAAAA,EAAAA,IAyBSqW,GAAA,CAzBAC,OAAQ,IAAE,C,iBACjB,IAKS,EALTtW,EAAAA,EAAAA,IAKSuW,GAAA,CALAC,KAAM,IAAE,C,iBACf,IAGM,EAHNrW,EAAAA,EAAAA,IAGM,MAHNoY,GAGM,C,iBAFJpY,EAAAA,EAAAA,IAAuC,QAAjCL,MAAM,gBAAe,SAAK,KAChCK,EAAAA,EAAAA,IAA8F,OAA9FqY,IAA8F9W,EAAAA,EAAAA,IAAhEV,GAAA+U,iBAAiBC,WAAWqC,YAAY5O,MAAQ,QAAJ,O,OAG9EzJ,EAAAA,EAAAA,IAKSuW,GAAA,CALAC,KAAM,IAAE,C,iBACf,IAGM,EAHNrW,EAAAA,EAAAA,IAGM,MAHNsY,GAGM,C,iBAFJtY,EAAAA,EAAAA,IAAwC,QAAlCL,MAAM,gBAAe,UAAM,KACjCK,EAAAA,EAAAA,IAAiJ,OAAjJuY,IAAiJhX,EAAAA,EAAAA,IAAnHV,GAAA+U,iBAAiBC,WAAWqC,YAAYM,UAAY3X,GAAA+U,iBAAiBC,WAAWqC,YAAYO,MAAQ,OAAJ,O,OAGlI5Y,EAAAA,EAAAA,IAKSuW,GAAA,CALAC,KAAM,IAAE,C,iBACf,IAGM,EAHNrW,EAAAA,EAAAA,IAGM,MAHN0Y,GAGM,C,iBAFJ1Y,EAAAA,EAAAA,IAAqC,QAA/BL,MAAM,gBAAe,OAAG,KAC9BK,EAAAA,EAAAA,IAAkG,OAAlG2Y,IAAkGpX,EAAAA,EAAAA,IAApEV,GAAA+U,iBAAiBC,WAAWqC,YAAYU,UAAY,QAAJ,O,OAGlF/Y,EAAAA,EAAAA,IAKSuW,GAAA,CALAC,KAAM,IAAE,C,iBACf,IAGM,EAHNrW,EAAAA,EAAAA,IAGM,MAHN6Y,GAGM,C,iBAFJ7Y,EAAAA,EAAAA,IAAuC,QAAjCL,MAAM,gBAAe,SAAK,KAChCK,EAAAA,EAAAA,IAAuI,OAAvI8Y,IAAuIvX,EAAAA,EAAAA,IAAzGV,GAAA+U,iBAAiBC,WAAWqC,YAAYa,SAAWlY,GAAA+U,iBAAiBC,WAAWkD,SAAW,MAAO,IAAC,O,oFAS5IlZ,EAAAA,EAAAA,IAgKYwU,GAAA,C,WA/JDxT,GAAAmY,uBAAuBzE,Q,uCAAvB1T,GAAAmY,uBAAuBzE,QAAO3S,GACvC2L,MAAM,SACN7D,MAAM,QACN,eAAa,6BACZuP,UAAU,EACV,eAActY,GAAAuY,mBACfC,IAAI,MACJC,OAAA,GACA,oBACA,uB,CA8IWC,QAAMhT,EAAAA,EAAAA,IACf,IAKM,EALNrG,EAAAA,EAAAA,IAKM,MALNsZ,GAKM,EAJJzZ,EAAAA,EAAAA,IAA2DW,GAAA,CAA/CE,QAAOC,GAAAuY,mBAAoBK,MAAA,I,kBAAM,IAAEtX,EAAA,OAAAA,EAAA,O,QAAF,S,8BAC7CpC,EAAAA,EAAAA,IAEYW,GAAA,CAFDC,KAAK,UAAWC,QAAOC,GAAA6Y,yB,kBAChC,IAA+B,EAA/B3Z,EAAAA,EAAAA,IAA+BkB,GAAA,M,iBAAtB,IAAY,EAAZlB,EAAAA,EAAAA,IAAY4Z,M,+BAAU,a,kDAhJrC,IA0IM,EA1INzZ,EAAAA,EAAAA,IA0IM,MA1IN0Z,GA0IM,EAzIJ1Z,EAAAA,EAAAA,IAMM,MANN2Z,GAMM,EALJ3Z,EAAAA,EAAAA,IAGM,MAHN4Z,GAGM,EAFJ/Z,EAAAA,EAAAA,IAAqDkB,GAAA,CAA5CpB,MAAM,qBAAmB,C,iBAAC,IAAQ,EAARE,EAAAA,EAAAA,IAAQoH,M,uBAC3CjH,EAAAA,EAAAA,IAAqB,YAAf,YAAQ,M,iBAEhBA,EAAAA,EAAAA,IAA4D,OAAvDL,MAAM,yBAAwB,uBAAmB,OAGxDE,EAAAA,EAAAA,IAgIQga,GAAA,CAhIEC,MAAOjZ,GAAAmY,uBAAyBe,MAAOlZ,GAAAmZ,kBAAmBjY,IAAI,sBAAsB,iBAAe,MAAMpC,MAAM,qB,kBACvH,IAQa,EARbE,EAAAA,EAAAA,IAQaoa,GAAA,CARCrQ,KAAK,OAAOC,MAAM,Q,kBAC9B,IAME,EANFhK,EAAAA,EAAAA,IAME8B,GAAA,C,WALSd,GAAAmY,uBAAuB1P,K,uCAAvBzI,GAAAmY,uBAAuB1P,KAAI1H,GACpCsY,UAAU,KACVC,UAAU,IACVjO,YAAY,sBACZvM,MAAM,sB,gCAIVE,EAAAA,EAAAA,IAsCaoa,GAAA,CAtCCrQ,KAAK,WAAWC,MAAM,U,kBAClC,IAoCI,EApCJ7J,EAAAA,EAAAA,IAoCI,MApCJoa,GAoCI,EAnCJpa,EAAAA,EAAAA,IAOM,OANFL,OAAKgP,EAAAA,EAAAA,IAAA,CAAC,2BAA0B,UAC0B,UAApC9N,GAAAmY,uBAAuBqB,YAC9C3Z,QAAKuB,EAAA,MAAAA,EAAA,IAAAL,GAAEf,GAAAmY,uBAAuBqB,SAAW,U,EAExCra,EAAAA,EAAAA,IAAwE,MAAxEsa,GAAwE,EAApCza,EAAAA,EAAAA,IAA8BkB,GAAA,M,iBAArB,IAAW,EAAXlB,EAAAA,EAAAA,IAAW0a,M,yBACxDva,EAAAA,EAAAA,IAAe,YAAT,MAAE,K,IAGZA,EAAAA,EAAAA,IAOM,OANFL,OAAKgP,EAAAA,EAAAA,IAAA,CAAC,2BAA0B,UAC0B,YAApC9N,GAAAmY,uBAAuBqB,YAC9C3Z,QAAKuB,EAAA,MAAAA,EAAA,IAAAL,GAAEf,GAAAmY,uBAAuBqB,SAAW,Y,EAExCra,EAAAA,EAAAA,IAAuE,MAAvEwa,GAAuE,EAAjC3a,EAAAA,EAAAA,IAA2BkB,GAAA,M,iBAAlB,IAAQ,EAARlB,EAAAA,EAAAA,IAAQ4a,M,yBACvDza,EAAAA,EAAAA,IAAoB,YAAd,WAAO,K,IAGjBA,EAAAA,EAAAA,IAOM,OANFL,OAAKgP,EAAAA,EAAAA,IAAA,CAAC,2BAA0B,UAC0B,aAApC9N,GAAAmY,uBAAuBqB,YAC9C3Z,QAAKuB,EAAA,MAAAA,EAAA,IAAAL,GAAEf,GAAAmY,uBAAuBqB,SAAW,a,EAExCra,EAAAA,EAAAA,IAAgF,MAAhF0a,GAAgF,EAAzC7a,EAAAA,EAAAA,IAAmCkB,GAAA,M,iBAA1B,IAAgB,EAAhBlB,EAAAA,EAAAA,IAAgB8a,M,yBAChE3a,EAAAA,EAAAA,IAAe,YAAT,MAAE,K,IAGZA,EAAAA,EAAAA,IAOM,OANFL,OAAKgP,EAAAA,EAAAA,IAAA,CAAC,2BAA0B,UAC0B,WAApC9N,GAAAmY,uBAAuBqB,YAC9C3Z,QAAKuB,EAAA,MAAAA,EAAA,IAAAL,GAAEf,GAAAmY,uBAAuBqB,SAAW,W,EAExCra,EAAAA,EAAAA,IAA2E,MAA3E4a,GAA2E,EAAtC/a,EAAAA,EAAAA,IAAgCkB,GAAA,M,iBAAvB,IAAa,EAAblB,EAAAA,EAAAA,IAAagb,M,yBAC3D7a,EAAAA,EAAAA,IAAe,YAAT,MAAE,K,aAKkCa,GAAAmY,uBAAuBqB,UAAgD,UAApCxZ,GAAAmY,uBAAuBqB,W,WAA1GnZ,EAAAA,EAAAA,IAaa+Y,GAAA,C,MAbCrQ,KAAK,UAAUC,MAAM,Q,kBACjC,IAWS,EAXThK,EAAAA,EAAAA,IAWS8B,GAAA,C,WAVEd,GAAAmY,uBAAuB8B,Q,uCAAvBja,GAAAmY,uBAAuB8B,QAAOlZ,GACvCuY,UAAU,IACVjO,YAAY,eACZvM,MAAM,sB,CAEGoS,QAAM1L,EAAAA,EAAAA,IACf,IAAgF,CAA7B,YAApCxF,GAAAmY,uBAAuBqB,W,WAAtCnZ,EAAAA,EAAAA,IAAgFH,GAAA,CAAAiK,IAAA,I,iBAAlB,IAAQ,EAARnL,EAAAA,EAAAA,IAAQ4a,M,uBACnB,aAApC5Z,GAAAmY,uBAAuBqB,W,WAAtCnZ,EAAAA,EAAAA,IAAyFH,GAAA,CAAAiK,IAAA,I,iBAA1B,IAAgB,EAAhBnL,EAAAA,EAAAA,IAAgB8a,M,uBAC5B,WAApC9Z,GAAAmY,uBAAuBqB,W,WAAtCnZ,EAAAA,EAAAA,IAAoFH,GAAA,CAAAiK,IAAA,I,iBAAvB,IAAa,EAAbnL,EAAAA,EAAAA,IAAagb,M,yEAKM,UAApCha,GAAAmY,uBAAuBqB,W,WAAzEnZ,EAAAA,EAAAA,IAkBe+Y,GAAA,C,MAlBDrQ,KAAK,aAAaC,MAAM,O,kBAClC,IAeU,EAfVhK,EAAAA,EAAAA,IAeUoS,GAAA,CAdR8I,SAAA,G,WACSla,GAAAmY,uBAAuBgC,W,uCAAvBna,GAAAmY,uBAAuBgC,WAAUpZ,GAC1CsK,YAAY,SACZtM,MAAA,eACA,mBACA,2BACAD,MAAM,uB,kBAER,IAAqL,G,WAArLuB,EAAAA,EAAAA,IAAqLiR,GAAA,CAAzKtI,MAAO,OAASuI,MAAO,OAASpH,IAAK,OAASnE,SAAUhG,GAAAmY,uBAAuBgC,WAAWtI,OAAS,IAAM7R,GAAAmY,uBAAuBgC,WAAWC,SAAS,S,uBAChKpb,EAAAA,EAAAA,IAIasS,GAAA,CAHXtI,MAAM,oBACNuI,MAAM,oBACLvL,SAAUhG,GAAAmY,uBAAuBgC,WAAWC,SAAS,S,+DAGxDjb,EAAAA,EAAAA,IAA2D,OAAtDL,MAAM,oBAAmB,2BAAuB,M,iCAGvDK,EAAAA,EAAAA,IAyCM,MAzCNkb,GAyCM,EAxCJlb,EAAAA,EAAAA,IAGM,MAHNmb,GAGM,EAFJtb,EAAAA,EAAAA,IAA+BkB,GAAA,M,iBAAtB,IAAY,EAAZlB,EAAAA,EAAAA,IAAY4G,M,uBACrBzG,EAAAA,EAAAA,IAAiB,YAAX,QAAI,OAEZA,EAAAA,EAAAA,IAmCM,MAnCNob,GAmCM,EAlCJpb,EAAAA,EAAAA,IAGM,MAHNqb,GAGM,C,iBAFJrb,EAAAA,EAAAA,IAAqC,QAA/BL,MAAM,cAAa,SAAK,KAC9BK,EAAAA,EAAAA,IAA2D,OAA3Dsb,IAA2D/Z,EAAAA,EAAAA,IAA/BV,GAAA+B,WAAW2Y,YAAU,MAEnDvb,EAAAA,EAAAA,IAOE,MAPFwb,GAOE,C,iBANAxb,EAAAA,EAAAA,IAAqC,QAA/BL,MAAM,cAAa,SAAK,KAC9BK,EAAAA,EAAAA,IAIO,OAJPyb,GAIO,EAHL5b,EAAAA,EAAAA,IAEYW,GAAA,CAFAC,KAAkC,MAA5BI,GAAA+B,WAAW8Y,aAAuB,UAAY,OAAQ1Z,KAAK,S,kBAC/E,IAA4C,E,iBAAzCrB,GAAAgb,cAAc9a,GAAA+B,WAAW8Y,eAAY,K,sBAI1C1b,EAAAA,EAAAA,IAqBM,MArBN4b,GAqBM,EApBJ5b,EAAAA,EAAAA,IASM,MATN6b,GASM,EARJ7b,EAAAA,EAAAA,IAGM,MAHN8b,GAGM,C,iBAFJ9b,EAAAA,EAAAA,IAAoC,OAA/BL,MAAM,gBAAe,QAAI,KAC9BK,EAAAA,EAAAA,IAAoF,MAApF+b,IAAoFxa,EAAAA,EAAAA,IAAvDZ,GAAAsC,mBAAmBpC,GAAA+B,WAAWM,kBAAe,MAE5ElD,EAAAA,EAAAA,IAGM,MAHNgc,GAGM,C,iBAFJhc,EAAAA,EAAAA,IAAmC,OAA9BL,MAAM,gBAAe,OAAG,KAC7BK,EAAAA,EAAAA,IAAuD,MAAvDic,IAAuD1a,EAAAA,EAAAA,IAA1BZ,GAAAub,kBAAmB,IAAC,QAGrDlc,EAAAA,EAAAA,IASM,MATNmc,GASM,EARJnc,EAAAA,EAAAA,IAGM,MAHNoc,GAGM,C,iBAFJpc,EAAAA,EAAAA,IAAqC,OAAhCL,MAAM,gBAAe,SAAK,KAC/BK,EAAAA,EAAAA,IAAyE,MAAzEqc,IAAyE9a,EAAAA,EAAAA,KAA3CV,GAAA+B,WAAWC,QAAU,GAAGC,QAAQ,IAAD,MAE/D9C,EAAAA,EAAAA,IAGM,MAHNsc,GAGM,C,iBAFJtc,EAAAA,EAAAA,IAAoC,OAA/BL,MAAM,gBAAe,QAAI,KAC9BK,EAAAA,EAAAA,IAAyF,MAAzFuc,IAAyFhb,EAAAA,EAAAA,IAA5DV,GAAA+B,WAAW4Z,UAAY3b,GAAA+B,WAAW6Z,UAAY,OAAJ,e,qEAoBvF5c,EAAAA,EAAAA,IAqGYwU,GAAA,C,WApGDxT,GAAA6b,mBAAmBnI,Q,uCAAnB1T,GAAA6b,mBAAmBnI,QAAO3S,GAClC2L,MAAO,UACR7D,MAAM,QACN,uB,CA2FW2P,QAAMhT,EAAAA,EAAAA,IACf,IAGO,EAHPrG,EAAAA,EAAAA,IAGO,OAHP2c,GAGO,EAFL9c,EAAAA,EAAAA,IAAqEW,GAAA,CAAzDE,QAAKuB,EAAA,MAAAA,EAAA,IAAAL,GAAEf,GAAA6b,mBAAmBnI,SAAU,I,kBAAO,IAAEtS,EAAA,OAAAA,EAAA,O,QAAF,S,gBACvDpC,EAAAA,EAAAA,IAA0GW,GAAA,CAA/FC,KAAK,UAAWC,QAAOC,GAAAic,mBAAqBC,QAAShc,GAAA6b,mBAAmBG,S,kBAAS,IAAE5a,EAAA,OAAAA,EAAA,O,QAAF,S,4DA5FhG,IAuFU,EAvFVpC,EAAAA,EAAAA,IAuFUga,GAAA,CAvFAC,MAAOjZ,GAAA6b,mBAAmBI,KAAO/C,MAAOlZ,GAAAkc,kBAAmBhb,IAAI,kBAAkB,cAAY,S,kBACrG,IAEe,EAFflC,EAAAA,EAAAA,IAEeoa,GAAA,CAFDpQ,MAAM,QAAQD,KAAK,Q,kBAC/B,IAAmF,EAAnF/J,EAAAA,EAAAA,IAAmF8B,GAAA,C,WAAhEd,GAAA6b,mBAAmBI,KAAKxT,K,uCAAxBzI,GAAA6b,mBAAmBI,KAAKxT,KAAI1H,GAAEsK,YAAY,Y,gCAG/DrM,EAAAA,EAAAA,IAOeoa,GAAA,CAPDpQ,MAAM,KAAKD,KAAK,e,kBAC5B,IAKW,EALX/J,EAAAA,EAAAA,IAKW8B,GAAA,C,WAJAd,GAAA6b,mBAAmBI,KAAKtP,Y,uCAAxB3M,GAAA6b,mBAAmBI,KAAKtP,YAAW5L,GAC5CnB,KAAK,WACJuL,KAAM,EACPE,YAAY,Y,gCAIhBrM,EAAAA,EAAAA,IAAuD8H,GAAA,CAA3C,mBAAiB,UAAQ,C,iBAAC,IAAI1F,EAAA,OAAAA,EAAA,O,QAAJ,W,gBAEtCpC,EAAAA,EAAAA,IAqBSqW,GAAA,CArBAC,OAAQ,IAAE,C,iBACjB,IASS,EATTtW,EAAAA,EAAAA,IASSuW,GAAA,CATAC,KAAM,IAAE,C,iBACf,IAOe,EAPfxW,EAAAA,EAAAA,IAOeoa,GAAA,CAPDpQ,MAAM,WAAWD,KAAK,qB,kBAClC,IAKkB,EALlB/J,EAAAA,EAAAA,IAKkBmd,GAAA,C,WAJPnc,GAAA6b,mBAAmBI,KAAKG,kB,uCAAxBpc,GAAA6b,mBAAmBI,KAAKG,kBAAiBrb,GACjDsb,UAAW,EACXC,IAAK,EACNvd,MAAA,gB,wCAINC,EAAAA,EAAAA,IASSuW,GAAA,CATAC,KAAM,IAAE,C,iBACf,IAOe,EAPfxW,EAAAA,EAAAA,IAOeoa,GAAA,CAPDpQ,MAAM,MAAMD,KAAK,W,kBAC7B,IAKkB,EALlB/J,EAAAA,EAAAA,IAKkBmd,GAAA,C,WAJPnc,GAAA6b,mBAAmBI,KAAKM,Q,uCAAxBvc,GAAA6b,mBAAmBI,KAAKM,QAAOxb,GACvCsb,UAAW,EACXC,IAAK,EACNvd,MAAA,gB,gDAMRC,EAAAA,EAAAA,IAuBSqW,GAAA,CAvBAC,OAAQ,IAAE,C,iBACjB,IAUS,EAVTtW,EAAAA,EAAAA,IAUSuW,GAAA,CAVAC,KAAM,IAAE,C,iBACf,IAQe,EARfxW,EAAAA,EAAAA,IAQeoa,GAAA,CARDpQ,MAAM,SAASD,KAAK,gB,kBAChC,IAMkB,EANlB/J,EAAAA,EAAAA,IAMkBmd,GAAA,C,WALPnc,GAAA6b,mBAAmBI,KAAKO,a,uCAAxBxc,GAAA6b,mBAAmBI,KAAKO,aAAYzb,GAC5Csb,UAAW,EACXC,IAAK,EACLG,IAAK,IACN1d,MAAA,gB,wCAINC,EAAAA,EAAAA,IAUSuW,GAAA,CAVAC,KAAM,IAAE,C,iBACf,IAQe,EARfxW,EAAAA,EAAAA,IAQeoa,GAAA,CARDpQ,MAAM,YAAYD,KAAK,W,kBACnC,IAMkB,EANlB/J,EAAAA,EAAAA,IAMkBmd,GAAA,C,WALPnc,GAAA6b,mBAAmBI,KAAKS,Q,uCAAxB1c,GAAA6b,mBAAmBI,KAAKS,QAAO3b,GACvCsb,UAAW,EACXC,IAAK,EACLG,IAAK,IACN1d,MAAA,gB,gDAMRC,EAAAA,EAAAA,IAiBSqW,GAAA,CAjBAC,OAAQ,IAAE,C,iBACjB,IAUS,EAVTtW,EAAAA,EAAAA,IAUSuW,GAAA,CAVAC,KAAM,IAAE,C,iBACf,IAQe,EARfxW,EAAAA,EAAAA,IAQeoa,GAAA,CARDpQ,MAAM,WAAWD,KAAK,c,kBAClC,IAMkB,EANlB/J,EAAAA,EAAAA,IAMkBmd,GAAA,C,WALPnc,GAAA6b,mBAAmBI,KAAKU,W,uCAAxB3c,GAAA6b,mBAAmBI,KAAKU,WAAU5b,GAC1Csb,UAAW,EACXC,IAAK,EACLG,IAAK,IACN1d,MAAA,gB,wCAINC,EAAAA,EAAAA,IAISuW,GAAA,CAJAC,KAAM,IAAE,C,iBACf,IAEe,EAFfxW,EAAAA,EAAAA,IAEeoa,GAAA,CAFDpQ,MAAM,QAAM,C,iBACxB,IAAmE,EAAnEhK,EAAAA,EAAAA,IAAmE4R,GAAA,C,WAA/C5Q,GAAA6b,mBAAmBI,KAAKW,U,uCAAxB5c,GAAA6b,mBAAmBI,KAAKW,UAAS7b,I,gDAK3D5B,EAAAA,EAAAA,IAGM,MAHN0d,GAGM,EAFJ7d,EAAAA,EAAAA,IAAiCkB,GAAA,M,iBAAxB,IAAc,EAAdlB,EAAAA,EAAAA,IAAc8d,M,uBACvB3d,EAAAA,EAAAA,IAAiC,YAA3B,wBAAoB,Q,oDAahCH,EAAAA,EAAAA,IA6BYwU,GAAA,C,WA5BDxT,GAAA+c,0BAA0BrJ,Q,uCAA1B1T,GAAA+c,0BAA0BrJ,QAAO3S,GAC1C2L,MAAM,YACN7D,MAAM,QACN,uB,CAmBW2P,QAAMhT,EAAAA,EAAAA,IACf,IAGO,EAHPrG,EAAAA,EAAAA,IAGO,OAHP6d,GAGO,EAFLhe,EAAAA,EAAAA,IAA4EW,GAAA,CAAhEE,QAAKuB,EAAA,MAAAA,EAAA,IAAAL,GAAEf,GAAA+c,0BAA0BrJ,SAAU,I,kBAAO,IAAEtS,EAAA,OAAAA,EAAA,O,QAAF,S,gBAC9DpC,EAAAA,EAAAA,IAAsHW,GAAA,CAA3GC,KAAK,UAAWC,QAAOC,GAAAmd,sBAAwBjB,QAAShc,GAAA+c,0BAA0Bf,S,kBAAS,IAAI5a,EAAA,OAAAA,EAAA,O,QAAJ,W,4DApB1G,IAeU,EAfVpC,EAAAA,EAAAA,IAeUga,GAAA,CAfD,cAAY,SAAO,C,iBAC1B,IAae,EAbfha,EAAAA,EAAAA,IAaeoa,GAAA,CAbDpQ,MAAM,OAAK,C,iBACvB,IAWY,EAXZhK,EAAAA,EAAAA,IAWYoS,GAAA,C,WAXQpR,GAAA+c,0BAA0BG,mB,uCAA1Bld,GAAA+c,0BAA0BG,mBAAkBnc,GAAEhC,MAAA,gB,kBAE9D,IAA0D,G,aAD5D2H,EAAAA,EAAAA,IASY+K,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IARS1R,GAAA+c,0BAA0BI,aAAtCC,K,WADT/c,EAAAA,EAAAA,IASYiR,GAAA,CAPTnH,IAAKiT,EAASnX,GACd+C,MAAOoU,EAAS3U,KAChB8I,MAAO6L,EAASnX,I,kBACjB,IAGM,EAHN9G,EAAAA,EAAAA,IAGM,MAHNke,GAGM,EAFJle,EAAAA,EAAAA,IAAgC,aAAAuB,EAAAA,EAAAA,IAAvB0c,EAAS3U,MAAI,IACtBtJ,EAAAA,EAAAA,IAAwF,OAAxFme,IAAwF5c,EAAAA,EAAAA,IAA1CZ,GAAA2D,WAAW2Z,EAAS1Z,cAAW,O,wGAgBzF1E,EAAAA,EAAAA,IAgJYwU,GAAA,C,WA/IDxT,GAAAud,0BAA0B7J,Q,uCAA1B1T,GAAAud,0BAA0B7J,QAAO3S,GAC1C2L,MAAM,YACN7D,MAAM,QACN,uB,CAsIW2P,QAAMhT,EAAAA,EAAAA,IACf,IAGO,EAHPrG,EAAAA,EAAAA,IAGO,OAHPqe,GAGO,EAFLxe,EAAAA,EAAAA,IAA4EW,GAAA,CAAhEE,QAAKuB,EAAA,MAAAA,EAAA,IAAAL,GAAEf,GAAAud,0BAA0B7J,SAAU,I,kBAAO,IAAEtS,EAAA,OAAAA,EAAA,O,QAAF,S,gBAC9DpC,EAAAA,EAAAA,IAA4EW,GAAA,CAAjEC,KAAK,UAAWC,QAAOC,GAAA2d,wB,kBAAwB,IAAMrc,EAAA,OAAAA,EAAA,O,QAAN,a,kDAvI9D,IAkIM,EAlINjC,EAAAA,EAAAA,IAkIM,MAlINue,GAkIM,EAjIJ1e,EAAAA,EAAAA,IAoHSqW,GAAA,CApHAC,OAAQ,IAAE,C,iBACjB,IA6BS,EA7BTtW,EAAAA,EAAAA,IA6BSuW,GAAA,CA7BAC,KAAM,IAAE,C,iBACf,IA2BM,EA3BNrW,EAAAA,EAAAA,IA2BM,MA3BNwe,GA2BM,EA1BJxe,EAAAA,EAAAA,IAGM,MAHNye,GAGM,EAFJ5e,EAAAA,EAAAA,IAAkCkB,GAAA,M,iBAAzB,IAAe,EAAflB,EAAAA,EAAAA,IAAesH,M,uBACxBnH,EAAAA,EAAAA,IAAgB,YAAV,OAAG,OAEXA,EAAAA,EAAAA,IAqBM,MArBN0e,GAqBM,EApBJ1e,EAAAA,EAAAA,IAGM,MAHN2e,GAGM,C,iBAFJ3e,EAAAA,EAAAA,IAAuC,QAAjCL,MAAM,gBAAe,SAAK,KAChCK,EAAAA,EAAAA,IAAiH,OAAjH4e,IAAiHrd,EAAAA,EAAAA,IAAnFV,GAAAud,0BAA0BS,iBAAiB5B,kBAAkBna,QAAQ,IAAK,KAAE,MAE5G9C,EAAAA,EAAAA,IAGM,MAHN8e,GAGM,C,iBAFJ9e,EAAAA,EAAAA,IAAsC,QAAhCL,MAAM,gBAAe,QAAI,KAC/BK,EAAAA,EAAAA,IAAqG,OAArG+e,IAAqGxd,EAAAA,EAAAA,IAAvEV,GAAAud,0BAA0BS,iBAAiBzB,QAAQta,QAAQ,IAAD,MAE1F9C,EAAAA,EAAAA,IAGM,MAHNgf,GAGM,C,iBAFJhf,EAAAA,EAAAA,IAAsC,QAAhCL,MAAM,gBAAe,QAAI,KAC/BK,EAAAA,EAAAA,IAA2G,OAA3Gif,IAA2G1d,EAAAA,EAAAA,IAA7EV,GAAAud,0BAA0BS,iBAAiBxB,aAAava,QAAQ,IAAK,IAAC,MAEtG9C,EAAAA,EAAAA,IAGM,MAHNkf,GAGM,C,iBAFJlf,EAAAA,EAAAA,IAAyC,QAAnCL,MAAM,gBAAe,WAAO,KAClCK,EAAAA,EAAAA,IAAiH,OAAjHmf,IAAiH5d,EAAAA,EAAAA,IAAnFV,GAAAud,0BAA0BS,iBAAiBtB,SAASza,QAAQ,IAAM,QAAS,IAAC,MAE5G9C,EAAAA,EAAAA,IAGM,MAHNof,GAGM,C,iBAFJpf,EAAAA,EAAAA,IAAwC,QAAlCL,MAAM,gBAAe,UAAM,KACjCK,EAAAA,EAAAA,IAAoH,OAApHqf,IAAoH9d,EAAAA,EAAAA,IAAtFV,GAAAud,0BAA0BS,iBAAiBrB,YAAY1a,QAAQ,IAAM,QAAS,IAAC,W,OAKrHjD,EAAAA,EAAAA,IAoFSuW,GAAA,CApFAC,KAAM,IAAE,C,iBACf,IAkFM,EAlFNrW,EAAAA,EAAAA,IAkFM,MAlFNsf,GAkFM,EAjFJtf,EAAAA,EAAAA,IAGM,MAHNuf,GAGM,EAFJ1f,EAAAA,EAAAA,IAA+BkB,GAAA,M,iBAAtB,IAAY,EAAZlB,EAAAA,EAAAA,IAAY4G,M,uBACrBzG,EAAAA,EAAAA,IAAiB,YAAX,QAAI,OAEZA,EAAAA,EAAAA,IA4EM,MA5ENwf,GA4EM,EA3EJxf,EAAAA,EAAAA,IAcM,MAdNyf,GAcM,C,iBAbJzf,EAAAA,EAAAA,IAAuC,QAAjCL,MAAM,gBAAe,SAAK,KAChCK,EAAAA,EAAAA,IAAgH,OAAhH0f,IAAgHne,EAAAA,EAAAA,IAAlFV,GAAAud,0BAA0BuB,gBAAgB1C,kBAAkBna,QAAQ,IAAK,KAAE,IACzG9C,EAAAA,EAAAA,IAUO,QAVAL,OAAKgP,EAAAA,EAAAA,IAAEhO,GAAAif,mBAAsC/e,GAAAud,0BAA0BuB,gBAAgB1C,kBAAqCpc,GAAAud,0BAA0BS,iBAAiB5B,kB,4BAKzKtc,GAAAkf,kBAAuChf,GAAAud,0BAA0BuB,gBAAgB1C,kBAAuCpc,GAAAud,0BAA0BS,iBAAiB5B,kB,wBAO1Kjd,EAAAA,EAAAA,IAcM,MAdN8f,GAcM,C,iBAbJ9f,EAAAA,EAAAA,IAAsC,QAAhCL,MAAM,gBAAe,QAAI,KAC/BK,EAAAA,EAAAA,IAAoG,OAApG+f,IAAoGxe,EAAAA,EAAAA,IAAtEV,GAAAud,0BAA0BuB,gBAAgBvC,QAAQta,QAAQ,IAAD,IACvF9C,EAAAA,EAAAA,IAUO,QAVAL,OAAKgP,EAAAA,EAAAA,IAAEhO,GAAAif,mBAAsC/e,GAAAud,0BAA0BuB,gBAAgBvC,QAA2Bvc,GAAAud,0BAA0BS,iBAAiBzB,Q,kBAK/Jzc,GAAAkf,kBAAuChf,GAAAud,0BAA0BuB,gBAAgBvC,QAA6Bvc,GAAAud,0BAA0BS,iBAAiBzB,Q,cAOhKpd,EAAAA,EAAAA,IAcM,MAdNggB,GAcM,C,iBAbJhgB,EAAAA,EAAAA,IAAsC,QAAhCL,MAAM,gBAAe,QAAI,KAC/BK,EAAAA,EAAAA,IAA0G,OAA1GigB,IAA0G1e,EAAAA,EAAAA,IAA5EV,GAAAud,0BAA0BuB,gBAAgBtC,aAAava,QAAQ,IAAK,IAAC,IACnG9C,EAAAA,EAAAA,IAUO,QAVAL,OAAKgP,EAAAA,EAAAA,IAAEhO,GAAAif,mBAAsC/e,GAAAud,0BAA0BuB,gBAAgBtC,aAAgCxc,GAAAud,0BAA0BS,iBAAiBxB,a,2BAKpK1c,GAAAkf,kBAAuChf,GAAAud,0BAA0BuB,gBAAgBtC,aAAkCxc,GAAAud,0BAA0BS,iBAAiBxB,a,uBAOrKrd,EAAAA,EAAAA,IAcM,MAdNkgB,GAcM,C,iBAbJlgB,EAAAA,EAAAA,IAAyC,QAAnCL,MAAM,gBAAe,WAAO,KAClCK,EAAAA,EAAAA,IAAgH,OAAhHmgB,IAAgH5e,EAAAA,EAAAA,IAAlFV,GAAAud,0BAA0BuB,gBAAgBpC,SAASza,QAAQ,IAAM,QAAS,IAAC,IACzG9C,EAAAA,EAAAA,IAUO,QAVAL,OAAKgP,EAAAA,EAAAA,IAAEhO,GAAAif,mBAAsC/e,GAAAud,0BAA0BuB,gBAAgBpC,SAAO,EAAyB1c,GAAAud,0BAA0BS,iBAAiBtB,SAAO,E,kBAK3K5c,GAAAkf,kBAAuChf,GAAAud,0BAA0BuB,gBAAgBpC,SAAO,EAA2B1c,GAAAud,0BAA0BS,iBAAiBtB,SAAO,E,cAO5Kvd,EAAAA,EAAAA,IAcM,MAdNogB,GAcM,C,iBAbJpgB,EAAAA,EAAAA,IAAwC,QAAlCL,MAAM,gBAAe,UAAM,KACjCK,EAAAA,EAAAA,IAAmH,OAAnHqgB,IAAmH9e,EAAAA,EAAAA,IAArFV,GAAAud,0BAA0BuB,gBAAgBnC,YAAY1a,QAAQ,IAAM,QAAS,IAAC,IAC5G9C,EAAAA,EAAAA,IAUO,QAVAL,OAAKgP,EAAAA,EAAAA,IAAEhO,GAAAif,mBAAsC/e,GAAAud,0BAA0BuB,gBAAgBnC,YAAU,EAAyB3c,GAAAud,0BAA0BS,iBAAiBrB,YAAU,E,qBAKjL7c,GAAAkf,kBAAuChf,GAAAud,0BAA0BuB,gBAAgBnC,YAAU,EAA2B3c,GAAAud,0BAA0BS,iBAAiBrB,YAAU,E,qCAa1Lxd,EAAAA,EAAAA,IASM,MATNsgB,GASM,EARJtgB,EAAAA,EAAAA,IAGM,MAHNugB,GAGM,EAFJ1gB,EAAAA,EAAAA,IAAiCkB,GAAA,M,iBAAxB,IAAc,EAAdlB,EAAAA,EAAAA,IAAc8d,M,uBACvB3d,EAAAA,EAAAA,IAAiB,YAAX,QAAI,OAEZA,EAAAA,EAAAA,IAGM,MAHNwgB,GAGM,CAFK3f,GAAAud,0BAA0BqC,a,WAAnClZ,EAAAA,EAAAA,IAA6F,IAAAmZ,IAAAnf,EAAAA,EAAAA,IAA3CV,GAAAud,0BAA0BqC,YAAU,M,WACtFlZ,EAAAA,EAAAA,IAAkD,IAAAoZ,IAAApf,EAAAA,EAAAA,IAArCZ,GAAAigB,gCAA4B,Y,sICvwC5CjhB,MAAM,mB,IACJA,MAAM,gB,IACLA,MAAM,e,IAeAA,MAAM,gB,iGAjBpB4H,EAAAA,EAAAA,IAsBM,MAtBNtH,GAsBM,EArBJD,EAAAA,EAAAA,IAmBM,MAnBNE,GAmBM,EAlBJF,EAAAA,EAAAA,IAA6C,KAA7CI,IAA6CmB,EAAAA,EAAAA,IAAlBsf,EAAApW,YAAU,IACrC5K,EAAAA,EAAAA,IAgBYoS,EAAA,C,WAfDpR,EAAAigB,e,qCAAAjgB,EAAAigB,eAAclf,GACtB+P,SAAQhR,EAAAogB,aACTphB,MAAM,kBACNqC,KAAK,S,kBAEL,IAEY,EAFZnC,EAAAA,EAAAA,IAEYsS,EAAA,CAFDC,MAAM,OAAK,C,iBACpB,IAAsCnQ,EAAA,KAAAA,EAAA,KAAtCjC,EAAAA,EAAAA,IAAsC,QAAhCL,MAAM,gBAAe,QAAI,M,4BAEjC4H,EAAAA,EAAAA,IAMY+K,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IALcsO,EAAA/V,WAAU,CAA1B/B,EAAMrD,M,WADhBxE,EAAAA,EAAAA,IAMYiR,EAAA,CAJTnH,IAAKtF,EACL0M,MAAOrJ,EAAKO,M,kBAEb,IAAiD,EAAjDtJ,EAAAA,EAAAA,IAAiD,OAAjDK,IAAiDkB,EAAAA,EAAAA,IAAnBwH,EAAKO,MAAI,K,oEAI7CtJ,EAAAA,EAAAA,IAA2E,OAAtE+B,IAAI,QAASnC,OAAKohB,EAAAA,EAAAA,IAAA,CAAAtX,MAAWmX,EAAAnW,WAAU3K,OAAU8gB,EAAAlW,e,0BAO1D,IACErB,KAAM,oBACN2X,MAAO,CACLxW,WAAY,CACVhK,KAAMygB,OACNnX,QAAS,UAEXa,UAAW,CACTnK,KAAMygB,OACNnX,QAAS,UAEXW,WAAY,CACVjK,KAAMygB,OACNnX,QAAS,SAEXY,YAAa,CACXlK,KAAMygB,OACNnX,QAAS,SAEXc,MAAO,CACLpK,KAAM0gB,MACNlI,UAAU,GAEZnO,WAAY,CACVrK,KAAM0gB,MACNlI,UAAU,GAGZ/N,UAAW,CACTzK,KAAMygB,OACNnX,QAAS,iBAGbhB,IAAAA,GACE,MAAO,CACL+X,eAAgB,MAEpB,EAEAM,OAAAA,GACEC,KAAKC,UAAU,KACbD,KAAKE,aAET,EAEAC,MAAO,CAEL3W,MAAO,CACL4W,OAAAA,GACEJ,KAAKC,UAAU,KACbD,KAAKK,eAET,EACAC,MAAM,GAER7W,WAAY,CACV2W,OAAAA,GACEJ,KAAKC,UAAU,KACbD,KAAKK,eAET,EACAC,MAAM,IAIVC,QAAS,CACPL,SAAAA,GACE,IAAKF,KAAKQ,MAAMC,MAAO,OAEvB,MAAMA,EAAQC,GAAAA,GAAaV,KAAKQ,MAAMC,OACtCT,KAAKW,cAAgBF,EAGrBG,OAAOC,iBAAiB,SAAU,KAChCJ,EAAMK,WAGRd,KAAKK,aACP,EAEAX,YAAAA,GACEM,KAAKK,aACP,EAEAA,WAAAA,GACE,IAAKL,KAAKW,cAAe,OAEzB,MAAMF,EAAQT,KAAKW,cAGnB,IAAKX,KAAKxW,QAAUwW,KAAKvW,YAAyC,IAA3BuW,KAAKvW,WAAW4H,OAErD,YADA0P,QAAQxP,IAAI,eAKd,MAAMyP,EAAchB,KAAKiB,sBAEnBC,EAASlB,KAAKvW,WAAW0X,IAAI,CAACzZ,EAAMrD,KAAU,CAClD4D,KAAMP,EAAKO,KACX7I,KAAM,OACNsI,KAA8B,QAAxBsY,KAAKP,gBAA4BO,KAAKP,iBAAmB/X,EAAKO,KAAOP,EAAK0Z,OAAS,GACzFC,WAAYL,EAAYK,WACxBC,WAAYN,EAAYM,WACxBC,OAAQP,EAAYO,OACpBC,UAAW,CACTnZ,MAAO2Y,EAAYS,UACnBriB,KAAM4gB,KAAK0B,YAAYrd,EAAO2c,GAC9BW,YAAa,qBACbC,WAAYZ,EAAYY,WACxBC,cAAe,EACfC,IAAK,SAEPC,UAAW,CACTtO,MAAOuM,KAAKgC,eAAe3d,EAAO2c,GAClCiB,YAAa,EACbC,YAAalC,KAAKgC,eAAe3d,EAAO2c,GACxCW,YAAa,qBACbC,WAAY,GAEdO,UAAWnC,KAAKoC,aAAa/d,EAAO2c,GACpCqB,SAAU,CACRC,MAAO,SACPd,UAAW,CACTnZ,MAAO2Y,EAAYS,UAAY,EAC/BG,WAAYZ,EAAYY,WAAa,GAEvCG,UAAW,CACTE,YAAa,EACbL,WAAY,OAKZW,EAAS,CACb9O,MAAOuN,EAAYwB,OACnBC,QAAS,CACP7d,QAAS,OACT8d,UAAYC,IACV,IAAIC,EAAS,uDAAuDD,EAAO,GAAGE,kBAY9E,OAXAF,EAAOG,QAAQC,SACMC,IAAfD,EAAMrb,OACRkb,GAAU,sNAGgBG,EAAMtP,2GAEpBsP,EAAME,eAAeF,EAAMrb,QAAQsY,KAAKzW,gDAIjDqZ,GAETM,gBAAiB,4BACjBhB,YAAa,OACbD,YAAa,EACbkB,UAAW,CACT1P,MAAO,OACP2P,SAAU,IAEZC,aAAc,kEAEhBC,KAAM,CACJC,KAAM,KACNC,MAAO,KACPC,OAAQ,MACR3L,IAAK,KACL4L,cAAc,GAEhBC,MAAO,CACLvkB,KAAM,WACNsI,KAAMsY,KAAKxW,MACXoa,UAAW,CACTC,OAAQ,GACRpQ,MAAO,OACP2P,SAAU,GACVU,SAAU,QAEZC,SAAU,CACRvC,UAAW,CACT/N,MAAO,UACPpL,MAAO,IAGX2b,SAAU,CACRC,MAAM,IAGVC,MAAO,CACLjc,KAAM+X,KAAKzW,UACX4a,aAAc,SACdC,QAAS,GACTC,cAAe,CACb5Q,MAAO,OACP2P,SAAU,IAEZhkB,KAAM,QACNwkB,UAAW,CACTnQ,MAAO,OACP2P,SAAU,GACVV,UAAY3R,GACNA,GAAS,KACHA,EAAQ,KAAMtP,QAAQ,GAAK,IAE9BsP,GAGXgT,SAAU,CACRE,MAAM,GAERK,UAAW,CACT9C,UAAW,CACT/N,MAAO,UACPrU,KAAM,YAIZ8hB,OAAQA,EACRqD,OAAQ,CACNN,KAA8B,QAAxBjE,KAAKP,gBAA4ByB,EAAO7P,OAAS,EACvDoS,OAAQ,EACR/b,KAAMwZ,EAAOC,IAAIqD,GAAKA,EAAEvc,MACxBkb,UAAW,CACTC,SAAU,GACV3P,MAAO,QAETvS,KAAM8f,EAAYyD,WAClBC,UAAW,GACXC,WAAY,GACZC,QAAS,IAEXC,WAAW,EACXC,kBAAmB,IACnBC,gBAAiB,YAGnBtE,EAAMuE,UAAUzC,GAAQ,EAC1B,EAGAtB,mBAAAA,GACE,MAAMgE,EAAU,CACdC,aAAc,CACZ1C,OAAQ,CACN,UAAW,UAAW,UAAW,UAAW,UAC5C,UAAW,UAAW,UAAW,UAAW,WAE9CjB,QAAQ,EACRE,UAAW,EACXJ,YAAY,EACZC,WAAY,EACZM,WAAY,EACZuD,aAAa,EACbV,WAAY,YACZW,gBAAiB,QACjBC,YAAa,KAEfC,IAAK,CACH9C,OAAQ,CACN,UAAW,UAAW,UAAW,UAAW,UAC5C,UAAW,UAAW,UAAW,UAAW,WAE9CjB,QAAQ,EACRE,UAAW,EACXJ,YAAY,EACZC,WAAY,EACZM,WAAY,EACZuD,aAAa,EACbV,WAAY,SACZW,gBAAiB,QACjBC,YAAa,IAEfE,IAAK,CACH/C,OAAQ,CACN,UAAW,UAAW,UAAW,UAAW,UAC5C,UAAW,UAAW,UAAW,UAAW,WAE9CjB,QAAQ,EACRE,UAAW,EACXJ,YAAY,EACZC,WAAY,EACZM,WAAY,EACZuD,aAAa,EACbV,WAAY,MACZW,gBAAiB,QACjBC,YAAa,KAEflP,MAAO,CACLqM,OAAQ,CACN,UAAW,UAAW,UAAW,UAAW,UAC5C,UAAW,UAAW,UAAW,UAAW,WAE9CjB,QAAQ,EACRE,UAAW,EACXJ,YAAY,EACZC,WAAY,EACZM,WAAY,EACZuD,aAAa,EACbV,WAAY,OACZW,gBAAiB,QACjBC,YAAa,KAEfG,IAAK,CACHhD,OAAQ,CACN,UAAW,UAAW,UAAW,UAAW,UAC5C,UAAW,UAAW,UAAW,UAAW,WAE9CjB,QAAQ,EACRE,UAAW,EACXJ,YAAY,EACZC,WAAY,EACZM,WAAY,EACZuD,aAAa,EACbV,WAAY,UACZW,gBAAiB,SACjBC,YAAa,IAEfI,IAAK,CACHjD,OAAQ,CACN,UAAW,UAAW,UAAW,UAAW,UAC5C,UAAW,UAAW,UAAW,UAAW,WAE9CjB,QAAQ,EACRE,UAAW,EACXJ,YAAY,EACZC,WAAY,EACZM,WAAY,EACZuD,aAAa,EACbV,WAAY,WACZW,gBAAiB,SACjBC,YAAa,MAKjB,OAAOJ,EAAQjF,KAAKnW,YAAcob,EAAQC,YAC5C,EAGAxD,WAAAA,CAAYrd,EAAO2c,GACjB,MAAM0E,EAAY,CAAC,QAAS,SAAU,UAGtC,MAAuB,QAAnB1F,KAAKnW,WAA0C,QAAnBmW,KAAKnW,UAC5BmX,EAAYoE,gBAIdM,EAAUrhB,EAAQqhB,EAAUrU,OACrC,EAGA2Q,cAAAA,CAAe3d,EAAO2c,GACpB,OAAOA,EAAYwB,OAAOne,EAAQ2c,EAAYwB,OAAOnR,OACvD,EAGA+Q,YAAAA,CAAa/d,EAAO2c,GAClB,MAAMvN,EAAQuM,KAAKgC,eAAe3d,EAAO2c,GAGzC,MAA4B,QAAxBhB,KAAKP,gBAA6BuB,EAAYmE,YAK3C,CACL1R,MAAO,CACLrU,KAAM,SACNumB,EAAG,EACHC,EAAG,EACHC,GAAI,EACJC,GAAI,EACJC,WAAY,CAAC,CACXC,OAAQ,EACRvS,MAAOA,EAAQwS,KAAKC,MAAgC,IAA1BlF,EAAYqE,aAAmBc,SAAS,IAAIC,SAAS,EAAG,MACjF,CACDJ,OAAQ,EACRvS,MAAOA,EAAQ,QAGnB4S,OAAQ,QAnBD,IAqBX,EAGAC,aAAAA,GACMtG,KAAKW,eACPX,KAAKW,cAAc4F,UAErB3F,OAAO4F,oBAAoB,SAAUxG,KAAKyG,cAC5C,I,YC7ZJ,MAAMC,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAAS,IAAQ,CAAC,YAAY,qBAEzF,U,YF6yCA,IACEze,KAAM,2BACN0e,WAAY,CACVC,KAAI,MACJC,kBAAiB,GACjBC,KAAI,QACJC,KAAI,QACJC,aAAY,gBACZC,WAAU,cACVC,YAAW,eACXC,OAAM,UACNC,UAAS,aACTC,UAAS,aACTC,MAAK,SACLC,SAAQ,YACRC,SAAQ,YACRC,QAAO,WACPC,aAAY,gBACZC,MAAK,SACLC,KAAI,QACJC,QAAO,WACPC,KAAI,QACJC,aAAY,gBACZC,UAAS,aACTC,WAAU,cACVC,aAAY,gBACZC,SAAQ,YACRC,QAAO,WAEPC,WAAU,cACVC,YAAWA,GAAAA,aAEb5gB,IAAAA,GACE,MAAO,CACL6gB,SAAU,KACVC,aAAc,GACdjnB,WAAY,CAAC,EACb9B,SAAU,GACVU,KAAM,GACNP,SAAU,GACVQ,UAAU,EACVC,aAAc,GACdooB,aAAc,GACdzkB,YAAa,IACb0kB,WAAY,GACZC,WAAY,GACZnf,MAAO,GACPC,WAAY,GACZmf,aAAc,CAAC,EACfpb,eAAgB,CAAC,EACjBqb,QAAS,GAETxN,mBAAoB,CAClBnI,SAAS,EACTsI,SAAS,EACTvX,KAAM,SACNwX,KAAM,CACJxT,KAAM,GACNkE,YAAa,GACb2c,QAAS,KACTlN,kBAAmB,EACnBG,QAAS,EACTC,aAAc,EACdE,QAAS,EACTC,WAAY,EACZC,WAAW,EACX2M,WAAY,OAIhBrN,kBAAmB,CACjBzT,KAAM,CACJ,CAAE2P,UAAU,EAAMlF,QAAS,WAAY9N,QAAS,QAChD,CAAEkX,IAAK,EAAGG,IAAK,IAAKvJ,QAAS,cAAe9N,QAAS,SAEvDkkB,QAAS,CACP,CAAElR,UAAU,EAAMlF,QAAS,UAAW9N,QAAS,YAInD2X,0BAA2B,CACzBrJ,SAAS,EACTsI,SAAS,EACTmB,aAAc,GACdD,mBAAoB,MAGtBK,0BAA2B,CACzB7J,SAAS,EACTsK,iBAAkB,CAChB5B,kBAAmB,EACnBG,QAAS,EACTC,aAAc,EACdE,QAAS,EACTC,WAAY,GAEdmC,gBAAiB,CACf1C,kBAAmB,EACnBG,QAAS,EACTC,aAAc,EACdE,QAAS,EACTC,WAAY,GAEdiD,WAAY,IAGdjO,cAAe,CACb6X,IAAK,KACLC,OAAQ,KACRC,QAAS,KACTC,MAAO,KACPC,YAAa,KACbC,MAAO,MAETrY,YAAa,MAEbsY,aAAc,KACdC,oBAAqB,EACrBC,uBAAwB,EACxBC,oBAAqB,KACrBC,mBAAoB,eACpBrZ,aAAa,EACbsZ,gBAAiB,KAEjBC,kBAAmB,CAAC,EACpBnZ,UAAW,GACXI,SAAU,MACVgZ,cAAc,EACdjgB,eAAgB,EAEhBkgB,sBAAsB,EACtBC,yBAAyB,EACzBC,eAAe,EAEfC,0BAA2B,KAC3BC,6BAA8B,KAE9BC,iBAAkB,GAClBC,QAAS,GACTC,QAAS,GACTC,UAAW,GACXC,QAAS,GACTC,QAAS,GACTC,QAAS,GACTC,QAAS,GACTC,cAAe,GAEfC,mBAAoB,CAClB1F,aAAc,CAAC,EACfI,IAAK,CAAC,EACNC,IAAK,CAAC,EACNpP,MAAO,CAAC,EACR0U,IAAK,CAAC,EACNrF,IAAK,CAAC,EACNsF,IAAK,CAAC,EACNrF,IAAK,CAAC,EACN5hB,UAAW,CAAC,GAEdknB,oBAAqB,GACrBC,uBAAwB,KAExB5jB,cAAc,EACd6jB,aAAc,GAEd1f,OAAQ,GACR2f,iBAAiB,EACjBnf,aAAa,EACbK,mBAAoB,GAGpB6G,mBAAoB,CAClBC,SAAS,EACTM,kBAAmB,EACnBK,YAAa,GACbM,gBAAiB,IAEnBI,iBAAkB,CAChBrB,SAAS,EACTsB,WAAY,MAEdmD,uBAAwB,CACtBzE,SAAS,EACT9T,KAAM,GACN4Z,SAAU,SACV/Q,KAAM,GACNwR,QAAS,GACTE,WAAY,IAGdhB,kBAAmB,CACjB1Q,KAAM,CACJ,CAAE2P,UAAU,EAAMlF,QAAS,UAAW9N,QAAS,QAC/C,CAAEkX,IAAK,EAAGG,IAAK,GAAIvJ,QAAS,iBAAkB9N,QAAS,SAEzDoU,SAAU,CACR,CAAEpB,UAAU,EAAMlF,QAAS,UAAW9N,QAAS,WAEjD6U,QAAS,CACP,CAAE7B,UAAU,EAAMlF,QAAS,eAAgB9N,QAAS,QACpD,CAAEkX,IAAK,EAAGpJ,QAAS,kBAAmB9N,QAAS,SAEjD+U,WAAY,CACV,CAAE/B,UAAU,EAAMlF,QAAS,SAAU9N,QAAS,YAGlD2K,WAAY,GACZM,iBAAkB,GAEtB,EACAsb,SAAU,CAER7f,aAAAA,GACE,MAAwC,MAAjC0U,KAAKze,WAAW8Y,cAA0C,QAAlB2F,KAAKpgB,QACtD,EAEA6K,MAAAA,GACE,OAAO,CACT,EAEA2G,YAAAA,GACE,IAAIga,EAAOpL,KAAK6I,QAqBhB,OAlBI7I,KAAKnP,UAA8B,QAAlBmP,KAAKnP,WACxBua,EAAOA,EAAKC,OAAO9Z,GAAOA,EAAIE,QAAUuO,KAAKnP,WAI3CmP,KAAKhP,aAAoC,QAArBgP,KAAKhP,cAC3Boa,EAAOA,EAAKC,OAAO9Z,GAAOA,EAAIO,WAAakO,KAAKhP,cAI9CgP,KAAKvP,YACP2a,EAAOA,EAAKC,OAAO9Z,GACjBA,EAAImB,QAAQ4Y,cAAc1R,SAASoG,KAAKvP,UAAU6a,gBACjD/Z,EAAIoB,SAAWpB,EAAIoB,QAAQ2Y,cAAc1R,SAASoG,KAAKvP,UAAU6a,iBAK/DF,EAAKG,KAAK,CAACC,EAAGC,IAAM,IAAIC,KAAKF,EAAE7Z,WAAa,IAAI+Z,KAAKD,EAAE9Z,WAChE,EAGAjI,sBAAAA,GACE,MAAMD,EAAa,GAcnB,OAbIuW,KAAK5Y,cAAgB4Y,KAAKiL,aAAa5Z,OAAS,IAClD2O,KAAKwI,aAAexI,KAAKiL,aAAa9J,IAAIpZ,GAAOA,EAAIC,OAAS,IAAMD,EAAIE,OACzE0jB,OAAOC,KAAK5L,KAAK4K,mBAAmB1F,cAAcpC,QAAQ+I,IACrD7L,KAAK5Y,cAAgB4Y,KAAKwI,eAAiBxI,KAAKwI,aAAa5O,SAASiS,IAGtE7L,KAAK4K,mBAAmB1F,aAAa2G,GAAcxa,OAAS,GAC9D5H,EAAWqiB,KAAK,CACd7jB,KAAM4jB,EACNzK,OAAQpB,KAAK4K,mBAAmB1F,aAAa2G,OAI5CpiB,EAAW4H,OAAS,EAAI5H,EAAa,CAAC,CAAExB,KAAM,SAAUmZ,OAAQ,IACzE,EAGArX,aAAAA,GACE,MAAMN,EAAa,GAsBnB,OArBIuW,KAAK5Y,cAAgB4Y,KAAKiL,aAAa5Z,OAAS,IAClD2O,KAAKwI,aAAexI,KAAKiL,aAAa9J,IAAIpZ,GAAOA,EAAIC,OAAS,IAAMD,EAAIE,OAG1E0jB,OAAOC,KAAK5L,KAAK4K,mBAAmBtF,KAAKxC,QAAQ+I,IAC3C7L,KAAK5Y,cAAgB4Y,KAAKwI,eAAiBxI,KAAKwI,aAAa5O,SAASiS,IAGtE7L,KAAK4K,mBAAmBtF,IAAIuG,GAAcxa,OAAS,GACrD5H,EAAWqiB,KAAK,CACd7jB,KAAM4jB,EACNzK,OAAQpB,KAAK4K,mBAAmBtF,IAAIuG,OAIhB,IAAtBpiB,EAAW4H,QAAgB2O,KAAKoK,SAAWpK,KAAKoK,QAAQ/Y,OAAS,GACnE5H,EAAWqiB,KAAK,CACd7jB,KAAM,QACNmZ,OAAQpB,KAAKoK,UAGV3gB,EAAW4H,OAAS,EAAI5H,EAAa,CAAC,CAAExB,KAAM,MAAOmZ,OAAQ,IACtE,EAGAnX,aAAAA,GACE,MAAMR,EAAa,GAqBnB,OApBIuW,KAAK5Y,cAAgB4Y,KAAKiL,aAAa5Z,OAAS,IAClD2O,KAAKwI,aAAexI,KAAKiL,aAAa9J,IAAIpZ,GAAOA,EAAIC,OAAS,IAAMD,EAAIE,OAE1E0jB,OAAOC,KAAK5L,KAAK4K,mBAAmBrF,KAAKzC,QAAQ+I,IAC3C7L,KAAK5Y,cAAgB4Y,KAAKwI,eAAiBxI,KAAKwI,aAAa5O,SAASiS,IAGtE7L,KAAK4K,mBAAmBrF,IAAIsG,GAAcxa,OAAS,GACrD5H,EAAWqiB,KAAK,CACd7jB,KAAM4jB,EACNzK,OAAQpB,KAAK4K,mBAAmBrF,IAAIsG,OAIhB,IAAtBpiB,EAAW4H,QAAgB2O,KAAKqK,SAAWrK,KAAKqK,QAAQhZ,OAAS,GACnE5H,EAAWqiB,KAAK,CACd7jB,KAAM,QACNmZ,OAAQpB,KAAKqK,UAGV5gB,EAAW4H,OAAS,EAAI5H,EAAa,CAAC,CAAExB,KAAM,MAAOmZ,OAAQ,IACtE,EAGAjX,eAAAA,GACE,MAAMV,EAAa,GAQnB,OANIuW,KAAKsK,UAAUjZ,OAAS,GAC1B5H,EAAWqiB,KAAK,CACd7jB,KAAM,QACNmZ,OAAQpB,KAAKsK,YAGV7gB,EAAW4H,OAAS,EAAI5H,EAAa,CAAC,CAAExB,KAAM,QAASmZ,OAAQ,IACxE,EAGA2K,aAAAA,GACE,MAAMtiB,EAAa,GAenB,OAdIuW,KAAK5Y,cAAgB4Y,KAAKiL,aAAa5Z,OAAS,IAClD2O,KAAKwI,aAAexI,KAAKiL,aAAa9J,IAAIpZ,GAAOA,EAAIC,OAAS,IAAMD,EAAIE,OAE1E0jB,OAAOC,KAAK5L,KAAK4K,mBAAmBC,KAAK/H,QAAQ+I,IAC3C7L,KAAK5Y,cAAgB4Y,KAAKwI,eAAiBxI,KAAKwI,aAAa5O,SAASiS,IAGtE7L,KAAK4K,mBAAmBC,IAAIgB,GAAcxa,OAAS,GACrD5H,EAAWqiB,KAAK,CACd7jB,KAAM4jB,EACNzK,OAAQpB,KAAK4K,mBAAmBC,IAAIgB,OAInCpiB,EAAW4H,OAAS,EAAI5H,EAAa,CAAC,CAAExB,KAAM,WAAYmZ,OAAQ,IAC3E,EAGA/W,aAAAA,GACE,MAAMZ,EAAa,GAcnB,OAbIuW,KAAK5Y,cAAgB4Y,KAAKiL,aAAa5Z,OAAS,IAClD2O,KAAKwI,aAAexI,KAAKiL,aAAa9J,IAAIpZ,GAAOA,EAAIC,OAAS,IAAMD,EAAIE,OAE1E0jB,OAAOC,KAAK5L,KAAK4K,mBAAmBpF,KAAK1C,QAAQ+I,IAAqB7L,KAAK5Y,cAAgB4Y,KAAKwI,eAAiBxI,KAAKwI,aAAa5O,SAASiS,IAGtI7L,KAAK4K,mBAAmBpF,IAAIqG,GAAcxa,OAAS,GACrD5H,EAAWqiB,KAAK,CACd7jB,KAAM4jB,EACNzK,OAAQpB,KAAK4K,mBAAmBpF,IAAIqG,OAInCpiB,EAAW4H,OAAS,EAAI5H,EAAa,CAAC,CAAExB,KAAM,WAAYmZ,OAAQ,IAC3E,EAGA4K,aAAAA,GACE,MAAMviB,EAAa,GAenB,OAdIuW,KAAK5Y,cAAgB4Y,KAAKiL,aAAa5Z,OAAS,IAClD2O,KAAKwI,aAAexI,KAAKiL,aAAa9J,IAAIpZ,GAAOA,EAAIC,OAAS,IAAMD,EAAIE,OAE1E0jB,OAAOC,KAAK5L,KAAK4K,mBAAmBE,KAAKhI,QAAQ+I,IAC3C7L,KAAK5Y,cAAgB4Y,KAAKwI,eAAiBxI,KAAKwI,aAAa5O,SAASiS,IAGtE7L,KAAK4K,mBAAmBE,IAAIe,GAAcxa,OAAS,GACrD5H,EAAWqiB,KAAK,CACd7jB,KAAM4jB,EACNzK,OAAQpB,KAAK4K,mBAAmBE,IAAIe,OAInCpiB,EAAW4H,OAAS,EAAI5H,EAAa,CAAC,CAAExB,KAAM,WAAYmZ,OAAQ,IAC3E,EAGA7W,aAAAA,GACE,MAAMd,EAAa,GAenB,OAdIuW,KAAK5Y,cAAgB4Y,KAAKiL,aAAa5Z,OAAS,IAClD2O,KAAKwI,aAAexI,KAAKiL,aAAa9J,IAAIpZ,GAAOA,EAAIC,OAAS,IAAMD,EAAIE,OAE1E0jB,OAAOC,KAAK5L,KAAK4K,mBAAmBnF,KAAK3C,QAAQ+I,IAC3C7L,KAAK5Y,cAAgB4Y,KAAKwI,eAAiBxI,KAAKwI,aAAa5O,SAASiS,IAGtE7L,KAAK4K,mBAAmBnF,IAAIoG,GAAcxa,OAAS,GACrD5H,EAAWqiB,KAAK,CACd7jB,KAAM4jB,EACNzK,OAAQpB,KAAK4K,mBAAmBnF,IAAIoG,OAInCpiB,EAAW4H,OAAS,EAAI5H,EAAa,CAAC,CAAExB,KAAM,WAAYmZ,OAAQ,IAC3E,EAGAzZ,eAAAA,GACE,MAAMskB,EAAajM,KAAKkM,qBAExB,IAAKlM,KAAK5Y,cAA6C,IAA7B4Y,KAAKiL,aAAa5Z,OAE1C,OAAO4a,EAIT,MAAME,EAAgBnM,KAAKiL,aAAa9J,IAAIpZ,GAAOA,EAAIE,MAGjDmkB,EAAeH,EAAWZ,OAAOgB,GACnB,QAAlBA,EAAOrkB,QAAoBmkB,EAAcvS,SAASyS,EAAOpkB,OAIrDqkB,EAAYF,EAAaf,OAAOgB,GAA4B,QAAlBA,EAAOrkB,QACjDukB,EAAaH,EAAaf,OAAOgB,GAA4B,QAAlBA,EAAOrkB,QAExD,MAAO,IAAIskB,KAAcC,EAC3B,GAGFhM,QAAS,CAEP1F,cAAAA,GACE,IAAKmF,KAAKze,aAAeye,KAAKze,WAAW2F,eAAiB8Y,KAAKze,WAAW2F,eAAiB,EACzF,OAAO,EAET,MAAMC,EAAkB6Y,KAAKze,WAAW4F,iBAAmB,EAC3D,OAAO8e,KAAKuG,MAAOrlB,EAAkB6Y,KAAKze,WAAW2F,cAAiB,IACxE,EAGAulB,aAAAA,GAKE,GAHAzM,KAAK0M,mBAGA1M,KAAKuI,UAA6C,MAAjCvI,KAAKze,WAAW8Y,aAAtC,CAIA2F,KAAK0J,mBAAqB,aAE1B,IACE,MAAMnS,EAAwC,WAA7BqJ,OAAO+L,SAASpV,SAAwB,OAAS,MAC5DqV,EAAUC,CAAAA,SAAAA,aAAAA,SAAAA,KAAYC,qBAAuBlM,OAAO+L,SAASvV,KAC7D2V,EAAQ,GAAGxV,MAAaqV,2BAAiC5M,KAAKuI,YAEpEvI,KAAKsJ,aAAe,IAAI0D,UAAUD,GAGlC,MAAME,EAAoBC,WAAW,KAC/BlN,KAAKsJ,cAAgBtJ,KAAKsJ,aAAa6D,aAAeH,UAAUI,aAClEpN,KAAKsJ,aAAa+D,QAClBrN,KAAKsN,qBAAqB,UAE3B,KAEHtN,KAAKsJ,aAAaiE,OAAS,KACzBC,aAAaP,GACbjN,KAAK0J,mBAAqB,YAC1B1J,KAAKuJ,oBAAsB,EAG3BvJ,KAAKyN,iBAELzN,KAAK0N,SAASC,QAAQ,YAGxB3N,KAAKsJ,aAAasE,UAAavE,IAC7B,IACE,MAAM3hB,EAAOmmB,KAAKC,MAAMzE,EAAM3hB,MAC9BsY,KAAK+N,uBAAuBrmB,EAC9B,CAAE,MAAOyhB,GACPpI,QAAQoI,MAAM,mBAAoBA,EACpC,GAGFnJ,KAAKsJ,aAAa0E,QAAW7E,IAC3BqE,aAAaP,GACblM,QAAQoI,MAAM,iBAAkBA,GAChCnJ,KAAKsN,qBAAqB,SAG5BtN,KAAKsJ,aAAa2E,QAAW5E,IAC3BmE,aAAaP,GACbjN,KAAK0J,mBAAqB,eAC1B1J,KAAKkO,gBAGDlO,KAAKmO,gBAAgB9E,IACvBrJ,KAAKoO,mBAGX,CAAE,MAAOjF,GACPnJ,KAAKsN,qBAAqB,SAC5B,CAzDA,CA0DF,EAGAZ,gBAAAA,GACM1M,KAAKsJ,eACPtJ,KAAKsJ,aAAa+D,QAClBrN,KAAKsJ,aAAe,MAEtBtJ,KAAKkO,gBACLlO,KAAKqO,sBACLrO,KAAK0J,mBAAqB,cAC5B,EAGAyE,eAAAA,CAAgB9E,GAEd,OAAmB,MAAfA,EAAMiF,OAK2B,MAAjCtO,KAAKze,WAAW8Y,eAKhB2F,KAAKuJ,qBAAuBvJ,KAAKwJ,wBACnCxJ,KAAK0N,SAASa,QAAQ,sBACf,KAIJvO,KAAK3P,aAKZ,EAGA+d,gBAAAA,GACEpO,KAAKuJ,sBACL,MAAMiF,EAAQvI,KAAKnK,IAAI,IAAOmK,KAAKwI,IAAI,EAAGzO,KAAKuJ,qBAAsB,KAErExI,QAAQxP,IAAI,MAAMyO,KAAKuJ,0BAA0BiF,UAEjDxO,KAAKyJ,oBAAsByD,WAAW,KACJ,cAA5BlN,KAAK0J,oBACP1J,KAAKyM,iBAEN+B,EACL,EAGAH,mBAAAA,GACMrO,KAAKyJ,sBACP+D,aAAaxN,KAAKyJ,qBAClBzJ,KAAKyJ,oBAAsB,KAE/B,EAGA6D,oBAAAA,CAAqBoB,GACnB1O,KAAK0J,mBAAqB,QAC1B3I,QAAQoI,MAAM,eAAgBuF,GAGG,IAA7B1O,KAAKuJ,qBAEPvJ,KAAK2O,mBAET,EAGAA,iBAAAA,GACM3O,KAAK2J,iBACPiF,cAAc5O,KAAK2J,iBAGjB3J,KAAK3P,aAAgD,MAAjC2P,KAAKze,WAAW8Y,eACtC2F,KAAK2J,gBAAkBkF,YAAY,KACjC7O,KAAK8O,qBACL9O,KAAK+O,wBACL/O,KAAKgP,eACJ,KAGP,EAGAvB,cAAAA,GACEzN,KAAKiP,kBAAoBJ,YAAY,KAC/B7O,KAAKsJ,cAAgBtJ,KAAKsJ,aAAa6D,aAAeH,UAAUkC,MAClElP,KAAKsJ,aAAa6F,KAAKtB,KAAKuB,UAAU,CAAEhwB,KAAM,gBAE/C,IACL,EAGA8uB,aAAAA,GACMlO,KAAKiP,oBACPL,cAAc5O,KAAKiP,mBACnBjP,KAAKiP,kBAAoB,KAE7B,EAGAI,kBAAAA,GACErP,KAAKuJ,oBAAsB,EAC3BvJ,KAAKyM,eACP,EAGAsB,sBAAAA,CAAuBrmB,GAErB,GAAkB,uBAAdA,EAAKtI,KAA+B,CAMtC,GAJA4gB,KAAK4I,aAAelhB,EAAKA,KACzBsY,KAAKsP,kBAGD5nB,EAAKA,MAAQA,EAAKA,KAAK6nB,MAAO,CAChC,MAAMA,EAAQ7nB,EAAKA,KAAK6nB,MAGxBvP,KAAKwP,gCAAgCD,GAIrCvP,KAAKyP,kBAAkB,CACrBlK,IAAKgK,EAAMhhB,YACX2W,aAAcqK,EAAM3T,kBACpBzF,MAAOoZ,EAAM9gB,cACb5K,UAAW0rB,EAAM3gB,YAErB,CAGIlH,EAAKA,MAAQA,EAAKA,KAAKgoB,gBAEzB1P,KAAKC,UAAU,KACbD,KAAK2P,gBAIX,MAAO,GAAkB,eAAdjoB,EAAKtI,KAAuB,CAErC,MAAMypB,EAAU7I,KAAK4P,eAAeloB,EAAKA,MACzCsY,KAAK6I,QAAQiD,KAAKjD,GAEd7I,KAAK6I,QAAQxX,OAAS,MACxB2O,KAAK6I,QAAU7I,KAAK6I,QAAQgH,OAAO,MAGrC7P,KAAK8P,mBAEP,KAAyB,sBAAdpoB,EAAKtI,MAA8C,qBAAdsI,EAAKtI,MAEnD4gB,KAAKxS,eAAiB9F,EAAKA,KAG3BsY,KAAK+P,qBAAqBroB,EAAKA,MAG/BsY,KAAK2P,gBAEkB,kBAAdjoB,EAAKtI,MAGd4gB,KAAKgQ,YAAY,OAAQ,uBAAwB,QAGjD9C,WAAW,KACTlN,KAAKiQ,wBACJ,MAEoB,gBAAdvoB,EAAKtI,MAEd4gB,KAAKpgB,SAAW,OAChBogB,KAAKze,WAAW8Y,aAAe,KAG/B2F,KAAKgQ,YAAY,OAAQ,mBAAoB,SAG7ChQ,KAAKkQ,0BACkB,oBAAdxoB,EAAKtI,MAA4C,oBAAdsI,EAAKtI,MAEjD4gB,KAAKmQ,cAAczoB,EAEvB,EAGAkoB,cAAAA,CAAe/G,GAEb,GAAIA,EAAQ/W,SACV,OAAO+W,EAIT,IAAI/W,EAAW,SAEf,MAAMY,EAAUmW,EAAQnW,SAAW,GAC/BA,EAAQkH,SAAS,UAAYlH,EAAQkH,SAAS,cAAkC,UAAlBiP,EAAQpX,MACxEK,EAAW,QACFY,EAAQkH,SAAS,YAAclH,EAAQkH,SAAS,SAAWlH,EAAQkH,SAAS,SAC7ElH,EAAQkH,SAAS,UAAYlH,EAAQkH,SAAS,SAAWlH,EAAQkH,SAAS,WAClF9H,EAAW,UACFY,EAAQkH,SAAS,QAAUlH,EAAQkH,SAAS,QAAUlH,EAAQkH,SAAS,SACxElH,EAAQkH,SAAS,QAAUlH,EAAQkH,SAAS,MACpD9H,EAAW,eACFY,EAAQkH,SAAS,OAASlH,EAAQkH,SAAS,OAASlH,EAAQkH,SAAS,OACtElH,EAAQkH,SAAS,OAASlH,EAAQkH,SAAS,SACnD9H,EAAW,SAIb,IAAIa,EAAU,GACd,GAAID,EAAQkH,SAAS,MAAQlH,EAAQkH,SAAS,KAC5C,IAEE,MAAMwW,EAAY1d,EAAQ2d,QAAQ,KAC5BC,EAAU5d,EAAQ6d,YAAY,KAAO,EACrCC,EAAU9d,EAAQ+d,UAAUL,EAAWE,GACvCI,EAAW7C,KAAKC,MAAM0C,GAC5B7d,EAAUkb,KAAKuB,UAAUsB,EAAU,KAAM,EAC3C,CAAE,MAAOC,GACP,CAIJ,MAAO,IACF9H,EACH/W,WACAa,UACAT,UAAW8N,KAAK4Q,iBAAiB/H,GAErC,EAGA+H,gBAAAA,CAAiBrf,GACf,MAAMmB,EAAUnB,EAAImB,SAAW,GAG/B,GAAIA,EAAQkH,SAAS,UAAYlH,EAAQkH,SAAS,QAAUlH,EAAQkH,SAAS,SACzElH,EAAQkH,SAAS,QAAUlH,EAAQkH,SAAS,WAAY,CAE1D,MAAMiX,EAAcne,EAAQoe,MAAM,uDAC5BC,EAAcre,EAAQoe,MAAM,oBAAsBpe,EAAQoe,MAAM,WAEtE,GAAID,EAAa,CACf,MAAM7oB,EAAS6oB,EAAY,GACrBve,EAAMue,EAAY,GAClBte,EAASwe,EAAcA,EAAY,GAAK,GAE9C,MAAO,CACL/oB,SACAsK,MACAC,SACAJ,aAAa,EAEjB,CACF,CAEA,OAAO,IACT,EAGA2d,iBAAAA,GAE2B,MAArB9P,KAAKhc,aACPgc,KAAKC,UAAU,KACb,MAAM+Q,EAAeC,SAASC,cAAc,kBACxCF,IACFA,EAAaG,UAAYH,EAAaI,eAI9C,EAGArB,oBAAAA,CAAqBroB,GACnB,IAAKA,EAAM,OAGX,MAAM2pB,EAAM,IAAI3F,KACV4F,EAAgBtR,KAAK6I,QAAQ0I,KAAKhgB,GAAwB,WAAjBA,EAAIO,UAAsC,aAAbP,EAAInS,MAChF,GAAIkyB,EAAe,CACjB,MAAME,EAAW,IAAI9F,KAAK4F,EAAc3f,WACxC,GAAI0f,EAAMG,EAAW,IACnB,MAEJ,CAEAxR,KAAK6I,QAAQiD,KAAK,CAChBna,WAAW,IAAI+Z,MAAO+F,cACtBhgB,MAAO,OACPK,SAAU,SACV1S,KAAM,WACNsT,QAAS,aAAahL,EAAK+F,aAAe,UAAU/F,EAAKkG,gBAAkB,UAAUlG,EAAKoG,cAAgB,KAC1G6E,QAASkb,KAAKuB,UAAU,CACtBsC,IAAKhqB,EAAK+F,aAAe,EACzBkkB,OAAQjqB,EAAKkG,gBAAkB,EAC/BgkB,KAAMlqB,EAAKoG,cAAgB,EAC3BG,aAAc+R,KAAKhS,YAAYtG,EAAKuG,cAAgB,GACpDC,aAAc8R,KAAKhS,YAAYtG,EAAKwG,cAAgB,IACnD,KAAM,KAIP8R,KAAK6I,QAAQxX,OAAS,MACxB2O,KAAK6I,QAAU7I,KAAK6I,QAAQgH,OAAO,MAGrC7P,KAAK8P,mBACP,EAGAL,iBAAAA,CAAkB/nB,GAChB,IAAKA,EAAM,OAGX,MAAM2pB,EAAM,IAAI3F,KACVmG,EAAc7R,KAAK6I,QAAQ0I,KAAKhgB,GAAwB,gBAAjBA,EAAIO,UAA2C,YAAbP,EAAInS,MACnF,GAAIyyB,EAAa,CACf,MAAML,EAAW,IAAI9F,KAAKmG,EAAYlgB,WACtC,GAAI0f,EAAMG,EAAW,IACnB,MAEJ,CAEAxR,KAAK6I,QAAQiD,KAAK,CAChBna,WAAW,IAAI+Z,MAAO+F,cACtBhgB,MAAO,OACPK,SAAU,cACV1S,KAAM,UACNsT,QAAS,aAAahL,EAAK6d,KAAK9jB,QAAQ,IAAM,WAAWiG,EAAKwd,cAAczjB,QAAQ,IAAM,YAAYiG,EAAKyO,OAAS,UAAUzO,EAAK7D,WAAWpC,QAAQ,IAAM,KAC5JkR,QAASkb,KAAKuB,UAAU,CACtB7J,IAAK7d,EAAK6d,KAAK9jB,QAAQ,IAAM,EAC7ByjB,aAAcxd,EAAKwd,cAAczjB,QAAQ,IAAM,EAC/C0U,MAAOzO,EAAKyO,OAAS,EACrBtS,UAAW6D,EAAK7D,WAAWpC,QAAQ,IAAM,GACxC,KAAM,KAIPue,KAAK6I,QAAQxX,OAAS,MACxB2O,KAAK6I,QAAU7I,KAAK6I,QAAQgH,OAAO,MAGrC7P,KAAK8P,mBACP,EAGAE,WAAAA,CAAY9jB,EAAOwG,EAASjB,EAAQ,QAClCuO,KAAK6I,QAAQiD,KAAK,CAChBna,WAAW,IAAI+Z,MAAO+F,cACtBhgB,QACAK,SAAU,QACV1S,KAAM,QACNsT,QAAS,GAAGxG,MAAUwG,MAIpBsN,KAAK6I,QAAQxX,OAAS,MACxB2O,KAAK6I,QAAU7I,KAAK6I,QAAQgH,OAAO,MAGrC7P,KAAK8P,mBACP,EAGAK,aAAAA,CAAczoB,GACZ,IAAKA,IAASA,EAAKA,KAAM,OAEzB,MAAMoqB,EAAUpqB,EAAKA,KACfqqB,EAA0B,oBAAdrqB,EAAKtI,KACjBqS,EAAQsgB,EAAY,OAAS,QAE7Brf,EAAUqf,EACZ,SAASD,EAAQ9pB,QAAU,SAAS8pB,EAAQxf,KAAO,QAAQwf,EAAQE,eAAiB,OACpF,SAASF,EAAQ9pB,QAAU,SAAS8pB,EAAQxf,KAAO,SAASwf,EAAQ3I,OAAS,SAEjFnJ,KAAK6I,QAAQiD,KAAK,CAChBna,WAAW,IAAI+Z,MAAO+F,cACtBhgB,QACAK,SAAU,UACV1S,KAAM2yB,EAAY,UAAY,UAC9Brf,UACAC,QAASkb,KAAKuB,UAAU,CACtBpnB,OAAQ8pB,EAAQ9pB,QAAU,MAC1BsK,IAAKwf,EAAQxf,KAAO,IACpBC,OAAQuf,EAAQvf,SAAWwf,EAAY,IAAM,KAC7CC,cAAeF,EAAQE,eAAiB,EACxC7I,MAAO2I,EAAQ3I,OAAS,KACxB8I,UAAWH,EAAQG,WAAa,MAC/B,KAAM,GACT/f,UAAW,CACTlK,OAAQ8pB,EAAQ9pB,QAAU,MAC1BsK,IAAKwf,EAAQxf,KAAO,IACpBC,OAAQuf,EAAQvf,SAAWwf,EAAY,IAAM,KAC7C5f,aAAa,KAKb6N,KAAK6I,QAAQxX,OAAS,MACxB2O,KAAK6I,QAAU7I,KAAK6I,QAAQgH,OAAO,MAGrC7P,KAAK8P,mBACP,EAGAte,WAAAA,CAAYC,GACV,MAAO,OAAOA,GAChB,EAGAe,cAAAA,CAAeD,GACb,IAAKA,EAAQ,MAAO,GACpB,MAAM+b,EAAO4D,SAAS3f,GACtB,OAAI+b,GAAQ,KAAOA,EAAO,IAAY,iBAClCA,GAAQ,KAAOA,EAAO,IAAY,kBAClCA,GAAQ,KAAOA,EAAO,IAAY,sBAClCA,GAAQ,IAAY,sBACjB,EACT,EAGAlc,cAAAA,CAAepK,GACb,IAAKA,EAAQ,MAAO,GACpB,OAAOA,EAAO6J,eACZ,IAAK,MAAO,MAAO,aACnB,IAAK,OAAQ,MAAO,cACpB,IAAK,MAAO,MAAO,aACnB,IAAK,SAAU,MAAO,gBACtB,IAAK,QAAS,MAAO,eACrB,IAAK,OAAQ,MAAO,cACpB,IAAK,UAAW,MAAO,iBACvB,QAAS,MAAO,GAEpB,EAGAI,kBAAAA,CAAmBH,GACjB,OAAOA,GACL,IAAK,SAAU,MAAO,UACtB,IAAK,UAAW,MAAO,aACvB,IAAK,QAAS,MAAO,cACrB,IAAK,cAAe,MAAO,eAC3B,IAAK,QAAS,MAAO,OACrB,QAAS,MAAO,WAEpB,EAGA,iBAAMkd,GACJ,IAEE,MAAMrM,EAAS,CACblR,MAAyB,QAAlBuO,KAAKnP,SAAqB,GAAKmP,KAAKnP,SAC3CshB,MAAO,IACPnM,OAAQ,GAGJoM,QAAiBpS,KAAKqS,KAAKC,kBAAkBtS,KAAKuI,SAAU5F,GAElE3C,KAAK6I,SAAWuJ,EAAS1qB,KAAK0jB,MAAQ,IAAIjK,IAAI5P,GAAOyO,KAAK4P,eAAere,IAG7C,IAAxByO,KAAK6I,QAAQxX,QACf2O,KAAK6I,QAAQiD,KAAK,CAChBna,WAAW,IAAI+Z,MAAO+F,cACtBhgB,MAAO,OACPK,SAAU,QACV1S,KAAM,QACNsT,QAAS,8BAKbsN,KAAK8P,mBACP,CAAE,MAAO3G,GAEPnJ,KAAK6I,QAAU,GAEf7I,KAAK6I,QAAQiD,KAAK,CAChBna,WAAW,IAAI+Z,MAAO+F,cACtBhgB,MAAO,QACPK,SAAU,QACV1S,KAAM,QACNsT,QAAS,WAAWyW,EAAMzW,SAAW,WAIvCsN,KAAK8P,mBACP,CACF,EAGA,eAAMppB,GACJ,UAEQsZ,KAAKuS,SACT,6BACA,SACA,CACEC,kBAAmB,OACnBC,iBAAkB,KAClBrzB,KAAM,UACNszB,0BAA0B,IAK9B1S,KAAK0N,SAASiF,KAAK,iBAInB,IAAIC,EAAS,KAkBb,GAfI5S,KAAKze,WAAWsF,OAEkB,kBAAzBmZ,KAAKze,WAAWsF,MAAqBmZ,KAAKze,WAAWsF,KAAKpB,GACnEmtB,EAAS5S,KAAKze,WAAWsF,KAAKpB,GACW,kBAAzBua,KAAKze,WAAWsF,OAEhC+rB,EAAS5S,KAAKze,WAAWsF,OAKxB+rB,IACHA,EAAS5S,KAAKze,WAAWqxB,QAAU5S,KAAKze,WAAWunB,UAGhD8J,EACH,MAAM,IAAIC,MAAM,YAIlB,IAAIC,EAAQ,KAGVA,EAFE9S,KAAKze,WAAWwxB,IAEqB,kBAAxB/S,KAAKze,WAAWwxB,IAAmB/S,KAAKze,WAAWwxB,IAAIttB,GAAKua,KAAKze,WAAWwxB,IAGnF/S,KAAKze,WAAWuxB,OAAS9S,KAAKze,WAAWyxB,OAInD,MAAMZ,QAAiBpS,KAAKqS,KAAKY,QAAQL,EAAQ,CAC/CM,OAAO,EACPC,eAAe,EACfH,OAAQF,EACRM,gBAAiB,GAAGpT,KAAKze,WAAW2Y,kBAAiB,IAAIwR,MAAO+F,cAAc5B,MAAM,EAAG,IAAIwD,QAAQ,IAAK,KAAKA,QAAQ,KAAM,QAG7H,GAAwB,MAApBjB,EAAS7f,OAAgB,CAC3ByN,KAAK0N,SAASC,QAAQ,aAGtB,MAAM2F,EAAclB,EAAS1qB,KAAK6rB,WAAanB,EAAS1qB,KAAKjC,GAEzD6tB,EAEFtT,KAAKwT,QAAQ1H,KAAK,CAChB7jB,KAAM,2BACN0a,OAAQ,CAAEld,GAAI6tB,KAIhBpG,WAAW,KACTP,SAAS8G,UACR,IAEP,CAEF,CAAE,MAAOtK,GACP,GAAc,WAAVA,EAEF,YADAnJ,KAAK0N,SAASiF,KAAK,WAGrB3S,KAAK0N,SAASvE,MAAM,YAAcA,EAAMiJ,UAAU1qB,MAAMgL,SAAWyW,EAAMzW,SAAW,QACtF,CACF,EAGA,0BAAMud,GACJ,IAGE,MAAMmC,QAAiBpS,KAAKqS,KAAKqB,oBAAoB1T,KAAKuI,UACpDoL,EAAmBvB,EAAS1qB,KAI5BksB,EAAsD,MAAlCD,EAAiBtZ,aACrCwZ,EAAaF,EAAiBG,SAAwC,OAA7BH,EAAiBG,QAC1DC,EAAeJ,EAAiBzsB,eAAiBysB,EAAiBzsB,cAAgB,EAIpF0sB,IAAsBC,GAAcE,IACtC/T,KAAKpgB,SAAW,MAChBogB,KAAKze,WAAW8Y,aAAe,IAG/BsR,OAAOqI,OAAOhU,KAAKze,WAAYoyB,GAG/B3T,KAAKkQ,yBAGLlQ,KAAK0N,SAASC,QAAQ,WAGtB3N,KAAKiU,qBAKL/G,WAAW,KACTlN,KAAKiQ,wBACJ,IAGP,CAAE,MAAO9G,GAGPnJ,KAAKpgB,SAAW,MAChBogB,KAAKze,WAAW8Y,aAAe,IAC/B2F,KAAKkQ,yBACLlQ,KAAKkU,gBACP,CACF,EACA1E,+BAAAA,CAAgC2E,QAECnR,IAA3BmR,EAAW5lB,cACbyR,KAAKze,WAAWC,OAAS2yB,EAAW5lB,kBAEDyU,IAAjCmR,EAAWvY,oBACboE,KAAKze,WAAWM,gBAAkBsyB,EAAWvY,wBAEVoH,IAAjCmR,EAAWC,oBACbpU,KAAKze,WAAW8yB,gBAAkBF,EAAWC,wBAEVpR,IAAjCmR,EAAWG,oBACbtU,KAAKze,WAAWgzB,gBAAkBJ,EAAWG,wBAEPtR,IAApCmR,EAAWK,uBACbxU,KAAKze,WAAWkzB,gBAAkBN,EAAWK,2BAEVxR,IAAjCmR,EAAWO,oBACb1U,KAAKze,WAAWozB,gBAAkBR,EAAWO,wBAEV1R,IAAjCmR,EAAWS,oBACb5U,KAAKze,WAAWszB,gBAAkBV,EAAWS,wBAEV5R,IAAjCmR,EAAWW,oBACb9U,KAAKze,WAAWwzB,gBAAkBZ,EAAWW,wBAEf9R,IAA5BmR,EAAWa,eACbhV,KAAKze,WAAW2F,cAAgBitB,EAAWa,mBAEbhS,IAA5BmR,EAAWc,eACbjV,KAAKze,WAAW2zB,eAAiBf,EAAWc,aAC5CjV,KAAKze,WAAW4F,iBAAmBgtB,EAAWa,cAAgB,GAAKb,EAAWc,mBAElDjS,IAA1BmR,EAAWvlB,aACboR,KAAKze,WAAWsC,UAAYswB,EAAWvlB,iBAERoU,IAA7BmR,EAAW1lB,gBACbuR,KAAKze,WAAW4zB,aAAehB,EAAW1lB,oBAEZuU,IAA5BmR,EAAWiB,eACbpV,KAAKze,WAAWmC,SAAWuiB,KAAKC,MAAMiO,EAAWiB,eAInDpV,KAAK2P,cACP,EAGA,oBAAMuE,GACJ,IACE,MAAM9B,QAAiBpS,KAAKqS,KAAKqB,oBAAoB1T,KAAKuI,UAC1DvI,KAAKze,WAAa6wB,EAAS1qB,KAC3BsY,KAAKvgB,SAAWugB,KAAKze,WAAW2Y,WAChC8F,KAAK7f,KAAO6f,KAAKze,WAAW8zB,eAAiB,SAC7CrV,KAAKpgB,SAAWogB,KAAK1F,cAAc0F,KAAKze,WAAW8Y,cAGd,MAAjC2F,KAAKze,WAAW8Y,cAAyD,OAAjC2F,KAAKze,WAAW8Y,cAC1D2F,KAAKsV,yBAIPtV,KAAKiU,oBAGgC,MAAjCjU,KAAKze,WAAW8Y,eAClB2F,KAAKyM,gBAELzM,KAAKuV,0BAET,CAAE,MAAOpM,GACPnJ,KAAK0N,SAASvE,MAAM,WACtB,CACF,EAGAmM,sBAAAA,GACE,IAAKtV,KAAKze,WAAY,OAEtB,IAAIi0B,EAAexV,KAAK7f,MAAQ,GAGhC,IAAKq1B,GAAiC,WAAjBA,EACnB,GAAqC,MAAjCxV,KAAKze,WAAW8Y,aAAsB,CAExC,MAAMob,EAAczV,KAAKnF,iBACnBhZ,GAAmBme,KAAKze,WAAWM,iBAAmB,GAAGJ,QAAQ,GACjED,EAASwe,KAAKze,WAAWC,QAAU,EACnCqC,EAAYmc,KAAKze,WAAWsC,WAAa,EAE/C2xB,EAAe,aACfA,GAAgB,iBAAiB3zB,aAA2BL,SAAci0B,QAGxED,GADE3zB,EAAkB,IACJ,mBACPA,EAAkB,IACX,qBACPA,EAAkB,IACX,mBAEA,0BAGdgC,EAAY,EACd2xB,GAAgB,WAAW3xB,iBAClBA,EAAY,IACrB2xB,GAAgB,YAAY3xB,gBAGhC,MAAO,GAAqC,OAAjCmc,KAAKze,WAAW8Y,aAAuB,CAIhD,GAFAmb,EAAe,YAEXxV,KAAKze,WAAW2F,cAAgB,EAAG,CACrC,MAAMguB,EAAiBlV,KAAKze,WAAW2zB,gBAAkB,EACnDrxB,EAAYmc,KAAKze,WAAWsC,WAAa,EAC/C2xB,GAAgB,OAAOxV,KAAKze,WAAW2F,yBAAyBguB,WAAwBrxB,OAC1F,CAEA2xB,GAAgB,UAChBA,GAAgB,kBAChBA,GAAgB,gBAChBA,GAAgB,kBAChBA,GAAgB,cAClB,CAIExV,KAAKze,WAAWm0B,QAAU1V,KAAKze,WAAWo0B,YAC5CH,GAAgB,oBAAoBxV,KAAKze,WAAWm0B,gBAAgB1V,KAAKze,WAAWo0B,cAEhF3V,KAAKze,WAAWm0B,OAAS,GAC3BF,GAAgB,qBACPxV,KAAKze,WAAWo0B,UAAY,KACrCH,GAAgB,2BAIpBxV,KAAK7f,KAAOq1B,CACd,EAGA,wBAAM1G,GACJ,IACE,MAAMsD,QAAiBpS,KAAKqS,KAAKuD,0BAC3BC,EAAczD,EAAS1qB,KAAKouB,SAAW,CAAC,EAG9C9V,KAAKxS,eAAiB,CACpBC,YAAaooB,EAAYnE,KAAKqE,SAAW,EACzCnoB,eAAgBioB,EAAYlE,QAAQoE,SAAW,EAC/CjoB,aAAc+nB,EAAYjE,MAAMmE,SAAW,EAC3C9nB,aAAc4nB,EAAYG,SAASC,YAAc,EACjD/nB,aAAc2nB,EAAYG,SAASE,YAAc,EACjD7nB,mBAAoBwnB,EAAYM,aAAe,EAC/C5nB,YAAasnB,EAAYvQ,KAAO,EAChC7W,cAAeonB,EAAY1f,OAAS,EACpCvH,WAAYinB,EAAYjnB,YAAc,EACtCG,YAAa8mB,EAAY9mB,aAAe,SACxCE,UAAW4mB,EAAYnE,KAAK0E,OAAS,EACrCjnB,aAAc0mB,EAAYlE,QAAQpC,OAAS,EAC3ClgB,OAAQwmB,EAAYxmB,QAAU,KAElC,CAAE,MAAO8Z,GACPpI,QAAQoI,MAAM,YAAaA,GAE3BnJ,KAAKxS,eAAiB,CACpBC,YAAa,EACbG,eAAgB,EAChBE,aAAc,EACdG,aAAc,EACdC,aAAc,EACdG,mBAAoB,EACpBE,YAAa,EACbE,cAAe,EACfG,WAAY,EACZG,YAAa,UACbE,UAAW,EACXE,aAAc,EACdE,OAAQ,KAEZ,CACF,EAGA,2BAAM0f,GACJ,IACE,MAAMqD,QAAiBpS,KAAKqS,KAAKgE,uBAAuBrW,KAAKuI,UAC7DvI,KAAK4J,kBAAoBwI,EAAS1qB,MAAQ,CAAC,CAC7C,CAAE,MAAOyhB,GACPpI,QAAQoI,MAAM,cAAeA,GAE7BnJ,KAAK4J,kBAAoB,CACvB0M,eAAgB,UAChBC,uBAAwB,EACxBC,sBAAuB,EACvBC,eAAgB,EAChBC,eAAgB,EAChBC,oBAAqB,CACnBC,OAAQ,GACRxV,OAAQ,IAEVyV,cAAe,GAEnB,CACF,EAGA,oBAAM3mB,SACE8P,KAAKgP,cACXhP,KAAK0N,SAASC,QAAQ,SACtB3N,KAAK8P,mBACP,EAGA3f,SAAAA,GACE6P,KAAK6I,QAAU,GACf7I,KAAK0N,SAASC,QAAQ,QACxB,EAKAmJ,kBAAAA,GACE9W,KAAKxW,MAAQ,GACbwW,KAAKmK,iBAAmB,GACxBnK,KAAKoK,QAAU,GACfpK,KAAKqK,QAAU,GACfrK,KAAKsK,UAAY,GACjBtK,KAAKuK,QAAU,GACfvK,KAAKwK,QAAU,GACfxK,KAAKyK,QAAU,GACfzK,KAAK0K,QAAU,GACf1K,KAAK2K,cAAgB,EACvB,EAGAsJ,iBAAAA,GACElT,QAAQxP,IAAI,YAGZyO,KAAK8W,qBAEL,IAAIC,GAAc,EAElB,GAAI/W,KAAKze,WAAWy1B,aAClB,IACE,MAAMpU,EAASiL,KAAKC,MAAM9N,KAAKze,WAAWy1B,cACtCpU,EAAOqU,eAAiBrU,EAAOqU,cAAc5lB,OAAS,GACxD2O,KAAKkX,2BAA2BtU,EAAOqU,eACvCF,GAAc,GACLnU,EAAO8M,gBAAmD,MAAjC1P,KAAKze,WAAW8Y,eAClD2F,KAAKmX,iCAAiCvU,GACtCmU,GAAc,EAElB,CAAE,MAAO5N,GACPpI,QAAQoI,MAAM,YAAaA,EAC7B,EAIG4N,GAAe/W,KAAKze,WAAW2F,eAAiB8Y,KAAKze,WAAW2F,cAAgB,GAAsC,MAAjC8Y,KAAKze,WAAW8Y,eACxG2F,KAAKoX,8BACLL,GAAc,GAIXA,GAAgD,MAAjC/W,KAAKze,WAAW8Y,aAGxB0c,GACVhW,QAAQxP,IAAI,gBAHZwP,QAAQxP,IAAI,kBACZyO,KAAKuV,2BAMPvV,KAAKC,UAAU,KACb,GAAyB,MAArBD,KAAKhc,YAAqB,CAE5B,MAAMqzB,EAAerX,KAAKQ,MAAM8W,MAC5BD,GACFA,EAAa1H,eAGf3P,KAAK2P,cACP,GAEJ,EAGAuH,0BAAAA,CAA2BK,GA2BzB,GA1BAvX,KAAKxW,MAAQ,GACbwW,KAAKmK,iBAAmB,GACxBnK,KAAKoK,QAAU,GACfpK,KAAKqK,QAAU,GACfrK,KAAKsK,UAAY,GACjBtK,KAAKuK,QAAU,GACfvK,KAAKwK,QAAU,GACfxK,KAAKyK,QAAU,GACfzK,KAAK0K,QAAU,GACf1K,KAAK2K,cAAgB,GAGrB3K,KAAK4K,mBAAqB,CACxB1F,aAAc,CAAC,EACfI,IAAK,CAAC,EACNC,IAAK,CAAC,EACNpP,MAAO,CAAC,EACR0U,IAAK,CAAC,EACNrF,IAAK,CAAC,EACNsF,IAAK,CAAC,EACNrF,IAAK,CAAC,EACN5hB,UAAW,CAAC,GAEdmc,KAAK+K,oBAAsB,GAGvBwM,EAAalmB,OAAS,EAAG,CAC3B,MAAMmmB,EAAYD,EAAaA,EAAalmB,OAAS,GACrD2O,KAAK4I,aAAe,CAClB2G,MAAOiI,EAAUjI,OAAS,CAAC,EAC3BG,eAAgB8H,EAAU9H,gBAAkB,CAAC,EAEjD,CAEA6H,EAAazU,QAAQ,CAAC2U,EAAMpzB,KAE1B,MAAMsN,EAAY8lB,EAAK9lB,YAAa,IAAI+Z,MAAO+F,cAC/CzR,KAAKxW,MAAMsiB,KAAK,IAAIJ,KAAK/Z,GAAW+lB,sBAGpC,MAAMnI,EAAQkI,EAAKlI,OAAS,CAAC,EAC7BvP,KAAKmK,iBAAiB2B,KAAKyD,EAAM3T,mBAAqB,GACtDoE,KAAKoK,QAAQ0B,KAAKyD,EAAMhhB,aAAe,GACvCyR,KAAKqK,QAAQyB,KAAKyD,EAAMoI,aAAepI,EAAMhhB,aAAe,GAC5DyR,KAAKsK,UAAUwB,KAAKyD,EAAM9gB,eAAiB,GAC3CuR,KAAKuK,QAAQuB,KAAKyD,EAAMqI,mBAAqB,GAC7C5X,KAAKwK,QAAQsB,KAAKyD,EAAMmF,mBAAqB,GAC7C1U,KAAKyK,QAAQqB,KAAKyD,EAAMqF,mBAAqB,GAC7C5U,KAAK0K,QAAQoB,KAAKyD,EAAMuF,mBAAqB,GAC7C9U,KAAK2K,cAAcmB,KAAKyD,EAAM3gB,YAAc,GAGxC6oB,EAAK/H,gBACP/D,OAAOC,KAAK6L,EAAK/H,gBAAgB5M,QAAQnZ,IACvC,MAAMkuB,EAAQJ,EAAK/H,eAAe/lB,GAC5B3B,EAASgY,KAAK8X,qBAAqBnuB,EAAKkuB,GACxCE,EAAgB/X,KAAKgY,wBAAwBruB,EAAKkuB,GAClDI,EAAYjwB,EAAS,IAAM+vB,EAE5B/X,KAAK4K,mBAAmB1F,aAAa+S,KACxCjY,KAAK4K,mBAAmB1F,aAAa+S,GAAa,GAClDjY,KAAK4K,mBAAmBtF,IAAI2S,GAAa,GACzCjY,KAAK4K,mBAAmBrF,IAAI0S,GAAa,GACzCjY,KAAK4K,mBAAmBzU,MAAM8hB,GAAa,GAC3CjY,KAAK4K,mBAAmBC,IAAIoN,GAAa,GACzCjY,KAAK4K,mBAAmBpF,IAAIyS,GAAa,GACzCjY,KAAK4K,mBAAmBE,IAAImN,GAAa,GACzCjY,KAAK4K,mBAAmBnF,IAAIwS,GAAa,GACzCjY,KAAK4K,mBAAmB/mB,UAAUo0B,GAAa,IAGjDjY,KAAK4K,mBAAmB1F,aAAa+S,GAAWnM,KAAK+L,EAAMjc,mBAAqB,GAChFoE,KAAK4K,mBAAmBtF,IAAI2S,GAAWnM,KAAK+L,EAAMtpB,aAAe,GACjEyR,KAAK4K,mBAAmBrF,IAAI0S,GAAWnM,KAAK+L,EAAMF,aAAeE,EAAMtpB,aAAe,GACtFyR,KAAK4K,mBAAmBzU,MAAM8hB,GAAWnM,KAAK+L,EAAMppB,eAAiB,GACrEuR,KAAK4K,mBAAmBC,IAAIoN,GAAWnM,KAAK+L,EAAMD,mBAAqB,GACvE5X,KAAK4K,mBAAmBpF,IAAIyS,GAAWnM,KAAK+L,EAAMnD,mBAAqB,GACvE1U,KAAK4K,mBAAmBE,IAAImN,GAAWnM,KAAK+L,EAAMjD,mBAAqB,GACvE5U,KAAK4K,mBAAmBnF,IAAIwS,GAAWnM,KAAK+L,EAAM/C,mBAAqB,GACvE9U,KAAK4K,mBAAmB/mB,UAAUo0B,GAAWnM,KAAK+L,EAAMjpB,YAAc,GAEjEoR,KAAK+K,oBAAoBnR,SAASqe,IACrCjY,KAAK+K,oBAAoBe,KAAKmM,MAKxC,EAGAd,gCAAAA,CAAiCvU,GAG/B5C,KAAK4I,aAAe,CAClB2G,MAAO3M,EAAO2M,OAAS,CAAC,EACxBG,eAAgB9M,EAAO8M,gBAAkB,CAAC,GAI5C,MAAMH,EAAQ3M,EAAO2M,OAAS,CAAC,EACzB5d,EAAYiR,EAAOjR,YAAa,IAAI+Z,MAAO+F,cAEjDzR,KAAKxW,MAAQ,CAAC,IAAIkiB,KAAK/Z,GAAW+lB,sBAClC1X,KAAKmK,iBAAmB,CAACoF,EAAM3T,mBAAqB,GACpDoE,KAAKoK,QAAU,CAACmF,EAAMhhB,aAAe,GACrCyR,KAAKqK,QAAU,CAACkF,EAAMoI,aAAepI,EAAMhhB,aAAe,GAC1DyR,KAAKsK,UAAY,CAACiF,EAAM9gB,eAAiB,GACzCuR,KAAKuK,QAAU,CAACgF,EAAMqI,mBAAqB,GAC3C5X,KAAKwK,QAAU,CAAC+E,EAAMmF,mBAAqB,GAC3C1U,KAAKyK,QAAU,CAAC8E,EAAMqF,mBAAqB,GAC3C5U,KAAK0K,QAAU,CAAC6E,EAAMuF,mBAAqB,GAC3C9U,KAAK2K,cAAgB,CAAC4E,EAAM3gB,YAAc,EAE1C,EAGFwoB,2BAAAA,GAEE,MAAM1zB,EAAWsc,KAAKze,WAAWmC,UAAY,IACvCw0B,EAAajS,KAAKnK,IAAIpY,EAAW,GAAI,IAE3Csc,KAAKxW,MAAQ,GACbwW,KAAKmK,iBAAmB,GACxBnK,KAAKoK,QAAU,GACfpK,KAAKqK,QAAU,GACfrK,KAAKsK,UAAY,GACjBtK,KAAKuK,QAAU,GACfvK,KAAKwK,QAAU,GACfxK,KAAKyK,QAAU,GACfzK,KAAK0K,QAAU,GACf1K,KAAK2K,cAAgB,GAGrB,MAAMwN,EAAcnY,KAAKze,WAAWM,iBAAmB,EACjDL,EAASwe,KAAKze,WAAWC,QAAU,EACnC0F,EAAgB8Y,KAAKze,WAAW2F,eAAiB,EACjDkxB,EAAS10B,EAAW,EAAIwD,EAAgBxD,EAAWlC,EACnD62B,EAAcrY,KAAKze,WAAWkzB,iBAAiC,IAAd0D,EACjDG,EAActY,KAAKze,WAAWozB,iBAAiC,EAAdwD,EACjDI,EAAcvY,KAAKze,WAAWszB,iBAAiC,IAAdsD,EACjDK,EAAcxY,KAAKze,WAAWwzB,iBAAiC,EAAdoD,EACjDt0B,EAAYmc,KAAKze,WAAWsC,WAAa,EAGzC40B,EAAYzY,KAAKze,WAAWk3B,UAAY,IAAI/M,KAAK1L,KAAKze,WAAWk3B,WAAa,IAAI/M,KAExF,IAAK,IAAIgN,EAAI,EAAGA,EAAIR,EAAYQ,IAAK,CACnC,MAAMC,EAAO,IAAIjN,KAAK+M,EAAUG,UAAgB,IAAJF,GAC5C1Y,KAAKxW,MAAMsiB,KAAK6M,EAAKjB,sBAGrB,MAAMmB,EAAY,GAClB7Y,KAAKmK,iBAAiB2B,KAAK9L,KAAK8Y,aAAaX,EAAaU,IAC1D7Y,KAAKoK,QAAQ0B,KAAK9L,KAAK8Y,aAAaV,EAAQS,IAC5C7Y,KAAKqK,QAAQyB,KAAK9L,KAAK8Y,aAAat3B,EAAQq3B,IAC5C7Y,KAAKsK,UAAUwB,KAAK7F,KAAKhK,IAAI,EAAGgK,KAAKC,MAAMlG,KAAK8Y,aAAa,GAAID,MACjE7Y,KAAKuK,QAAQuB,KAAK9L,KAAK8Y,aAAaT,EAAaQ,IACjD7Y,KAAKwK,QAAQsB,KAAK9L,KAAK8Y,aAAaR,EAAaO,IACjD7Y,KAAKyK,QAAQqB,KAAK9L,KAAK8Y,aAAaP,EAAaM,IACjD7Y,KAAK0K,QAAQoB,KAAK9L,KAAK8Y,aAAaN,EAAaK,IACjD7Y,KAAK2K,cAAcmB,KAAK7F,KAAKhK,IAAI,EAAG+D,KAAK8Y,aAAaj1B,EAAWg1B,IACnE,CAEA,EAGFC,YAAAA,CAAaC,EAAWC,GACtB,MAAMH,EAAoC,GAAvB5S,KAAKgT,SAAW,IAAWD,EACxCE,EAAWH,GAAa,EAAIF,GAClC,OAAO5S,KAAKhK,IAAI,EAAGkd,OAAOD,EAASz3B,QAAQ,IAC7C,EAIA23B,qBAAAA,CAAsBhD,EAAOta,EAAKG,EAAK7c,GACrC,MAAMsI,EAAO,GACb,IAAIqxB,GAAajd,EAAMG,GAAO,EAC1Bod,EAAgC,IAAvBpT,KAAKgT,SAAW,IAE7B,IAAK,IAAIP,EAAI,EAAGA,EAAItC,EAAOsC,IAAK,CAE9BK,GAAaM,GAASpd,EAAMH,GAAO,IAGnC,MAAM+c,GAAa5S,KAAKgT,SAAW,KAAQhd,EAAMH,GAAO,GACxD,IAAI/K,EAAQgoB,EAAYF,EAGxB9nB,EAAQkV,KAAKhK,IAAIH,EAAKmK,KAAKnK,IAAIG,EAAKlL,IAGvB,eAAT3R,EACF2R,EAAQkV,KAAKhK,IAAI,EAAGgK,KAAKnK,IAAI,GAAI/K,IACf,UAAT3R,IACT2R,EAAQkV,KAAKC,MAAMnV,IAGrBrJ,EAAKokB,KAAKqN,OAAOpoB,EAAMtP,QAAQ,KAG3BwkB,KAAKgT,SAAW,KAClBI,EAAgC,IAAvBpT,KAAKgT,SAAW,IAE7B,CAEA,OAAOvxB,CACT,EAGA6tB,uBAAAA,GACExU,QAAQxP,IAAI,YAERyO,KAAKgL,wBACP4D,cAAc5O,KAAKgL,wBAIrBhL,KAAKgL,uBAAyB6D,YAAY,KACxC7O,KAAKsZ,2BACJ,IACL,EAGA,6BAAMA,GACJ,IACE,MAAMlH,QAAiBpS,KAAKqS,KAAKqB,oBAAoB1T,KAAKuI,UACpDhnB,EAAa6wB,EAAS1qB,KAmB5B,GAjB2B,OAAvBnG,EAAWg4B,SAA2C,KAAvBh4B,EAAWg4B,UAC5CvZ,KAAKzU,OAAShK,EAAWg4B,SACI,IAAzBvZ,KAAKkL,iBACPlL,KAAK3U,aAEP2U,KAAKkL,iBAAkB,GAIzBlL,KAAKwZ,oBAAoBj4B,GAGrBA,EAAWy1B,eACbhX,KAAKze,WAAWy1B,aAAez1B,EAAWy1B,cAIxCz1B,EAAWy1B,aACb,IACE,MAAMpU,EAASiL,KAAKC,MAAMvsB,EAAWy1B,cACjCpU,EAAOqU,eAAiBrU,EAAOqU,cAAc5lB,OAAS,EACxD2O,KAAKkX,2BAA2BtU,EAAOqU,eAC9BrU,EAAO2M,OAEhBvP,KAAKyZ,yBAAyB7W,EAAO2M,MAAO3M,EAAO8M,gBAIjD9M,EAAO8M,gBAET1P,KAAKC,UAAU,KAIb,GAFAD,KAAK2P,eAEoB,MAArB3P,KAAKhc,YAAqB,CAC5B,MAAMqzB,EAAerX,KAAKQ,MAAM8W,MAC5BD,GACFA,EAAa1H,cAEjB,GAGN,CAAE,MAAOxG,GACPpI,QAAQoI,MAAM,cAAeA,EAC/B,CAI8B,MAA5B5nB,EAAW8Y,eACb0G,QAAQxP,IAAI,gBACZyO,KAAKkQ,+BAEClQ,KAAKkU,iBAGf,CAAE,MAAO/K,GACPpI,QAAQoI,MAAM,cAAeA,GAE7BnJ,KAAKkQ,wBACP,CACF,EAGAA,sBAAAA,GACEnP,QAAQxP,IAAI,iBAGZyO,KAAK0Z,yBAGL1Z,KAAK2Z,wBAGL3Z,KAAK4Z,2BAGD5Z,KAAK2J,kBACPiF,cAAc5O,KAAK2J,iBACnB3J,KAAK2J,gBAAkB,MAIrB3J,KAAKiP,oBACPL,cAAc5O,KAAKiP,mBACnBjP,KAAKiP,kBAAoB,MAI3BjP,KAAK3P,aAAc,EAGnB2P,KAAK0M,mBAEL3L,QAAQxP,IAAI,cACd,EAGAmoB,sBAAAA,GACM1Z,KAAKgL,yBACP4D,cAAc5O,KAAKgL,wBACnBhL,KAAKgL,uBAAyB,KAC9BjK,QAAQxP,IAAI,aAEhB,EAGAioB,mBAAAA,CAAoBj4B,QAEQyhB,IAAtBzhB,EAAWC,SAAsBwe,KAAKze,WAAWC,OAASD,EAAWC,aACtCwhB,IAA/BzhB,EAAWM,kBAA+Bme,KAAKze,WAAWM,gBAAkBN,EAAWM,sBACjEmhB,IAAtBzhB,EAAWm0B,SAAsB1V,KAAKze,WAAWm0B,OAASn0B,EAAWm0B,aAC5C1S,IAAzBzhB,EAAWo0B,YAAyB3V,KAAKze,WAAWo0B,UAAYp0B,EAAWo0B,gBAC9C3S,IAA7BzhB,EAAW2F,gBAA6B8Y,KAAKze,WAAW2F,cAAgB3F,EAAW2F,oBACpD8b,IAA/BzhB,EAAW4F,kBAA+B6Y,KAAKze,WAAW4F,gBAAkB5F,EAAW4F,sBACzD6b,IAA9BzhB,EAAW2zB,iBAA8BlV,KAAKze,WAAW2zB,eAAiB3zB,EAAW2zB,qBAC5DlS,IAAzBzhB,EAAWsC,YAAyBmc,KAAKze,WAAWsC,UAAYtC,EAAWsC,gBACnDmf,IAAxBzhB,EAAWmC,WAAwBsc,KAAKze,WAAWmC,SAAWnC,EAAWmC,eAG7Csf,IAA5BzhB,EAAW8Y,eACb2F,KAAKze,WAAW8Y,aAAe9Y,EAAW8Y,aAC1C2F,KAAKpgB,SAAWogB,KAAK1F,cAAc/Y,EAAW8Y,cAElD,EAGAof,wBAAAA,CAAyBtF,EAAY0F,EAAgB,MACnD,MAAMxI,GAAM,IAAI3F,MAAOgM,qBAGvB1X,KAAKxW,MAAMsiB,KAAKuF,GAGhBrR,KAAKmK,iBAAiB2B,KAAKqI,EAAWvY,mBAAqB,GAC3DoE,KAAKoK,QAAQ0B,KAAKqI,EAAW5lB,aAAe,GAC5CyR,KAAKqK,QAAQyB,KAAKqI,EAAWwD,aAAexD,EAAW5lB,aAAe,GACtEyR,KAAKsK,UAAUwB,KAAKqI,EAAW1lB,eAAiB,GAChDuR,KAAKuK,QAAQuB,KAAKqI,EAAWyD,mBAAqB,GAClD5X,KAAKwK,QAAQsB,KAAKqI,EAAWO,mBAAqB,GAClD1U,KAAKyK,QAAQqB,KAAKqI,EAAWS,mBAAqB,GAClD5U,KAAK0K,QAAQoB,KAAKqI,EAAWW,mBAAqB,GAClD9U,KAAK2K,cAAcmB,KAAKqI,EAAWvlB,YAAc,GAG7CirB,GACFlO,OAAOmO,QAAQD,GAAe/W,QAAQ,EAAEnZ,EAAKkuB,MAC3C,MAAME,EAAgB/X,KAAKgY,wBAAwBruB,EAAKkuB,GAGnD7X,KAAK4K,mBAAmB1F,aAAa6S,KACxC/X,KAAK4K,mBAAmB1F,aAAa6S,GAAiB,GACtD/X,KAAK4K,mBAAmBtF,IAAIyS,GAAiB,GAC7C/X,KAAK4K,mBAAmBrF,IAAIwS,GAAiB,GAC7C/X,KAAK4K,mBAAmBzU,MAAM4hB,GAAiB,GAC/C/X,KAAK4K,mBAAmBC,IAAIkN,GAAiB,GAC7C/X,KAAK4K,mBAAmBpF,IAAIuS,GAAiB,GAC7C/X,KAAK4K,mBAAmBE,IAAIiN,GAAiB,GAC7C/X,KAAK4K,mBAAmBnF,IAAIsS,GAAiB,GAC7C/X,KAAK4K,mBAAmB/mB,UAAUk0B,GAAiB,IAIrD/X,KAAK4K,mBAAmB1F,aAAa6S,GAAejM,KAAK+L,EAAMjc,mBAAqB,GACpFoE,KAAK4K,mBAAmBtF,IAAIyS,GAAejM,KAAK+L,EAAMtpB,aAAe,GACrEyR,KAAK4K,mBAAmBrF,IAAIwS,GAAejM,KAAK+L,EAAMF,aAAeE,EAAMtpB,aAAe,GAC1FyR,KAAK4K,mBAAmBzU,MAAM4hB,GAAejM,KAAK+L,EAAMppB,eAAiB0lB,EAAW1lB,eAAiB,GACrGuR,KAAK4K,mBAAmBC,IAAIkN,GAAejM,KAAK+L,EAAMD,mBAAqB,GAC3E5X,KAAK4K,mBAAmBpF,IAAIuS,GAAejM,KAAK+L,EAAMnD,mBAAqB,GAC3E1U,KAAK4K,mBAAmBE,IAAIiN,GAAejM,KAAK+L,EAAMjD,mBAAqB,GAC3E5U,KAAK4K,mBAAmBnF,IAAIsS,GAAejM,KAAK+L,EAAM/C,mBAAqB,GAC3E9U,KAAK4K,mBAAmB/mB,UAAUk0B,GAAejM,KAAK+L,EAAMjpB,YAAc,GAGrEoR,KAAK+K,oBAAoBnR,SAASme,IACrC/X,KAAK+K,oBAAoBe,KAAKiM,IAKtC,EAGAgC,iBAAAA,CAAkBC,EAAWle,EAAKG,GAChC,GAAyB,IAArB+d,EAAU3oB,OACZ,OAAQyK,EAAMG,GAAO,EAGvB,MAAMge,EAAYD,EAAUA,EAAU3oB,OAAS,GACzC6oB,EAAQje,EAAMH,EACdqe,EAAoB,GAARD,EAEZE,GAAUnU,KAAKgT,SAAW,IAAOkB,EACvC,IAAIjB,EAAWe,EAAYG,EAK3B,OAFAlB,EAAWjT,KAAKhK,IAAIH,EAAKmK,KAAKnK,IAAIG,EAAKid,IAEhCC,OAAOD,EAASz3B,QAAQ,GACjC,EAKA6tB,eAAAA,GACE,GAAItP,KAAK4I,aAAc,CACrB,MAAMyI,GAAM,IAAI3F,MAAOgM,qBACvB1X,KAAKxW,MAAMsiB,KAAKuF,GAGZrR,KAAKxW,MAAM6H,OAAS,KACtB2O,KAAKxW,MAAM6wB,QACXra,KAAKmK,iBAAiBkQ,QACtBra,KAAKoK,QAAQiQ,QACbra,KAAKqK,QAAQgQ,QACbra,KAAKsK,UAAU+P,QACfra,KAAKuK,QAAQ8P,QACbra,KAAKwK,QAAQ6P,QACbra,KAAKyK,QAAQ4P,QACbra,KAAK0K,QAAQ2P,QACbra,KAAK2K,cAAc0P,QAGnB1O,OAAOC,KAAK5L,KAAK4K,mBAAmB1F,cAAcpC,QAAQiV,IACxD/X,KAAK4K,mBAAmB1F,aAAa6S,GAAesC,QACpDra,KAAK4K,mBAAmBtF,IAAIyS,GAAesC,QAC3Cra,KAAK4K,mBAAmBrF,IAAIwS,GAAesC,QAC3Cra,KAAK4K,mBAAmBzU,MAAM4hB,GAAesC,QAC7Cra,KAAK4K,mBAAmBC,IAAIkN,GAAesC,QAC3Cra,KAAK4K,mBAAmBpF,IAAIuS,GAAesC,QAC3Cra,KAAK4K,mBAAmBE,IAAIiN,GAAesC,QAC3Cra,KAAK4K,mBAAmBnF,IAAIsS,GAAesC,QAC3Cra,KAAK4K,mBAAmB/mB,UAAUk0B,GAAesC,WAKrD,MAAM9K,EAAQvP,KAAK4I,aAAa2G,OAAS,CAAC,EAC1CvP,KAAKmK,iBAAiB2B,KAAKyD,EAAM3T,mBAAqB,GACtDoE,KAAKoK,QAAQ0B,KAAKyD,EAAMhhB,aAAe,GACvCyR,KAAKqK,QAAQyB,KAAKyD,EAAMoI,aAAepI,EAAMhhB,aAAe,GAC5DyR,KAAKsK,UAAUwB,KAAKyD,EAAM9gB,eAAiB,GAC3CuR,KAAKuK,QAAQuB,KAAKyD,EAAMqI,mBAAqB,GAC7C5X,KAAKwK,QAAQsB,KAAKyD,EAAMmF,mBAAqB,GAC7C1U,KAAKyK,QAAQqB,KAAKyD,EAAMqF,mBAAqB,GAC7C5U,KAAK0K,QAAQoB,KAAKyD,EAAMuF,mBAAqB,GAC7C9U,KAAK2K,cAAcmB,KAAKyD,EAAM3gB,YAAc,GAGxCoR,KAAK4I,aAAa8G,gBACpB/D,OAAOmO,QAAQ9Z,KAAK4I,aAAa8G,gBAAgB5M,QAAQ,EAAEnZ,EAAKkuB,MAC9D,MAAME,EAAgB/X,KAAKgY,wBAAwBruB,EAAKkuB,GAGnD7X,KAAK4K,mBAAmB1F,aAAa6S,KACxC/X,KAAK4K,mBAAmB1F,aAAa6S,GAAiB,GACtD/X,KAAK4K,mBAAmBtF,IAAIyS,GAAiB,GAC7C/X,KAAK4K,mBAAmBrF,IAAIwS,GAAiB,GAC7C/X,KAAK4K,mBAAmBzU,MAAM4hB,GAAiB,GAC/C/X,KAAK4K,mBAAmBC,IAAIkN,GAAiB,GAC7C/X,KAAK4K,mBAAmBpF,IAAIuS,GAAiB,GAC7C/X,KAAK4K,mBAAmBE,IAAIiN,GAAiB,GAC7C/X,KAAK4K,mBAAmBnF,IAAIsS,GAAiB,GAC7C/X,KAAK4K,mBAAmB/mB,UAAUk0B,GAAiB,IAIrD/X,KAAK4K,mBAAmB1F,aAAa6S,GAAejM,KAAK+L,EAAMjc,mBAAqB,GACpFoE,KAAK4K,mBAAmBtF,IAAIyS,GAAejM,KAAK+L,EAAMtpB,aAAe,GACrEyR,KAAK4K,mBAAmBrF,IAAIwS,GAAejM,KAAK+L,EAAMF,aAAeE,EAAMtpB,aAAe,GAC1FyR,KAAK4K,mBAAmBzU,MAAM4hB,GAAejM,KAAK+L,EAAMppB,eAAiB8gB,EAAM9gB,eAAiB,GAChGuR,KAAK4K,mBAAmBC,IAAIkN,GAAejM,KAAK+L,EAAMD,mBAAqB,GAC3E5X,KAAK4K,mBAAmBpF,IAAIuS,GAAejM,KAAK+L,EAAMnD,mBAAqB,GAC3E1U,KAAK4K,mBAAmBE,IAAIiN,GAAejM,KAAK+L,EAAMjD,mBAAqB,GAC3E5U,KAAK4K,mBAAmBnF,IAAIsS,GAAejM,KAAK+L,EAAM/C,mBAAqB,GAC3E9U,KAAK4K,mBAAmB/mB,UAAUk0B,GAAejM,KAAK+L,EAAMjpB,YAAc,GAGrEoR,KAAK+K,oBAAoBnR,SAASme,IACrC/X,KAAK+K,oBAAoBe,KAAKiM,IAKtC,CACF,EAGA7L,kBAAAA,GACE,IAAKlM,KAAKze,WAAY,MAAO,GAG7B,GAAIye,KAAK4I,eACH5I,KAAK4I,aAAa8G,gBAAkB1P,KAAK4I,aAAa2G,OAAO,CAE/D,MAAMsK,EAAgB7Z,KAAK4I,aAAa8G,gBAAkB,CAAC,EAC3D,OAAO1P,KAAKsa,oBAAoBT,EAClC,CAIF,GAAI7Z,KAAKze,WAAWy1B,aAClB,IACE,MAAMpU,EAASiL,KAAKC,MAAM9N,KAAKze,WAAWy1B,cAQ1C,GALIpU,EAAO2M,QACTvP,KAAK4I,aAAe5I,KAAK4I,cAAgB,CAAC,EAC1C5I,KAAK4I,aAAa2G,MAAQ3M,EAAO2M,OAG/B3M,EAAO8M,eACT,OAAO1P,KAAKsa,oBAAoB1X,EAAO8M,eAE3C,CAAE,MAAOvG,GACPpI,QAAQoI,MAAM,cAAeA,EAC/B,CAIF,MAAqC,MAAjCnJ,KAAKze,WAAW8Y,cAAwB2F,KAAKze,WAAW2F,eAAiB8Y,KAAKze,WAAW2F,cAAgB,EACpG,CAAC,CACNe,KAAM+X,KAAKze,WAAW2Y,YAAc,KACpClS,OAAQ,MACRd,cAAe8Y,KAAKze,WAAW2F,eAAiB,EAChDC,gBAAiB6Y,KAAKze,WAAW4F,iBAAmB,EACpD+tB,eAAgBlV,KAAKze,WAAW2zB,gBAAkB,EAClDX,gBAAiBvU,KAAKze,WAAWgzB,iBAAmB,EACpDF,gBAAiBrU,KAAKze,WAAW8yB,iBAAmB,EACpDxyB,gBAAiBme,KAAKze,WAAWM,iBAAmB,EACpD4yB,gBAAiBzU,KAAKze,WAAWkzB,iBAAmB,EACpDE,gBAAiB3U,KAAKze,WAAWozB,iBAAmB,EACpDE,gBAAiB7U,KAAKze,WAAWszB,iBAAmB,EACpDE,gBAAiB/U,KAAKze,WAAWwzB,iBAAmB,EACpDzP,IAAKtF,KAAKua,eACVnC,OAAQpY,KAAKwa,kBACbh5B,OAAQwe,KAAKze,WAAWC,QAAU,EAClCi5B,UAAWza,KAAKze,WAAWC,QAAU,EACrCqC,WAAYmc,KAAKze,WAAWsC,WAAa,GAAGpC,QAAQ,KAKjD,EACT,EAGA64B,mBAAAA,CAAoBT,GAClB,MAAMa,EAAU,GAGVC,EAAgB,GACtB,IAAK,MAAOhxB,EAAKkuB,KAAUlM,OAAOmO,QAAQD,GAAgB,CACxD,MAAMe,EAAmB,CACvB3yB,KAAM+X,KAAKgY,wBAAwBruB,EAAKkuB,GACxC7vB,OAAQgY,KAAK8X,qBAAqBnuB,EAAKkuB,GACvC3wB,cAAe2wB,EAAM7C,cAAgB,EACrC7tB,gBAAiB0wB,EAAMgD,kBAAqBhD,EAAM7C,aAAe6C,EAAM5C,cAAiB,EACxFC,eAAgB2C,EAAM5C,cAAgB,EACtCV,gBAAiBtO,KAAKuG,MAAMqL,EAAMvD,mBAAqB,GACvDD,gBAAiBpO,KAAKuG,MAAMqL,EAAMzD,mBAAqB,GACvDvyB,gBAAiBokB,KAAKuG,MAAMqL,EAAMjc,mBAAqB,GACvD6Y,gBAAiBxO,KAAKuG,MAAMqL,EAAMrD,sBAAwB,GAC1DG,gBAAiB1O,KAAKuG,MAAMqL,EAAMnD,mBAAqB,GACvDG,gBAAiB5O,KAAKuG,MAAMqL,EAAMjD,mBAAqB,GACvDG,gBAAiB9O,KAAKuG,MAAMqL,EAAM/C,mBAAqB,GACvDxP,IAAkC,kBAAtBuS,EAAMtpB,YAA2B4qB,OAAOtB,EAAMtpB,aAAa9M,QAAQ,GAAK,OACpF22B,OAAqC,kBAAtBP,EAAMtpB,YAA2B4qB,OAAOtB,EAAMtpB,aAAa9M,QAAQ,GAAK,OACvFD,OAAqC,kBAAtBq2B,EAAMtpB,YAA2B4qB,OAAOtB,EAAMtpB,aAAa9M,QAAQ,GAAK,OACvFg5B,UAAwC,kBAAtB5C,EAAMtpB,YAA2B4qB,OAAOtB,EAAMtpB,aAAa9M,QAAQ,GAAK,OAC1FoC,UAAuC,kBAArBg0B,EAAMjpB,WAA0BuqB,OAAOtB,EAAMjpB,YAAYnN,QAAQ,GAAK,OACxF0zB,aAAc0C,EAAMppB,eAAiBopB,EAAM1hB,OAAS,GAEtDwkB,EAAc7O,KAAK8O,GACnBF,EAAQ5O,KAAK8O,EACf,CAGA,GAAID,EAActpB,OAAS,EAAG,CAC5B,MAAMypB,EAAe9a,KAAK+a,6BAA6BJ,GACvDD,EAAQM,QAAQF,EAClB,MAAO,GAAI9a,KAAK4I,cAAgB5I,KAAK4I,aAAa2G,MAAO,CAEvD,MAAMA,EAAQvP,KAAK4I,aAAa2G,MAC1B7rB,EAAWsc,KAAKze,WAAWmC,UAAY,EACvC00B,EAAS7I,EAAMyF,cAAgBzF,EAAMyF,aAAetxB,GAAUjC,QAAQ,GAAK,OAEjFi5B,EAAQM,QAAQ,CACd/yB,KAAM,KACND,OAAQ,MACRd,cAAeqoB,EAAMyF,cAAgB,EACrC7tB,gBAAkBooB,EAAMyF,aAAezF,EAAM0F,cAAiB,EAC9DC,eAAgB3F,EAAM0F,cAAgB,EACtCV,gBAAiBtO,KAAKuG,MAAM+C,EAAM+E,mBAAqB,GACvDD,gBAAiBpO,KAAKuG,MAAM+C,EAAM6E,mBAAqB,GACvDvyB,gBAAiBokB,KAAKuG,MAAM+C,EAAM3T,mBAAqB,GACvD6Y,gBAAiBxO,KAAKuG,MAAM+C,EAAMiF,sBAAwB,GAC1DG,gBAAiB1O,KAAKuG,MAAM+C,EAAMmF,mBAAqB,GACvDG,gBAAiB5O,KAAKuG,MAAM+C,EAAMqF,mBAAqB,GACvDG,gBAAiB9O,KAAKuG,MAAM+C,EAAMuF,mBAAqB,GACvDxP,KAAMiK,EAAMhhB,aAAe,GAAG9M,QAAQ,GACtC22B,OAAQA,EACR52B,QAAS+tB,EAAMoI,aAAepI,EAAMhhB,aAAe,GAAG9M,QAAQ,GAC9Dg5B,UAAWrC,EACXv0B,WAAY0rB,EAAM3gB,YAAc,GAAGnN,QAAQ,GAC3C0zB,aAAc5F,EAAM9gB,eAAiBuR,KAAK/Y,uBAE9C,MAAO,GAAI+Y,KAAKze,YAAcye,KAAKze,WAAW2F,cAAgB,EAAG,CAE/D,MAAMxD,EAAWsc,KAAKze,WAAWmC,UAAY,EACvC00B,EAASpY,KAAKze,WAAW2F,eAAiB8Y,KAAKze,WAAW2F,cAAgBxD,GAAUjC,QAAQ,GAAK,OAEvGi5B,EAAQM,QAAQ,CACd/yB,KAAM,KACND,OAAQ,MACRd,cAAe8Y,KAAKze,WAAW2F,eAAiB,EAChDC,gBAAiB6Y,KAAKze,WAAW4F,iBAAmB,EACpD+tB,eAAgBlV,KAAKze,WAAW2zB,gBAAkB,EAClDX,gBAAiBtO,KAAKuG,MAAMxM,KAAKze,WAAWgzB,iBAAmB,GAC/DF,gBAAiBpO,KAAKuG,MAAMxM,KAAKze,WAAW8yB,iBAAmB,GAC/DxyB,gBAAiBokB,KAAKuG,MAAMxM,KAAKze,WAAWM,iBAAmB,GAC/D4yB,gBAAiBxO,KAAKuG,MAAMxM,KAAKze,WAAWkzB,iBAAmB,GAC/DE,gBAAiB1O,KAAKuG,MAAMxM,KAAKze,WAAWozB,iBAAmB,GAC/DE,gBAAiB5O,KAAKuG,MAAMxM,KAAKze,WAAWszB,iBAAmB,GAC/DE,gBAAiB9O,KAAKuG,MAAMxM,KAAKze,WAAWwzB,iBAAmB,GAC/DzP,IAAK8S,EACLA,OAAQA,EACR52B,QAASwe,KAAKze,WAAWC,QAAU,GAAGC,QAAQ,GAC9Cg5B,WAAYza,KAAKze,WAAWC,QAAU,GAAGC,QAAQ,GACjDoC,WAAYmc,KAAKze,WAAWsC,WAAa,GAAGpC,QAAQ,GACpD0zB,aAAcnV,KAAK/Y,uBAEvB,CAEA,OAAOyzB,CACT,EAGAK,4BAAAA,CAA6BJ,GAC3B,IAAIzzB,EAAgB,EAChB+zB,EAAuB,EACvBC,EAAsB,EACtBC,EAAW,EACXC,EAAW,EACXC,EAAa,EACb9G,EAAkB,EAClBF,EAAkBiH,IAClBC,EAA0B,EAC1BC,EAA0B,EAC1BC,EAA0B,EAC1BC,EAA0B,EAC1BC,EAA0B,EAG9BhB,EAAc7X,QAAQ8Y,IACpB10B,GAAiB00B,EAAK10B,cACtB+zB,GAAwBW,EAAKz0B,gBAC7B+zB,GAAuBU,EAAK1G,eAC5BiG,GAAYU,WAAWD,EAAKtW,KAC5B8V,GAAYS,WAAWD,EAAKp6B,QAC5B65B,EAAapV,KAAKhK,IAAIof,EAAYO,EAAKzG,cAAgB,GACvDZ,EAAkBtO,KAAKhK,IAAIsY,EAAiBqH,EAAKrH,iBAC7CqH,EAAKvH,gBAAkB,IACzBA,EAAkBpO,KAAKnK,IAAIuY,EAAiBuH,EAAKvH,kBAInDkH,GAA2BK,EAAK/5B,gBAAkB+5B,EAAK10B,cACvDs0B,GAA2BI,EAAKnH,gBAAkBmH,EAAK10B,cACvDu0B,GAA2BG,EAAKjH,gBAAkBiH,EAAK10B,cACvDw0B,GAA2BE,EAAK/G,gBAAkB+G,EAAK10B,cACvDy0B,GAA2BC,EAAK7G,gBAAkB6G,EAAK10B,gBAIzD,MAAMrF,EAAkBqF,EAAgB,EAAI+e,KAAKuG,MAAM+O,EAA0Br0B,GAAiB,EAC5FutB,EAAkBvtB,EAAgB,EAAI+e,KAAKuG,MAAMgP,EAA0Bt0B,GAAiB,EAC5FytB,EAAkBztB,EAAgB,EAAI+e,KAAKuG,MAAMiP,EAA0Bv0B,GAAiB,EAC5F2tB,EAAkB3tB,EAAgB,EAAI+e,KAAKuG,MAAMkP,EAA0Bx0B,GAAiB,EAC5F6tB,EAAkB7tB,EAAgB,EAAI+e,KAAKuG,MAAMmP,EAA0Bz0B,GAAiB,EAC5FrD,EAAYqD,EAAgB,GAAMg0B,EAAsBh0B,EAAiB,KAAKzF,QAAQ,GAAK,OAMjG,OAJI4yB,IAAoBiH,MACtBjH,EAAkB,GAGb,CACLpsB,KAAM,KACND,OAAQ,MACRd,cAAeA,EACfC,gBAAiB8zB,EACjB/F,eAAgBgG,EAChB3G,gBAAiBA,EACjBF,gBAAiBpO,KAAKuG,MAAM6H,GAC5BxyB,gBAAiBA,EACjB4yB,gBAAiBA,EACjBE,gBAAiBA,EACjBE,gBAAiBA,EACjBE,gBAAiBA,EACjBzP,IAAK6V,EAAS15B,QAAQ,GACtB22B,OAAQ+C,EAAS15B,QAAQ,GACzBD,OAAQ45B,EAAS35B,QAAQ,GACzBg5B,UAAWW,EAAS35B,QAAQ,GAC5BoC,UAAWA,EACXsxB,aAAckG,GAAcrb,KAAK/Y,sBAErC,EAGA60B,mBAAAA,CAAoBC,GAClB,MAAMxM,EAAQ,CACZtnB,KAAM,KACND,OAAQ,MACRd,cAAe,EACfC,gBAAiB,EACjB+tB,eAAgB,EAChBX,gBAAiB,EACjBF,gBAAiBiH,IACjBz5B,gBAAiB,EACjB4yB,gBAAiB,EACjBE,gBAAiB,EACjBE,gBAAiB,EACjBE,gBAAiB,EACjBzP,IAAK,EACL8S,OAAQ,EACR52B,OAAQ,EACRi5B,UAAW,EACX52B,UAAW,GAGb,IAAIm4B,EAAoB,EACpBC,EAAW,EACXC,EAAW,EACXC,EAAW,EACXC,EAAW,EAyCf,OAvCAL,EAAgBjZ,QAAQ8Y,IACtBrM,EAAMroB,eAAiB00B,EAAK10B,cAC5BqoB,EAAMpoB,iBAAmBy0B,EAAKz0B,gBAC9BooB,EAAM2F,gBAAkB0G,EAAK1G,eAC7B3F,EAAMgF,gBAAkBtO,KAAKhK,IAAIsT,EAAMgF,gBAAiBqH,EAAKrH,iBAC7DhF,EAAM8E,gBAAkBpO,KAAKnK,IAAIyT,EAAM8E,gBAAiBuH,EAAKvH,iBAC7D9E,EAAMjK,KAAOuW,WAAWD,EAAKtW,KAC7BiK,EAAM6I,QAAUyD,WAAWD,EAAKxD,QAChC7I,EAAM/tB,QAAUq6B,WAAWD,EAAKp6B,QAChC+tB,EAAMkL,WAAaoB,WAAWD,EAAKnB,WAGnCuB,GAAqBJ,EAAK/5B,gBAAkB+5B,EAAK10B,cACjD+0B,GAAYL,EAAKnH,gBAAkBmH,EAAK10B,cACxCg1B,GAAYN,EAAKjH,gBAAkBiH,EAAK10B,cACxCi1B,GAAYP,EAAK/G,gBAAkB+G,EAAK10B,cACxCk1B,GAAYR,EAAK7G,gBAAkB6G,EAAK10B,gBAGtCqoB,EAAMroB,cAAgB,GACxBqoB,EAAM1tB,gBAAkBokB,KAAKuG,MAAMwP,EAAoBzM,EAAMroB,eAC7DqoB,EAAMkF,gBAAkBxO,KAAKuG,MAAMyP,EAAW1M,EAAMroB,eACpDqoB,EAAMoF,gBAAkB1O,KAAKuG,MAAM0P,EAAW3M,EAAMroB,eACpDqoB,EAAMsF,gBAAkB5O,KAAKuG,MAAM2P,EAAW5M,EAAMroB,eACpDqoB,EAAMwF,gBAAkB9O,KAAKuG,MAAM4P,EAAW7M,EAAMroB,eACpDqoB,EAAM1rB,UAAYs1B,OAAQ5J,EAAM2F,eAAiB3F,EAAMroB,cAAiB,KAAKzF,QAAQ,IAErF8tB,EAAM1rB,UAAY,OAGhB0rB,EAAM8E,kBAAoBiH,MAC5B/L,EAAM8E,gBAAkB,GAG1B9E,EAAMjK,IAAM6T,OAAO5J,EAAMjK,KAAO,GAAG7jB,QAAQ,GAC3C8tB,EAAM6I,OAASe,OAAO5J,EAAM6I,QAAU,GAAG32B,QAAQ,GACjD8tB,EAAM/tB,OAAS23B,OAAO5J,EAAM/tB,QAAU,GAAGC,QAAQ,GACjD8tB,EAAMkL,UAAYtB,OAAO5J,EAAMkL,WAAa,GAAGh5B,QAAQ,GAEhD8tB,CACT,EAIAgL,YAAAA,GACE,IAAKva,KAAKze,WAAY,MAAO,OAE7B,GAAIye,KAAK4I,cAAgB5I,KAAK4I,aAAa2G,OAAwD,kBAAxCvP,KAAK4I,aAAa2G,MAAMhhB,YACjF,OAAO4qB,OAAOnZ,KAAK4I,aAAa2G,MAAMhhB,aAAa9M,QAAQ,GAG7D,MAAMiC,EAAWsc,KAAKze,WAAWmC,UAAY,EACvCwD,EAAgB8Y,KAAKze,WAAW2F,eAAiB,EACvD,OAAOiyB,OAAOjyB,EAAgBxD,GAAUjC,QAAQ,EAClD,EAGA+4B,eAAAA,GACE,IAAKxa,KAAKze,WAAY,MAAO,OAC7B,MAAMmC,EAAWsc,KAAKze,WAAWmC,UAAY,EACvCwD,EAAgB8Y,KAAKze,WAAW2F,eAAiB,EACvD,OAAOiyB,OAAOjyB,EAAgBxD,GAAUjC,QAAQ,EAClD,EAGA46B,qBAAAA,GACE,IAAKrc,KAAKze,WAAY,MAAO,OAE7B,MAAM+6B,EAAQtc,KAAKze,WAAWM,iBAAmB,EAC3C06B,EAAQvc,KAAKze,WAAWkzB,iBAAmB,EAC3C+H,EAAQxc,KAAKze,WAAWozB,iBAAmB,EAC3C8H,EAAQzc,KAAKze,WAAWszB,iBAAmB,EAC3C6H,EAAQ1c,KAAKze,WAAWwzB,iBAAmB,EAC3CzP,EAAMtF,KAAKua,eACXhV,EAAMvF,KAAKze,WAAWC,QAAU,EAEtC,MAAO,UAAU86B,kBAAsBC,kBAAsBC,kBAAsBC,kBAAsBC,WAAepX,SAAWC,GACrI,EAGAtiB,UAAAA,CAAW01B,GACT,OAAKA,EACE,IAAIjN,KAAKiN,GAAMgE,iBADJ,IAEpB,EAGA/6B,kBAAAA,CAAmB+2B,GACjB,OAAKA,GAAwB,kBAATA,EACbA,EAAO,IAAO,GAAGQ,OAAOR,GAAMl3B,QAAQ,OAAS,GAAG03B,OAAOR,EAAK,KAAMl3B,QAAQ,MADrC,KAEhD,EAGAgC,cAAAA,CAAem5B,GACb,IAAKA,EAAS,MAAO,KACrB,MAAMC,EAAQ5W,KAAKC,MAAM0W,EAAU,MAC7BE,EAAU7W,KAAKC,MAAO0W,EAAU,KAAQ,IACxCG,EAAOH,EAAU,GACvB,MAAO,GAAGC,MAAUC,MAAYC,IAClC,EAGA/uB,WAAAA,CAAYgvB,GACV,IAAKA,GAA0B,kBAAVA,GAAsBC,MAAMD,GAAQ,MAAO,KAChE,MAAME,EAAI,KACJC,EAAQ,CAAC,IAAK,KAAM,KAAM,MAC1BzE,EAAIzS,KAAKC,MAAMD,KAAK1U,IAAI0U,KAAKhK,IAAI,EAAG+gB,IAAU/W,KAAK1U,IAAI2rB,IACvDnsB,EAAQisB,EAAQ/W,KAAKwI,IAAIyO,EAAGxE,GAClC,OAAOmD,WAAW1C,OAAOpoB,GAAOtP,QAAQ,IAAM07B,EAAMlX,KAAKnK,IAAI4c,EAAGyE,EAAM9rB,OAAS,GACjF,EAGAzK,eAAAA,CAAgBxH,GACd,MAAMg+B,EAAQ,CAAE,GAAM,OAAQ,GAAM,QACpC,OAAOA,EAAMh+B,IAAS,IACxB,EAGA0H,iBAAAA,CAAkBu2B,GAChB,MAAMC,EAAW,CAAE,GAAM,OAAQ,GAAM,QACvC,OAAOA,EAASD,IAAY,IAC9B,EAGAt2B,sBAAAA,CAAuB9C,GACrB,MAAMs5B,EAAQ,CAAE,OAAU,OAAQ,YAAe,QACjD,OAAOA,EAAMt5B,IAAS,IACxB,EAGAqW,aAAAA,CAAc/H,GACZ,MAAMirB,EAAW,CAAE,EAAK,MAAO,EAAK,MAAO,GAAM,QACjD,OAAOA,EAASjrB,IAAW,IAC7B,EAGAhF,gBAAAA,CAAiBwoB,GACf,OAAIA,EAAU,GAAW,gBACrBA,EAAU,GAAW,iBAClB,eACT,EAGApoB,mBAAAA,CAAoBooB,GAClB,OAAIA,EAAU,GAAW,gBACrBA,EAAU,GAAW,iBAClB,eACT,EAGApnB,iBAAAA,CAAkB8uB,GAChB,OAAIA,EAAO,EAAU,gBACjBA,EAAO,EAAU,iBACd,eACT,EAKAjsB,WAAAA,CAAYC,GACV,MAAO,OAAOA,GAChB,EAGA,2BAAM3E,SACEkT,KAAK8O,qBACX9O,KAAK0N,SAASC,QAAQ,UACxB,EAIApd,iBAAAA,GACE,GAAIyP,KAAK3P,YAAa,CAEpB,GAAqC,MAAjC2P,KAAKze,WAAW8Y,aAalB,OAFA2F,KAAK0N,SAASiF,KAAK,uBACnB3S,KAAK3P,aAAc,GAXnB2P,KAAK2J,gBAAkBkF,YAAY,KACjC7O,KAAK8O,qBACL9O,KAAK+O,wBACL/O,KAAKgP,eACJ,KAGHhP,KAAKyM,eAOT,MACMzM,KAAK2J,kBACPiF,cAAc5O,KAAK2J,iBACnB3J,KAAK2J,gBAAkB,MAGzB3J,KAAK0M,kBAET,EAGA1f,sBAAAA,GACE,OAAQgT,KAAK0J,oBACX,IAAK,YACH,MAAO,OACT,IAAK,aACH,MAAO,MACT,IAAK,eACH,MAAO,MACT,IAAK,QACH,MAAO,OACT,QACE,MAAO,MAEb,EAGAzc,sBAAAA,GACE,OAAQ+S,KAAK0J,oBACX,IAAK,YACH,MAAO,UACT,IAAK,aACH,MAAO,UACT,IAAK,eACH,MAAO,OACT,IAAK,QACH,MAAO,SACT,QACE,MAAO,OAEb,EAGAxc,0BAAAA,GACE,OAAQ8S,KAAK0J,oBACX,IAAK,YACH1J,KAAK0N,SAASC,QAAQ,iBACtB,MACF,IAAK,aACH3N,KAAK0N,SAASiF,KAAK,oBACnB,MACF,IAAK,eACL,IAAK,QACH3S,KAAKuS,SAAS,2BAA4B,OAAQ,CAChDC,kBAAmB,OACnBC,iBAAkB,KAClBrzB,KAAM,YACLs+B,KAAK,KACN1d,KAAKqP,uBACJsO,MAAM,KACP3d,KAAK0N,SAASiF,KAAK,WAErB,MACF,QACuC,MAAjC3S,KAAKze,WAAW8Y,aAClB2F,KAAKyM,gBAELzM,KAAK0N,SAASiF,KAAK,gBAG3B,EAEAjoB,YAAAA,GACEsV,KAAK5f,UAAW,EAChB4f,KAAK3f,aAAe,OACpB2f,KAAKC,UAAU,KACTD,KAAKQ,MAAMod,OACb5d,KAAKQ,MAAMod,MAAMtb,SAGvB,EACA/iB,YAAAA,GACEygB,KAAK5f,UAAW,EAChB4f,KAAK3f,aAAe,WACpB2f,KAAKyI,aAAezI,KAAKvgB,SACzBugB,KAAKC,UAAU,KACTD,KAAKQ,MAAMod,OACb5d,KAAKQ,MAAMod,MAAMtb,SAGvB,EACA7hB,aAAAA,GACEuf,KAAK5f,UAAW,EAEU,aAAtB4f,KAAK3f,cAA+B2f,KAAKyI,eAC3CzI,KAAKvgB,SAAWugB,KAAKyI,aAEzB,EAGA,kBAAMoV,GACJ7d,KAAK5f,UAAW,EAGZ4f,KAAKvgB,WAAaugB,KAAKze,WAAW2Y,YAGtC8F,KAAKvb,YACP,EAGA,kBAAMmG,GACJoV,KAAK5f,UAAW,EAGZ4f,KAAK7f,OAAS6f,KAAKze,WAAW8zB,eAGlCrV,KAAKvb,YAEP,EACAN,YAAAA,CAAaE,GACX2b,KAAKhc,YAAcK,EAGL,MAAVA,EAEE2b,KAAKze,YAAcye,KAAKze,WAAWy1B,eAErChX,KAAKiU,oBAGLjU,KAAKC,UAAU,KACb,MAAMoX,EAAerX,KAAKQ,MAAM8W,MAC5BD,GACFA,EAAa1H,eAGf3P,KAAK2P,kBAGU,MAAVtrB,GAAkB2b,KAAK8J,qBAQb,MAAVzlB,GAAkB2b,KAAK+J,wBAQb,MAAV1lB,IACJ2b,KAAKgK,cAKRhK,KAAK8P,qBAJL9P,KAAKgP,cACLhP,KAAKgK,eAAgB,KAVvBhK,KAAK+O,wBACL/O,KAAK+J,yBAA0B,EAGM,MAAjC/J,KAAKze,WAAW8Y,cAAwB2F,KAAK3P,aAC/C2P,KAAK8d,8BAbP9d,KAAK8O,qBACL9O,KAAK8J,sBAAuB,EAGS,MAAjC9J,KAAKze,WAAW8Y,cAAwB2F,KAAK3P,aAC/C2P,KAAK+d,0BAqBK,MAAV15B,GACF2b,KAAK2Z,wBAEO,MAAVt1B,GACF2b,KAAK4Z,0BAET,EAGAmE,sBAAAA,GACM/d,KAAKiK,2BACP2E,cAAc5O,KAAKiK,2BAErBjK,KAAKiK,0BAA4B4E,YAAY,KAC3C7O,KAAK8O,sBACJ,IACL,EAGA6K,qBAAAA,GACM3Z,KAAKiK,4BACP2E,cAAc5O,KAAKiK,2BACnBjK,KAAKiK,0BAA4B,KAErC,EAGA6T,yBAAAA,GACM9d,KAAKkK,8BACP0E,cAAc5O,KAAKkK,8BAErBlK,KAAKkK,6BAA+B2E,YAAY,KAC9C7O,KAAK+O,yBACJ,IACL,EAGA6K,wBAAAA,GACM5Z,KAAKkK,+BACP0E,cAAc5O,KAAKkK,8BACnBlK,KAAKkK,6BAA+B,KAExC,EAEA3lB,IAAAA,GACEqc,OAAOod,QAAQz5B,MACjB,EAGA,yBAAMO,CAAoBK,GACxB,GAAK6a,KAAKze,WAAWkE,GAMrB,GAAgB,UAAZN,EAAJ,CAKA6a,KAAK6J,cAAe,EACpB,IACE,IAAIuI,EACA6L,EAAW,UAAUje,KAAKze,WAAW2Y,eAAc,IAAIwR,MAAO+F,cAAc5B,MAAM,EAAG,MAEzF,OAAQ1qB,GACN,IAAK,QACHitB,QAAiBpS,KAAKqS,KAAK6L,mBAAmBle,KAAKze,WAAWkE,IAC9Dw4B,GAAY,QACZ,MACF,IAAK,OAEH7L,QAAiBpS,KAAKme,yBACtBF,GAAY,QACZ,MACF,IAAK,MAEH7L,QAAiBpS,KAAKoe,wBACtBH,GAAY,OACZ,MACF,IAAK,MACH7L,QAAiBpS,KAAKqS,KAAKgM,eAAe,CACxC9K,UAAWvT,KAAKze,WAAWkE,GAC3B64B,OAAQ,SAEVL,GAAY,QACZ,MACF,QAEE,YADAje,KAAK0N,SAASa,QAAQ,WAI1B,GAAI6D,GAAgC,MAApBA,EAAS7f,OAAgB,CAEvC,MAAMgsB,EAAO,IAAIC,KAAK,CAACpM,EAAS1qB,OAC1B+2B,EAAOxN,SAASyN,cAAc,KACpCD,EAAKE,KAAOC,IAAIC,gBAAgBN,GAChCE,EAAKK,SAAWb,EAChBhN,SAAS8N,KAAKC,YAAYP,GAC1BA,EAAKQ,QACLhO,SAAS8N,KAAKG,YAAYT,GAC1BG,IAAIO,gBAAgBV,EAAKE,MAEzB3e,KAAK0N,SAASC,QAAQ,SACxB,CACF,CAAE,MAAOxE,GACPpI,QAAQoI,MAAM,QAASA,GACvBnJ,KAAK0N,SAASvE,MAAM,UAAYA,EAAMiJ,UAAU1qB,MAAMgL,SAAWyW,EAAMzW,SAAW,QACpF,CAAE,QACAsN,KAAK6J,cAAe,CACtB,CApDA,MAFE7J,KAAK0N,SAASiF,MAAoB,SAAZxtB,EAAqB,SAAuB,QAAZA,EAAoB,QAAU,QAAjE,uBANnB6a,KAAK0N,SAASa,QAAQ,WA6D1B,EAGA,4BAAM4P,GACJ,OAAO,IAAIiB,QAASC,IAClBnS,WAAW,KACT,MAAMoS,EAActf,KAAKuf,4BACzBF,EAAQ,CACN9sB,OAAQ,IACR7K,KAAM43B,KAEP,MAEP,EAGA,2BAAMlB,GACJ,OAAO,IAAIgB,QAASC,IAClBnS,WAAW,KACT,MAAMsS,EAAa,aAAaxf,KAAKze,WAAW2Y,aAChDmlB,EAAQ,CACN9sB,OAAQ,IACR7K,KAAM83B,KAEP,OAEP,EAIA,+BAAMC,CAA0Bt6B,GAC9B6a,KAAKra,uBAAuBR,EAC9B,EAGAQ,sBAAAA,GAEEqa,KAAKrI,uBAAyB,CAC5BzE,SAAS,EACT8F,SAAU,SACV/Q,KAAM,GAAG+X,KAAKze,WAAW2Y,qBACzBT,QAAS,GACTE,WAAY,GAEhB,EAGA9B,kBAAAA,GACEmI,KAAKrI,uBAAuBzE,SAAU,EACtC8M,KAAKQ,MAAMkf,qBAAqBC,aAClC,EAGA,6BAAMxnB,GACC6H,KAAKQ,MAAMkf,qBAEhB1f,KAAKQ,MAAMkf,oBAAoBE,SAASC,UACtC,IAAIC,EAIF,OAAO,EAHP9f,KAAKrI,uBAAuBzE,SAAU,QAChC8M,KAAK+f,iBAAiB/f,KAAKrI,uBAAuBqB,WAK9D,EAGA,sBAAM+mB,CAAiB3gC,GACrB4gB,KAAK0N,SAASiF,KAAK,oBACrB,EAIA,0BAAMqN,CAAqBrd,GACzB,OAAO,IAAIyc,QAASC,IAClBnS,WAAW,KACTmS,EAAQ,CAAE9sB,OAAQ,IAAK7K,KAAM,CAAEgL,QAAS,aACvC,MAEP,EAGAutB,2BAAAA,GAEE,MAAMv4B,EAAOsY,KAAKze,WACZ2+B,EAAgBlgB,KAAK/c,WAAWyE,EAAKosB,SAAWpsB,EAAKy4B,YAAc,IAAIzU,MACvE0U,EAAapgB,KAAK1F,cAAc5S,EAAK2S,cACrCob,EAAczV,KAAKnF,iBAEzB,IAAIhS,EAAU,cAAcnB,EAAKwS,iBAyCjC,OAxCArR,GAAW,aAAau3B,MACxBv3B,GAAW,aAAaq3B,QAExBr3B,GAAW,aACXA,GAAW,aAAamX,KAAKpe,mBAAmB8F,EAAK7F,qBACrDgH,GAAW,aAAanB,EAAKlG,QAAU,GAAGC,QAAQ,OAClDoH,GAAW,UAAU4sB,OAEjB/tB,EAAK6sB,kBACP1rB,GAAW,aAAamX,KAAKpe,mBAAmB8F,EAAK6sB,sBAGnD7sB,EAAKR,gBACP2B,GAAW,WAAWnB,EAAKR,mBAGzBQ,EAAKhE,WACPmF,GAAW,aAAanB,EAAKhE,gBAG3BgE,EAAKyT,UAAYzT,EAAK0T,YACxBvS,GAAW,YAAYnB,EAAKyT,UAAYzT,EAAK0T,eAG3C1T,EAAKguB,QAAUhuB,EAAKiuB,aACtB9sB,GAAW,iBACPnB,EAAKguB,SACP7sB,GAAW,eAAenB,EAAKguB,aAE7BhuB,EAAKiuB,YACP9sB,GAAW,cAAcnB,EAAKiuB,iBAK9B3V,KAAK7f,MAAsB,WAAd6f,KAAK7f,OACpB0I,GAAW,eACXA,GAAWmX,KAAK7f,KAAKkgC,MAAM,MAAMlf,IAAImf,GAAQ,KAAKA,KAAQC,KAAK,OAG1D13B,CACT,EAEAwC,UAAAA,GACE,GAAI2U,KAAKzU,OAAQ,CAEf,MAAMi1B,EAASvP,SAASC,cAAc,4BAClCsP,IAEFA,EAAO/0B,IAAMuU,KAAKzU,QAEpByU,KAAKjU,aAAc,EACnBiU,KAAK5T,mBAAqB,EAC5B,MACE4T,KAAK0N,SAASa,QAAQ,eAE1B,EAEA3iB,gBAAAA,GACEoU,KAAKjU,aAAc,EACnBiU,KAAK5T,mBAAqB,EAC5B,EAEAN,iBAAAA,GACEkU,KAAKjU,aAAc,EACnBiU,KAAK5T,mBAAqB,aAAa4T,KAAKzU,iBAC9C,EAEAe,YAAAA,GACE0T,KAAK3U,YACP,EAIAH,iBAAAA,GACE,OAAI8U,KAAK1U,cACA,OACmC,MAAjC0U,KAAKze,WAAW8Y,aAClB,UAEA,SAEX,EAEAlP,iBAAAA,GACE,OAAI6U,KAAK1U,cACA,MACmC,MAAjC0U,KAAKze,WAAW8Y,aAClB,MAEA,KAEX,EAEA3N,wBAAAA,GACE,MAAqC,MAAjCsT,KAAKze,WAAW8Y,aACX,qCAEF,6CACT,EAMA,uBAAMxU,CAAkBV,GACtB,IACE,OAAQA,GACN,IAAK,iBACG6a,KAAKygB,iBACX,MACF,IAAK,gBACGzgB,KAAK0gB,sBACX,MACF,IAAK,gBACG1gB,KAAK2gB,qBACX,MACF,IAAK,SACH3gB,KAAK4gB,iBACL,MACF,QACE5gB,KAAK0N,SAASa,QAAQ,WAE5B,CAAE,MAAOpF,GACPpI,QAAQoI,MAAM,QAASA,GACvBnJ,KAAK0N,SAASvE,MAAM,UAAYA,EAAMzW,SAAW,QACnD,CACF,EAGA,oBAAM+tB,GACJ,IAEEzgB,KAAK3E,mBAAqB,CACxBnI,SAAS,EACTsI,SAAS,EACTvX,KAAM,SACNwX,KAAM,CACNxT,KAAM,GAAG+X,KAAKze,WAAW2Y,iBACzB/N,YAAa,iBACX2c,QAAS9I,KAAKze,WAAWsF,MAAMpB,IAAMua,KAAKze,WAAWqxB,OACrDhX,kBAAmBoE,KAAKze,WAAWM,iBAAmB,EACtDka,QAASiE,KAAKze,WAAWC,QAAU,EACnCwa,aAAcgE,KAAKnF,kBAAoB,EACvCqB,QAAS8D,KAAKze,WAAWm0B,QAAU,EACnCvZ,WAAY6D,KAAKze,WAAWo0B,WAAa,EACzCvZ,WAAW,EACX2M,WAAY/I,KAAKze,WAAWs/B,SAASp7B,IAAMua,KAAK8gB,OAAOne,OAAOoe,WAGpE,CAAE,MAAO5X,GACPnJ,KAAK0N,SAASvE,MAAM,eAAiBA,EAAMiJ,UAAU1qB,MAAMgL,SAAWyW,EAAMzW,SAAW,QACzF,CACF,EAGA,wBAAM6I,GACJ,UAEQyE,KAAKQ,MAAMwgB,gBAAgBpB,WAEjC5f,KAAK3E,mBAAmBG,SAAU,EAElC,MAAMmH,EAAS,IAAK3C,KAAK3E,mBAAmBI,MAGP,WAAjCuE,KAAK3E,mBAAmBpX,OAC1B0e,EAAO4Q,UAAYvT,KAAKze,WAAWkE,IAIrC,MAAM2sB,QAAiBpS,KAAKqS,KAAKoO,eAAe9d,GAExB,MAApByP,EAAS7f,QAAsC,MAApB6f,EAAS7f,SACtCyN,KAAK0N,SAASC,QAAQ,WACtB3N,KAAK3E,mBAAmBnI,SAAU,EAEtC,CAAE,MAAOiW,GACPnJ,KAAK0N,SAASvE,MAAM,aAAeA,EAAMiJ,UAAU1qB,MAAMgL,SAAWyW,EAAMzW,SAAW,QACvF,CAAE,QACAsN,KAAK3E,mBAAmBG,SAAU,CACpC,CACF,EAGA,yBAAMklB,GACJ,IAEE,MAAMtO,QAAiBpS,KAAKqS,KAAK4O,aAAa,CAC5ClY,WAAY/I,KAAKze,WAAWs/B,SAASp7B,IAAMua,KAAK8gB,OAAOne,OAAOoe,UAC9DjY,QAAS9I,KAAKze,WAAWsF,MAAMpB,IAAMua,KAAKze,WAAWqxB,OACrDxW,WAAW,IAGX,GAAwB,MAApBgW,EAAS7f,OAAgB,CAC7B,IAAIoK,EAAe,GAGfyV,EAAS1qB,KAAKgzB,SAAW5a,MAAMohB,QAAQ9O,EAAS1qB,KAAKgzB,SACvD/d,EAAeyV,EAAS1qB,KAAKgzB,QACpB5a,MAAMohB,QAAQ9O,EAAS1qB,MAChCiV,EAAeyV,EAAS1qB,KACf0qB,EAAS1qB,KAAKy5B,WAAarhB,MAAMohB,QAAQ9O,EAAS1qB,KAAKy5B,aAChExkB,EAAeyV,EAAS1qB,KAAKy5B,WAG3BxkB,EAAatL,OAAS,EAExB2O,KAAKzD,0BAA4B,CAC/BrJ,SAAS,EACTsI,SAAS,EACTmB,eACAD,mBAAoBC,EAAa,GAAGlX,IAGxCua,KAAK0N,SAASa,QAAQ,mBAExB,CACF,CAAE,MAAOpF,GACPnJ,KAAK0N,SAASvE,MAAM,eAAiBA,EAAMiJ,UAAU1qB,MAAMgL,SAAWyW,EAAMzW,SAAW,QACzF,CACF,EAGA,2BAAM+J,GACJ,IACE,IAAKuD,KAAKzD,0BAA0BG,mBAElC,YADAsD,KAAK0N,SAASa,QAAQ,cAIxBvO,KAAKzD,0BAA0Bf,SAAU,EAEzC,IAEE,MAAM4lB,EAAmBphB,KAAKzD,0BAA0BI,aAAa4U,KACnE3U,GAAYA,EAASnX,KAAOua,KAAKzD,0BAA0BG,oBAI7D,IAAI2kB,EAAe,CAAC,EACpB,GAAID,EACFC,EAAeD,OAGf,IACE,MAAME,QAA+BthB,KAAKqS,KAAKkP,kBAAkBvhB,KAAKzD,0BAA0BG,oBAI1D,MAAlC4kB,EAAuB/uB,SACrB+uB,EAAuB55B,KAAKkV,SAC9BykB,EAAeC,EAAuB55B,KAAKkV,SAClC0kB,EAAuB55B,OAChC25B,EAAeC,EAAuB55B,MAG5C,CAAE,MAAO85B,GACPzgB,QAAQoI,MAAM,aAAcqY,EAE9B,CAIF,MAAMpP,QAAiBpS,KAAKqS,KAAKqO,oBAAoB1gB,KAAKze,WAAWkE,GAAI,CACvEg8B,YAAazhB,KAAKzD,0BAA0BG,qBAG9C,GAAwB,MAApB0V,EAAS7f,OAAgB,CAE3ByN,KAAKzD,0BAA0BrJ,SAAU,EAGzC,MAAMwuB,EAAmBtP,EAAS1qB,KAG5Bi6B,EAAkB,CACtB/lB,uBAAkDoH,IAA/Bqe,EAAarP,cAA8BhS,KAAK4hB,eAAeP,EAAarP,eAAiBhS,KAAK4hB,eAAeP,EAAazlB,mBACjJG,aAA8BiH,IAArBqe,EAAa9b,IAAoBvF,KAAK4hB,eAAeP,EAAa9b,KAAOvF,KAAK4hB,eAAeP,EAAatlB,SACnHC,kBAA0CgH,IAA5Bqe,EAAazyB,WAA2BoR,KAAK4hB,eAAe,IAAMP,EAAazyB,YAAcoR,KAAK4hB,eAAeP,EAAarlB,cAC5IE,QAAS8D,KAAK4hB,eAAeP,EAAaQ,WAAaR,EAAanlB,SACpEC,WAAY6D,KAAK4hB,eAAeP,EAAaS,cAAgBT,EAAallB,aAI5E,IAAI4lB,EAAqB,KACrBL,EAAiBlkB,mBACnBukB,EAAqB,CACnBnmB,kBAAmBoE,KAAK4hB,eAAeF,EAAiBlkB,iBAAiB5B,mBAAqB8lB,EAAiBlkB,iBAAiBwU,eAChIjW,QAASiE,KAAK4hB,eAAeF,EAAiBlkB,iBAAiBzB,SAAW2lB,EAAiBlkB,iBAAiB+H,KAC5GvJ,kBAAiEgH,IAAnD0e,EAAiBlkB,iBAAiBxB,aAC9CgE,KAAK4hB,eAAeF,EAAiBlkB,iBAAiBxB,mBACJgH,IAAjD0e,EAAiBlkB,iBAAiB5O,WACjCoR,KAAK4hB,eAAe,IAAMF,EAAiBlkB,iBAAiB5O,YAAc,EAC9EsN,QAAS8D,KAAK4hB,eAAeF,EAAiBlkB,iBAAiBtB,SAAWwlB,EAAiBlkB,iBAAiBqkB,WAC5G1lB,WAAY6D,KAAK4hB,eAAeF,EAAiBlkB,iBAAiBrB,YAAculB,EAAiBlkB,iBAAiBskB,gBAMtH,MAAME,EAAuBD,GAAsBJ,EAInD3hB,KAAKjD,0BAA4B,CAC/B7J,SAAS,EACTsK,iBAAkBwkB,EAClB1jB,gBAAiBojB,EAAiBpjB,iBAAmB,CACnD1C,kBAAmBoE,KAAKze,WAAWM,iBAAmB,EACtDka,QAASiE,KAAKze,WAAWC,QAAU,EACnCwa,aAAcgE,KAAKnF,kBAAoB,EACvCqB,QAAS8D,KAAKze,WAAWm0B,QAAU,EACnCvZ,WAAY6D,KAAKze,WAAWo0B,WAAa,GAE3CvW,WAAYsiB,EAAiBtiB,YAAc,GAE/C,CACF,CAAE,MAAO+J,GACPpI,QAAQoI,MAAM,kBAAmBA,GAGjCnJ,KAAKzD,0BAA0BrJ,SAAU,EAGzC8M,KAAKjD,0BAA4B,CAC/B7J,SAAS,EACTsK,iBAAkB,CAChB5B,kBAAmB,MACnBG,QAAS,MACTC,aAAc,KACdE,QAAS,KACTC,WAAY,MAEdmC,gBAAiB,CACf1C,kBAAmBoE,KAAKze,WAAWM,iBAAmB,IACtDka,QAASiE,KAAKze,WAAWC,QAAU,GACnCwa,aAAcgE,KAAKnF,kBAAoB,GACvCqB,QAAS8D,KAAKze,WAAWm0B,QAAU,GACnCvZ,WAAY6D,KAAKze,WAAWo0B,WAAa,IAE3CvW,WAAY,GAEhB,CACF,CAAE,MAAO+J,GACPnJ,KAAK0N,SAASvE,MAAM,aAAeA,EAAMiJ,UAAU1qB,MAAMgL,SAAWyW,EAAMzW,SAAW,QACvF,CAAE,QACAsN,KAAKzD,0BAA0Bf,SAAU,CAC3C,CACF,EAGA,wBAAMmlB,GACJ,IAEE,MAAMsB,EAAiBjiB,KAAKkiB,qBAAqBliB,KAAKze,YAEpDye,KAAK0N,SAASC,QAAQ,kBAIxB3N,KAAKmiB,oBAAoBF,EAC3B,CAAE,MAAO9Y,GACPnJ,KAAK0N,SAASvE,MAAM,YAAcA,EAAMzW,SAAW,QACrD,CACF,EAGAwvB,oBAAAA,CAAqB3gC,GAEnB,IAAI6gC,EAAmB,EACvB,MAAMvuB,EAAc,GACdM,EAAkB,GAGlBtS,EAAkBme,KAAK4hB,eAAergC,EAAWM,iBACvD,GAAIA,EAAkB,EAAG,CAEvB,IAAIwgC,EAAoB,EACpBxgC,EAAkB,IACpBwgC,EAAoB,GACXxgC,EAAkB,KAC3BwgC,EAAoB,GACpBluB,EAAgB2X,KAAK,qCACZjqB,EAAkB,KAC3BwgC,EAAoB,GACpBxuB,EAAYiY,KAAK,CACf1sB,KAAM,OACN6U,SAAU,SACV9H,YAAa,UAAUtK,EAAgBJ,QAAQ,2BAEjD0S,EAAgB2X,KAAK,gCAErBuW,EAAoB,GACpBxuB,EAAYiY,KAAK,CACf1sB,KAAM,OACN6U,SAAU,OACV9H,YAAa,UAAUtK,EAAgBJ,QAAQ,qBAEjD0S,EAAgB2X,KAAK,wCAEvBsW,GAAoBC,CACtB,CAGA,MAAM7gC,EAASwe,KAAK4hB,eAAergC,EAAWC,QAC9C,GAAIA,EAAS,EAAG,CAEd,MAAM8gC,EAAYtiB,KAAK4hB,eAAergC,EAAW4Z,UAAY5Z,EAAW6Z,UAClEmnB,EAActc,KAAKhK,IAAIqmB,EAAY,EAAG,GAE5C,IAAIE,EAAW,EACXhhC,GAAwB,IAAd+gC,GACZC,EAAW,GACXruB,EAAgB2X,KAAK,kCACZtqB,GAAU+gC,GACnBC,EAAW,GACXruB,EAAgB2X,KAAK,oBACZtqB,GAAwB,GAAd+gC,GACnBC,EAAW,GACX3uB,EAAYiY,KAAK,CACf1sB,KAAM,MACN6U,SAAU,SACV9H,YAAa,SAAS3K,EAAOC,QAAQ,uBAEvC0S,EAAgB2X,KAAK,qCAErB0W,EAAW,GACX3uB,EAAYiY,KAAK,CACf1sB,KAAM,MACN6U,SAAU,OACV9H,YAAa,SAAS3K,EAAOC,QAAQ,wBAEvC0S,EAAgB2X,KAAK,oCAEvBsW,GAAoBI,CACtB,CAGA,MAAM3+B,EAAYmc,KAAK4hB,eAAergC,EAAWsC,WAEjD,QAAkBmf,IAAdnf,EAAyB,CAC3B,IAAI4+B,EAAa,EACb5+B,EAAY,GACd4+B,EAAa,GACJ5+B,EAAY,EACrB4+B,EAAa,GACJ5+B,EAAY,GACrB4+B,EAAa,GACb5uB,EAAYiY,KAAK,CACf1sB,KAAM,MACN6U,SAAU,SACV9H,YAAa,OAAOtI,EAAUpC,QAAQ,mBAExC0S,EAAgB2X,KAAK,qCAErB2W,EAAa,EACb5uB,EAAYiY,KAAK,CACf1sB,KAAM,MACN6U,SAAU,OACV9H,YAAa,OAAOtI,EAAUpC,QAAQ,kBAExC0S,EAAgB2X,KAAK,sCAEvBsW,GAAoBK,CACtB,CAGA,MAAM/M,EAAS1V,KAAK4hB,eAAergC,EAAWm0B,QAC9C,GAAIA,EAAS,EAAG,CACd,IAAIgN,EAAW,EACXhN,EAAS,GACXgN,EAAW,GACFhN,EAAS,IAClBgN,EAAW,GACX7uB,EAAYiY,KAAK,CACf1sB,KAAM,SACN6U,SAAU,MACV9H,YAAa,UAAUupB,EAAOj0B,QAAQ,uBAGxCihC,EAAW,EACX7uB,EAAYiY,KAAK,CACf1sB,KAAM,SACN6U,SAAU,OACV9H,YAAa,UAAUupB,EAAOj0B,QAAQ,oBAExC0S,EAAgB2X,KAAK,uCAEvBsW,GAAoBM,CACtB,CAEA,MAAM/M,EAAY3V,KAAK4hB,eAAergC,EAAWo0B,WACjD,GAAIA,EAAY,EAAG,CACjB,IAAIgN,EAAc,EACdhN,EAAY,GACdgN,EAAc,GACLhN,EAAY,IACrBgN,EAAc,EACd9uB,EAAYiY,KAAK,CACf1sB,KAAM,QACN6U,SAAU,SACV9H,YAAa,SAASwpB,EAAUl0B,QAAQ,iBAE1C0S,EAAgB2X,KAAK,qCAErB6W,EAAc,EACd9uB,EAAYiY,KAAK,CACf1sB,KAAM,QACN6U,SAAU,OACV9H,YAAa,SAASwpB,EAAUl0B,QAAQ,mBAE1C0S,EAAgB2X,KAAK,uCAEvBsW,GAAoBO,CACtB,CAGyB,IAArBP,IACFA,EAAmB,IAIM,IAAvBvuB,EAAYxC,QACdwC,EAAYiY,KAAK,CACf1sB,KAAM,OACN6U,SAAU,MACV9H,YAAa,uBAKc,IAA3BgI,EAAgB9C,SAClB8C,EAAgB2X,KAAK,6BACrB3X,EAAgB2X,KAAK,2BAIvB,MAAM8W,EAAgB,CACpBjM,oBAAqB90B,EAAkB,IAAM,KAAQA,EAAkB,IAAM,MAAQ,MACrFghC,iBAAkBh/B,EAAY,EAAI,KAAQA,EAAY,EAAI,KAAO,MACjEi/B,UAAWthC,EAAS,EAAKA,EAAS,GAAK,IAAM,KAAQ,KAYvD,OARID,EAAW4Z,SAAW,IACpBtX,EAAY,GAAK6xB,EAAS,IAAMC,EAAY,GAC9CxhB,EAAgB2X,KAAK,WAAWvqB,EAAW4Z,wCAClCtX,EAAY,GAAK6xB,EAAS,IAAMC,EAAY,KACrDxhB,EAAgB2X,KAAK,WAAWvqB,EAAW4Z,qCAIxC,CACL3H,kBAAmByS,KAAKnK,IAAImK,KAAKuG,MAAM4V,GAAmB,KAC1DvuB,YAAaA,EACbM,gBAAiBA,EACjB4uB,eAAgBH,EAEpB,EAGAT,mBAAAA,CAAoBa,GAClB,MAAM,kBAAExvB,EAAiB,YAAEK,EAAW,gBAAEM,GAAoB6uB,EAG5DhjB,KAAK/M,mBAAqB,CACxBC,SAAS,EACTM,oBACAK,cACAM,kBAEJ,EAGAysB,cAAAA,GACO5gB,KAAKze,WAAWsF,MAASmZ,KAAKze,WAAWqxB,OAK9C5S,KAAKijB,uBAJHjjB,KAAK0N,SAASa,QAAQ,aAK1B,EAGA,0BAAM0U,GACJ,IAEE,IAAIzuB,EAAawL,KAAKze,WAAWsF,KAIjCmZ,KAAKzL,iBAAmB,CACtBrB,SAAS,EACTsB,aAGJ,CAAE,MAAO2U,GACPpI,QAAQoI,MAAM,UAAWA,GACzBnJ,KAAK0N,SAASvE,MAAM,YAAcA,EAAMiJ,UAAU1qB,MAAMgL,SAAWyW,EAAMzW,SAAW,QACtF,CACF,EAGAtQ,kBAAAA,GAEE,OAAI4d,KAAKxS,gBAA6D,kBAApCwS,KAAKxS,eAAeC,YAC7C0rB,OAAOnZ,KAAKxS,eAAeC,aAAahM,QAAQ,GAGrDue,KAAKze,YAAgD,kBAA3Bye,KAAKze,WAAWm0B,OACrCyD,OAAOnZ,KAAKze,WAAWm0B,QAAQj0B,QAAQ,GAEzC,KACT,EAGAuH,kBAAAA,CAAmBjB,GAEjB,MAAmB,QAAfA,EAAIC,OACCgY,KAAK/Y,sBAIVc,EAAIotB,aACCptB,EAAIotB,aAINnV,KAAK/Y,qBACd,EAGAA,mBAAAA,GAEE,GAAI+Y,KAAK4I,cAAgB5I,KAAK4I,aAAa2G,OAASvP,KAAK4I,aAAa2G,MAAM9gB,cAC1E,OAAOuR,KAAK4I,aAAa2G,MAAM9gB,cAIjC,GAAIuR,KAAKze,WAAW4Z,SAClB,OAAO6E,KAAKze,WAAW4Z,SAIzB,GAAI6E,KAAKze,WAAW6Z,SAClB,OAAO6K,KAAKuG,MAAMxM,KAAKze,WAAW6Z,UAIpC,GAAI4E,KAAKze,WAAWsF,KAAM,CACxB,GAAImZ,KAAKze,WAAWsF,KAAKq8B,kBACvB,OAAOljB,KAAKze,WAAWsF,KAAKq8B,kBAE9B,GAAIljB,KAAKze,WAAWsF,KAAKsP,MACvB,OAAO6J,KAAKze,WAAWsF,KAAKsP,KAEhC,CAEA,MAAO,GACT,EAGA5T,qBAAAA,GAEE,OAAIyd,KAAKxS,gBAAgE,kBAAvCwS,KAAKxS,eAAeI,eAC7CurB,OAAOnZ,KAAKxS,eAAeI,gBAAgBnM,QAAQ,GAGxDue,KAAKze,YAAmD,kBAA9Bye,KAAKze,WAAWo0B,UACrCwD,OAAOnZ,KAAKze,WAAWo0B,WAAWl0B,QAAQ,GAE5C,KACT,EAGAu2B,uBAAAA,CAAwBruB,EAAKkuB,GAE3B,GAAIA,EAAM5vB,KACR,OAAO4vB,EAAM5vB,KAIf,GAAI4vB,EAAMsL,KACR,OAAOtL,EAAMsL,KAIf,MAAMC,EAAc,CAAC,MAAO,OAAQ,MAAO,SAAU,QAAS,OAAQ,WACtE,IAAK,MAAMp7B,KAAUo7B,EACnB,GAAIz5B,EAAI05B,WAAWr7B,EAAS,KAC1B,OAAO2B,EAAI8mB,UAAUzoB,EAAOqJ,OAAS,GAKzC,OAAO1H,CACT,EAGAmuB,oBAAAA,CAAqBnuB,EAAKkuB,GAExB,GAAIA,EAAM7vB,OACR,OAAO6vB,EAAM7vB,OAIf,MAAMo7B,EAAc,CAAC,MAAO,OAAQ,MAAO,SAAU,QAAS,OAAQ,WACtE,IAAK,MAAMp7B,KAAUo7B,EACnB,GAAIz5B,EAAI05B,WAAWr7B,EAAS,KAC1B,OAAOA,EAIX,MAAO,KACT,EAGAs7B,YAAAA,CAAavyB,GACX,MAAMqyB,EAAc,CAAC,MAAO,OAAQ,MAAO,SAAU,QAAS,OAAQ,WACtE,OAAOA,EAAYxpB,SAAS7I,GAAOc,cACrC,EAGA0xB,oBAAAA,CAAqB55B,EAAKkuB,GAExB,GAAIA,EAAMsL,KACR,OAAOtL,EAAMsL,KAIf,MAAMC,EAAc,CAAC,MAAO,OAAQ,MAAO,SAAU,QAAS,OAAQ,WACtE,IAAK,MAAMp7B,KAAUo7B,EACnB,GAAIz5B,EAAI05B,WAAWr7B,EAAS,KAC1B,OAAO2B,EAAI8mB,UAAUzoB,EAAOqJ,OAAS,GAKzC,OAAO1H,CACT,EAGA+J,aAAAA,CAAc8vB,GACZ,OAAIA,GAAS,GAAW,UACpBA,GAAS,GAAW,UACjB,SACT,EAGAxvB,eAAAA,CAAgBC,GACd,MAAMwvB,EAAU,CACd,IAAO,UACP,OAAU,UACV,KAAQ,UAEV,OAAOA,EAAQxvB,IAAa,MAC9B,EAGA,gBAAMxP,GACJ,IACE,MAAMke,EAAS,CACbzI,WAAY8F,KAAKvgB,SACjB41B,cAAerV,KAAK7f,MAIhBiyB,QAAiBpS,KAAKqS,KAAKqR,uBAAuB1jB,KAAKze,WAAWkE,GAAIkd,GAEpD,MAApByP,EAAS7f,SACXyN,KAAK0N,SAASC,QAAQ,QAEtB3N,KAAKze,WAAW2Y,WAAa8F,KAAKvgB,SAClCugB,KAAKze,WAAW8zB,cAAgBrV,KAAK7f,KAEzC,CAAE,MAAOgpB,GACPpI,QAAQoI,MAAM,QAASA,GACvBnJ,KAAK0N,SAASvE,MAAM,UAAYA,EAAMiJ,UAAU1qB,MAAMgL,SAAWyW,EAAMzW,SAAW,QACpF,CACF,EAGAvK,cAAAA,CAAeJ,EAAK47B,EAAQta,GAEN,cAAhBsa,EAAOvkC,OAKN4gB,KAAK5Y,cAER4Y,KAAKQ,MAAM8W,MAAMsM,mBAAmB77B,GAExC,EAGAD,qBAAAA,CAAsB+7B,GAEpB7jB,KAAKiL,aAAe4Y,CACtB,EAGAx8B,gBAAAA,GACE,GAAK2Y,KAAK5Y,aAYR4Y,KAAK5Y,cAAe,EAOpB4Y,KAAK0N,SAASiF,KAAK,wBAnBG,CAEtB,MAAMmR,EAAc9jB,KAAKQ,MAAM8W,MAAMyM,iBAAmB/jB,KAAKQ,MAAM8W,MAAMyM,mBAAqB,GAC9F,GAA2B,IAAvBD,EAAYzyB,OAEd,YADA2O,KAAK0N,SAASa,QAAQ,eAGxBvO,KAAKiL,aAAe6Y,EACpB9jB,KAAK5Y,cAAe,EACpB4Y,KAAK0N,SAASC,QAAQ,OAAO3N,KAAKiL,aAAa5Z,uBACjD,CAWF,EAEA3B,mBAAAA,GACOsQ,KAAKzQ,YAAe,eAAey0B,KAAKhkB,KAAKzQ,YAIlDyQ,KAAKnQ,iBAAmBmQ,KAAKzQ,WAH3ByQ,KAAK0N,SAASa,QAAQ,kCAI1B,EACA3e,qBAAAA,GACEoQ,KAAKzQ,WAAa,GAClByQ,KAAKnQ,iBAAmB,EAC1B,EAGA0O,kBAAAA,CAAmB0lB,EAAcC,EAAeC,GAE9C,MAAMrO,EAAU+F,WAAWoI,IAAiB,EACtCrnB,EAAWif,WAAWqI,IAAkB,EAE9C,GAAiB,IAAbtnB,EAEF,OAAgB,IAAZkZ,EAAsB,qBACnB,CAAC,UAAW,MAAO,gBAAgBlc,SAASuqB,GACjD,oBAAsB,mBAI1B,MAAMC,EAAiB,CAAC,UAAW,MAAO,gBAAgBxqB,SAASuqB,GAC7DE,GAASvO,EAAUlZ,GAAYA,EAAY,IAGjD,OAAIwnB,EACEC,GAAQ,GAAW,oBACnBA,IAAS,GAAW,mBACjB,qBAEHA,IAAS,GAAW,oBACpBA,GAAQ,GAAW,mBAChB,oBAEX,EAGA7lB,iBAAAA,CAAkBylB,EAAcC,EAAeC,GAE7C,MAAMrO,EAAU+F,WAAWoI,IAAiB,EACtCrnB,EAAWif,WAAWqI,IAAkB,EAE9C,GAAiB,IAAbtnB,EAEF,OAAmB,IAAZkZ,EAAgB,SAAW,IAAIA,EAAQr0B,QAAQ,SAGxD,MAAM4iC,EAAOvO,EAAUlZ,EACjB0nB,GAAgBD,EAAOznB,EAAY,KAAKnb,QAAQ,GAChD8iC,EAAWF,GAAQ,EAAI,IAAIA,EAAK5iC,QAAQ,KAAO4iC,EAAK5iC,QAAQ,GAC5D+iC,EAAcH,GAAQ,EAAI,IAAIC,KAAiB,GAAGA,KAExD,MAAO,GAAGC,MAAaC,IACzB,EAGAjlB,4BAAAA,GACE,MAAMklB,EAAUzkB,KAAKjD,0BACrB,IAAK0nB,IAAYA,EAAQnmB,kBAAoBmmB,EAAQjnB,iBACnD,MAAO,kBAGT,MAAMsY,EAAU2O,EAAQnmB,gBAClB1B,EAAW6nB,EAAQjnB,iBAEzB,IAAI4B,EAAa,YAGjB,QAAkC4D,IAA9B8S,EAAQla,wBAAkEoH,IAA/BpG,EAAShB,mBAAmCgB,EAAShB,kBAAoB,EAAG,CAC3H,MAAM8oB,GAAqB5O,EAAQla,kBAAoBgB,EAAShB,mBAAqBgB,EAAShB,kBAAqB,IAEjHwD,GADEslB,EAAmB,GACP,gBAAgBA,EAAiBjjC,QAAQ,gBAC9CijC,GAAoB,GACf,gBAAgBze,KAAK0e,IAAID,GAAkBjjC,QAAQ,gBAEnD,uBAAuBwkB,KAAK0e,IAAID,GAAkBjjC,QAAQ,WAE1E,MACE2d,GAAc,wBAIhB,QAAwB4D,IAApB8S,EAAQ/Z,cAA8CiH,IAArBpG,EAASb,SAAyBa,EAASb,QAAU,EAAG,CAC7F,MAAM6oB,GAAY9O,EAAQ/Z,QAAUa,EAASb,SAAWa,EAASb,QAAW,IAE1EqD,GADEwlB,EAAU,GACE,eAAeA,EAAQnjC,QAAQ,kBACpCmjC,GAAW,GACN,eAAe3e,KAAK0e,IAAIC,GAASnjC,QAAQ,kBAEzC,sBAAsBwkB,KAAK0e,IAAIC,GAASnjC,QAAQ,WAEhE,MACE2d,GAAc,uBAIhB,QAA6B4D,IAAzB8S,EAAQ9Z,mBAAwDgH,IAA1BpG,EAASZ,aAA4B,CAC/E,MAAM6oB,EAAkB/O,EAAQ9Z,aAAeY,EAASZ,aAEtDoD,GADEylB,EAAkB,EACN,eAAeA,EAAgBpjC,QAAQ,sBAC5CojC,GAAmB,EACd,eAAe5e,KAAK0e,IAAIE,GAAiBpjC,QAAQ,sBAEjD,8BAEhB,MACE2d,GAAc,uBAIhB,QAAwB4D,IAApB8S,EAAQ5Z,cAA8C8G,IAArBpG,EAASV,SAAyBU,EAASV,QAAU,EAAG,CAC3F,MAAM4oB,GAAYhP,EAAQ5Z,QAAUU,EAASV,SAAWU,EAASV,QAAW,IAE1EkD,GADE0lB,EAAU,GACE,kBAAkBA,EAAQrjC,QAAQ,sBACvCqjC,GAAW,GACN,kBAAkB7e,KAAK0e,IAAIG,GAASrjC,QAAQ,oBAE5C,uBAElB,CAEA,QAA2BuhB,IAAvB8S,EAAQ3Z,iBAAoD6G,IAAxBpG,EAAST,YAA4BS,EAAST,WAAa,EAAG,CACpG,MAAM4oB,GAAejP,EAAQ3Z,WAAaS,EAAST,YAAcS,EAAST,WAAc,IAEtFiD,GADE2lB,EAAa,GACD,iBAAiBA,EAAWtjC,QAAQ,sBACzCsjC,GAAc,GACT,iBAAiB9e,KAAK0e,IAAII,GAAYtjC,QAAQ,oBAE9C,sBAElB,CAGA,IAAIujC,EAAoB,GAExB,QAAkChiB,IAA9B8S,EAAQla,wBAAkEoH,IAA/BpG,EAAShB,mBAAmCgB,EAAShB,kBAAoB,EAAG,CACzH,MAAM8oB,GAAqB5O,EAAQla,kBAAoBgB,EAAShB,mBAAqBgB,EAAShB,kBAAqB,IAC/G8oB,EAAmB,IAAIM,EAAkBlZ,KAAK,SACpD,CAEA,QAAwB9I,IAApB8S,EAAQ/Z,cAA8CiH,IAArBpG,EAASb,SAAyBa,EAASb,QAAU,EAAG,CAC3F,MAAM6oB,GAAY9O,EAAQ/Z,QAAUa,EAASb,SAAWa,EAASb,QAAW,IACxE6oB,GAAW,IAAII,EAAkBlZ,KAAK,QAC5C,CAEA,QAA6B9I,IAAzB8S,EAAQ9Z,mBAAwDgH,IAA1BpG,EAASZ,aAA4B,CAC7E,MAAM6oB,EAAkB/O,EAAQ9Z,aAAeY,EAASZ,aACpD6oB,GAAmB,GAAGG,EAAkBlZ,KAAK,QACnD,CAEA,MAAMmZ,EAAaD,EAAkB3zB,OAUrC,OAPE+N,GADiB,IAAf6lB,EACY,uCACU,IAAfA,EACK,0BAA0BD,EAAkB,oBAE5C,iCAAiCA,EAAkBzE,KAAK,sBAGjEnhB,CACT,EAGA,4BAAMnC,GACJ,IACE+C,KAAK0N,SAASiF,KAAK,iBAIN3S,KAAKze,WAAWkE,GACdua,KAAKzD,0BAA0BG,mBAS9CwQ,WAAW,KACTlN,KAAK0N,SAASC,QAAQ,aACrB,KAEL,CAAE,MAAOxE,GACPnJ,KAAK0N,SAASvE,MAAM,cAAgBA,EAAMiJ,UAAU1qB,MAAMgL,SAAWyW,EAAMzW,SAAW,QACxF,CACF,EAGAkvB,cAAAA,CAAe7wB,GACb,QAAciS,IAAVjS,GAAiC,OAAVA,EACzB,OAAO,EAIT,GAAc,SAAVA,GAA8B,cAAVA,GAAmC,KAAVA,EAC/C,OAAO,EAGT,MAAMm0B,EAAMrJ,WAAW9qB,GAGvB,OAAIksB,MAAMiI,KAASC,SAASD,GACnB,EAGFA,CACT,GAEFnlB,OAAAA,GACE,WAIE,GAFAC,KAAKuI,SAAWvI,KAAK8gB,OAAOne,OAAOld,IAAMua,KAAK8gB,OAAOsE,MAAM3/B,IAAMua,KAAK8gB,OAAOsE,MAAM7c,UAE9EvI,KAAKuI,SAIR,OAHAvI,KAAK0N,SAASvE,MAAM,4BAEpBnJ,KAAKwT,QAAQ1H,KAAK,CAAE7jB,KAAM,4BAKtB+X,KAAKkU,iBAEXlU,KAAKhc,YAAc,GACpB,EAfD,EAgBF,EACAqhC,aAAAA,GAEErlB,KAAKkQ,wBACP,GG5hKF,MAAM,IAA2B,QAAgB,GAAQ,CAAC,CAAC,SAASoV,IAAQ,CAAC,YAAY,qBAEzF,S", "sources": ["webpack://frontend-web/./src/views/PerformanceTest/PerformanceResult-Detail.vue", "webpack://frontend-web/./src/components/echart/ResponseTimeChart.vue", "webpack://frontend-web/./src/components/echart/ResponseTimeChart.vue?70dc", "webpack://frontend-web/./src/views/PerformanceTest/PerformanceResult-Detail.vue?b01c"], "sourcesContent": ["<template>\n  <el-scrollbar height=\"calc(100vh - 100px)\">\n  <div class=\"outer\">\n    <div class=\"box\">\n      <!-- 优化后的卡片布局 -->\n      <el-card class=\"report-header-card\">\n        <div class=\"report-header-content\">\n          <!-- 任务基本信息 -->\n          <div class=\"task-info-section\">\n            <div class=\"task-title-area\">\n              <div class=\"task-title-container\">\n                <el-button\n                  class=\"task-name-button\"\n                  type=\"text\"\n                  @click=\"editTaskName\"\n                  @click.stop\n                >\n                  {{ taskName }}\n                  <el-icon class=\"edit-icon\"><Edit /></el-icon>\n                </el-button>\n                <el-tag\n                  v-if=\"taskType=== '已完成'\"\n                  class=\"task-status-tag success\"\n                  effect=\"light\">\n                  {{taskType}}\n                </el-tag>\n                <el-tag\n                  v-if=\"taskType=== '运行中'\"\n                  class=\"task-status-tag running\"\n                  effect=\"light\">\n                  {{taskType}}\n                </el-tag>\n                <el-tag\n                  v-if=\"taskType=== '运行失败'\"\n                  class=\"task-status-tag error\"\n                  effect=\"light\">\n                  {{taskType}}\n                </el-tag>\n              </div>\n            </div>\n              <div class=\"task-description\">\n                  <div class=\"desc-text\">结果分析：{{ desc }}</div>\n              </div>\n              <el-input\n                v-if=\"inputDlg && editingField === 'taskName'\"\n                v-model=\"taskName\"\n                @blur=\"cancelEditing\"\n                ref=\"input\"\n                size=\"small\"\n                class=\"task-name-input\"\n                @click.stop\n              />\n          </div>\n          <!-- 性能指标卡片 -->\n          <div class=\"metrics-section\">\n            <div class=\"metric-card\">\n              <div class=\"metric-icon\">\n                <icon\n                  icon=\"grommet-icons:performance\"\n                  font-size=\"28\">\n                </icon>\n              </div>\n              <div class=\"metric-content\">\n                <div class=\"metric-title\">性能指标</div>\n                <div class=\"metric-values\">\n                  <div class=\"metric-item\">\n                    <span class=\"metric-label\">平均TPS：</span>\n                    <span class=\"metric-value\">{{ (reportData.avgTps || 0).toFixed(2) }}</span>\n                  </div>\n                  <div class=\"metric-item\">\n                    <span class=\"metric-label\">平均响应时间：</span>\n                    <span class=\"metric-value\">{{formatResponseTime(reportData.avgResponseTime)}}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"metric-card\">\n              <div class=\"metric-icon\">\n                <icon\n                  icon=\"grommet-icons:cpu\"\n                  font-size=\"28\">\n                </icon>\n              </div>\n              <div class=\"metric-content\">\n                <div class=\"metric-title\">系统资源</div>\n                <div class=\"metric-values\">\n                  <div class=\"metric-item\">\n                    <span class=\"metric-label\">执行结束后CPU：</span>\n                    <span class=\"metric-value\">{{getCurrentCpuUsage()}}%</span>\n                  </div>\n                  <div class=\"metric-item\">\n                    <span class=\"metric-label\">执行结束后内存：</span>\n                    <span class=\"metric-value\">{{getCurrentMemoryUsage()}}%</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"metric-card\">\n              <div class=\"metric-icon\">\n                <icon\n                  icon=\"el:user\"\n                  font-size=\"28\">\n                </icon>\n              </div>\n              <div class=\"metric-content\">\n                <div class=\"metric-title\">创建信息</div>\n                <div class=\"metric-values\">\n                  <div class=\"metric-item\">\n                    <span class=\"metric-label\">创建人：</span>\n                    <span class=\"metric-value\">{{reportData.executor || '未知'}}</span>\n                  </div>\n                  <div class=\"metric-item\">\n                    <span class=\"metric-label\">创建时间：</span>\n                    <span class=\"metric-value\">{{formatTime(reportData.create_time)}}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"metric-card\">\n              <div class=\"metric-icon\">\n                <icon\n                  icon=\"el:time\"\n                  font-size=\"28\">\n                </icon>\n              </div>\n              <div class=\"metric-content\">\n                <div class=\"metric-title\">运行信息</div>\n                <div class=\"metric-values\">\n                  <div class=\"metric-item\">\n                    <span class=\"metric-label\">运行时长：</span>\n                    <span class=\"metric-value\">{{formatDuration(reportData.duration)}}</span>\n                  </div>\n                  <div class=\"metric-item\">\n                    <span class=\"metric-label\">错误率：</span>\n                    <span class=\"metric-value\">{{reportData.errorRate || 0}}%</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </el-card>\n\n      <!-- Tab导航和操作按钮 -->\n      <div class=\"tab-navigation-container\">\n        <el-menu\n          :default-active=\"activeIndex\"\n          mode=\"horizontal\"\n          @select=\"handleSelect\"\n          class=\"report-tabs\"\n        >\n          <el-menu-item index=\"1\">指标详情</el-menu-item>\n          <el-menu-item index=\"2\">GUI</el-menu-item>\n          <el-menu-item index=\"3\">压力机监控</el-menu-item>\n          <el-menu-item index=\"4\">被测服务监控</el-menu-item>\n          <el-menu-item index=\"5\">日志</el-menu-item>\n        </el-menu>\n\n        <!-- 操作按钮组 -->\n        <div class=\"action-buttons\">\n          <el-button type=\"info\" @click=\"back\" class=\"action-btn\">\n            <el-icon><ArrowLeft /></el-icon>\n            返回\n          </el-button>\n          <el-button type=\"primary\" @click=\"saveReport\" class=\"action-btn\">\n            <el-icon><Check /></el-icon>\n            保存\n          </el-button>\n\n          <!-- 导出下拉菜单 -->\n          <el-dropdown trigger=\"click\" @command=\"handleExportCommand\" class=\"action-dropdown\">\n            <el-button type=\"primary\" :disabled=\"!reportData.id\" class=\"action-btn\">\n              导出<el-icon class=\"el-icon--right\"><arrow-down /></el-icon>\n            </el-button>\n            <template #dropdown>\n              <el-dropdown-menu>\n                <el-dropdown-item command=\"excel\">\n                  <el-icon><Document /></el-icon>导出Excel报告\n                </el-dropdown-item>\n                <el-dropdown-item command=\"html\">\n                  <el-icon><Monitor /></el-icon>生成HTML报告\n                </el-dropdown-item>\n                <el-dropdown-item command=\"pdf\">\n                  <el-icon><DocumentCopy /></el-icon>导出PDF报告\n                </el-dropdown-item>\n                <el-dropdown-item command=\"raw\">\n                  <el-icon><Files /></el-icon>导出原始数据\n                </el-dropdown-item>\n              </el-dropdown-menu>\n            </template>\n          </el-dropdown>\n\n          <!-- 通知按钮 -->\n          <el-button @click=\"showNotificationDialog\" type=\"success\" class=\"action-btn\">\n            <el-icon><Bell /></el-icon>\n            通知\n          </el-button>\n\n          <!-- 更多操作 -->\n          <el-dropdown trigger=\"click\" @command=\"handleMoreCommand\" class=\"action-dropdown\">\n            <el-button type=\"info\" class=\"action-btn\">\n              更多<el-icon class=\"el-icon--right\"><arrow-down /></el-icon>\n            </el-button>\n            <template #dropdown>\n              <el-dropdown-menu>\n                <el-dropdown-item command=\"baseline\">\n                  <el-icon><TrendCharts /></el-icon>创建基准线\n                </el-dropdown-item>\n                <el-dropdown-item command=\"compare\">\n                  <el-icon><DataAnalysis /></el-icon>与基准线对比\n                </el-dropdown-item>\n                <el-dropdown-item command=\"analyze\">\n                  <el-icon><PieChart /></el-icon>性能分析\n                </el-dropdown-item>\n                <el-dropdown-item command=\"config\">\n                  <el-icon><Setting /></el-icon>查看压测配置\n                </el-dropdown-item>\n              </el-dropdown-menu>\n            </template>\n          </el-dropdown>\n        </div>\n      </div>\n      <div v-if=\"activeIndex === '1'\" class=\"munu\">\n        <div style=\"display: flex; align-items: center; justify-content: space-between;\">\n          <div class=\"title-info\">\n              <el-divider direction=\"vertical\" class=\"divider\" />\n              压测信息\n          </div>\n          <div style=\"display: flex; align-items: center;\">\n              <el-button v-if=\"taskType === '运行中'\" class=\"runStop\" type=\"warning\">\n                  <el-icon><SwitchButton /></el-icon>\n                  <span style=\"margin-left: 5px\">暂停运行</span>\n              </el-button>\n              <el-button v-else class=\"runStop\" type=\"success\" @click=\"rerunTest\">\n                  <el-icon><SwitchButton /></el-icon>\n                  <span style=\"margin-left: 5px\">重新运行</span>\n              </el-button>\n          </div>\n        </div>\n        <div class=\"spacing\">\n          <span>任务类型：{{getTaskTypeText(reportData.taskType || reportData.task?.taskType)}}</span>\n          <span>压测模式：{{getRunPatternText(reportData.taskType)}}</span>\n          <span>控制模式：{{getDistributedModeText(reportData.task?.distributed_mode)}}</span>\n          <span>并发用户数：{{getCurrentUserCount()}}</span>\n          <span>总请求数：{{reportData.totalRequests || 0}}</span>\n          <span>成功请求数：{{reportData.successRequests || 0}}</span>\n        </div>\n        <el-button\n          style=\"margin-right: 25px;position: relative;margin-top: 10px\"\n          :type=\"isFilterMode ? 'warning' : 'success'\"\n          @click=\"toggleFilterMode\">\n          <el-icon><View /></el-icon>\n          <span style=\"margin-left: 5px\">{{ isFilterMode ? '显示全部' : '仅查看' }}</span>\n        </el-button>\n        <div class=\"table-desc\" >\n          <span>Max(ms)：最大响应时间</span>\n          <span>Min(ms)：最小响应时间</span>\n          <span>Avg(ms)：平均响应时间</span>\n          <span>90%ile(ms)：90%响应时间线</span>\n          <span>99%ile(ms)：99%响应时间线</span>\n          <span>平均RPS：平均每秒请求数</span>\n          <span>平均TPS：平均每秒处理事务数</span>\n        </div>\n        <div style=\"margin-bottom: 30px\">\n          <el-table\n            ref=\"table\"\n            :data=\"filteredMetrics\"\n            style=\"width: 100%\"\n            border\n            empty-text=\"暂无性能指标数据，请等待测试执行或测试完成后查看\"\n            @selection-change=\"handleSelectionChange\"\n            :row-key=\"row => row.method + '_' + row.name\"\n            :reserve-selection=\"true\"\n            @row-click=\"handleRowClick\">\n            <el-table-column\n              type=\"selection\"\n              width=\"55\"\n              :selectable=\"row => row.method !== 'ALL'\"\n            ></el-table-column>\n            <el-table-column prop=\"name\" label=\"接口名称\" align=\"center\" min-width=\"300\" >\n              <template #default=\"scope\">\n                <el-tooltip :content=\"scope.row.name\" placement=\"top\">\n                  <span class=\"interface-name\">{{ scope.row.name }}</span>\n                </el-tooltip>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"totalRequests\" label=\"总请求数\" width=\"120\" align=\"center\"></el-table-column>\n            <el-table-column prop=\"successRequests\" label=\"请求成功数\" align=\"center\" width=\"110\"></el-table-column>\n            <el-table-column prop=\"failedRequests\" label=\"请求失败数\" align=\"center\" width=\"110\"></el-table-column>\n            <el-table-column prop=\"currentUsers\" label=\"并发用户数\" align=\"center\" width=\"110\">\n              <template #default=\"scope\">\n                {{ getUserCountForRow(scope.row) }}\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"maxResponseTime\" label=\"Max(ms)\" align=\"center\" width=\"100\"></el-table-column>\n            <el-table-column prop=\"minResponseTime\" label=\"Min(ms)\" align=\"center\" width=\"100\"></el-table-column>\n            <el-table-column prop=\"avgResponseTime\" label=\"Avg(ms)\" align=\"center\" width=\"100\"></el-table-column>\n            <el-table-column prop=\"p50ResponseTime\" label=\"50%ile(ms)\" align=\"center\" width=\"110\"></el-table-column>\n            <el-table-column prop=\"p90ResponseTime\" label=\"90%ile(ms)\" align=\"center\" width=\"110\"></el-table-column>\n            <el-table-column prop=\"p95ResponseTime\" label=\"95%ile(ms)\" align=\"center\" width=\"110\"></el-table-column>\n            <el-table-column prop=\"p99ResponseTime\" label=\"99%ile(ms)\" align=\"center\" width=\"110\"></el-table-column>\n            <el-table-column prop=\"avgRps\" label=\"平均RPS\" align=\"center\" width=\"100\"></el-table-column>\n            <el-table-column prop=\"avgTpsAvg\" label=\"平均TPS\" align=\"center\" width=\"100\"></el-table-column>\n            <el-table-column prop=\"errorRate\" label=\"错误率%\" align=\"center\" width=\"110\">\n              <template #default=\"scope\">\n                <el-tag class=\"task-status-tag danger\" v-if=\"scope.row.errorRate > 5\" type=\"danger\">{{scope.row.errorRate}}%</el-tag>\n                <el-tag class=\"task-status-tag warning\" v-else-if=\"scope.row.errorRate > 1\" type=\"warning\">{{scope.row.errorRate}}%</el-tag>\n                <el-tag class=\"task-status-tag success\" v-else type=\"success\">{{scope.row.errorRate}}%</el-tag>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n        <div class=\"charts-container\">\n          <div class=\"chart-wrapper\">\n            <ResponseTimeChart\n              chartTitle=\"平均响应时间\"\n              chartWidth=\"100%\"\n              chartHeight=\"350px\"\n              chartUnit=\"单位(毫秒)\"\n              :xData=\"xData\"\n              :seriesData=\"responseTimeSeriesData\"\n              :key=\"'response-time-' + chartUpdateKey\"\n              chartType=\"responseTime\"\n            />\n          </div>\n          <div class=\"chart-wrapper\">\n            <ResponseTimeChart\n              chartTitle=\"每秒请求数(RPS)\"\n              chartWidth=\"100%\"\n              chartHeight=\"350px\"\n              chartUnit=\"单位(个)\"\n              :xData=\"xData\"\n              :seriesData=\"rpsSeriesData\"\n              :key=\"'rps-' + chartUpdateKey\"\n              chartType=\"rps\"\n            />\n          </div>\n          <div class=\"chart-wrapper\">\n            <ResponseTimeChart\n              chartTitle=\"每秒处理事务数(TPS)\"\n              chartWidth=\"100%\"\n              chartHeight=\"350px\"\n              chartUnit=\"单位(个)\"\n              :xData=\"xData\"\n              :seriesData=\"tpsSeriesData\"\n              :key=\"'tps-' + chartUpdateKey\"\n              chartType=\"tps\"\n            />\n          </div>\n          <div class=\"chart-wrapper\">\n            <ResponseTimeChart\n              chartTitle=\"并发用户数\"\n              chartWidth=\"100%\"\n              chartHeight=\"350px\"\n              chartUnit=\"单位(个)\"\n              :xData=\"xData\"\n              :seriesData=\"usersSeriesData\"\n              :key=\"'users-' + chartUpdateKey\"\n              chartType=\"users\"\n            />\n          </div>\n          <div class=\"chart-wrapper\">\n            <ResponseTimeChart\n              chartTitle=\"90%响应时间线\"\n              chartWidth=\"100%\"\n              chartHeight=\"350px\"\n              chartUnit=\"单位(毫秒)\"\n              :xData=\"xData\"\n              :seriesData=\"p90SeriesData\"\n              :key=\"'p90-' + chartUpdateKey\"\n              chartType=\"p90\"\n            />\n          </div>\n          <div class=\"chart-wrapper\">\n            <ResponseTimeChart\n              chartTitle=\"99%响应时间线\"\n              chartWidth=\"100%\"\n              chartHeight=\"350px\"\n              chartUnit=\"单位(毫秒)\"\n              :xData=\"xData\"\n              :seriesData=\"p99SeriesData\"\n              :key=\"'p99-' + chartUpdateKey\"\n              chartType=\"p99\"\n            />\n          </div>\n        </div>\n        <div class=\"title-info\" style=\"margin-top: 30px;\"><el-divider direction=\"vertical\" class=\"divider\"/>结果分析\n          <el-button :disabled=\"!isEdit\" @click=\"startEditing\" type=\"primary\" size=\"small\" style=\"margin-left: 10px;\">编辑</el-button>\n        </div>\n        <el-input\n            style=\"font-size: 14px; margin-top: 15px;\"\n            :rows=\"8\"\n            type=\"textarea\"\n            v-if=\"inputDlg && editingField === 'desc'\"\n            v-model=\"desc\"\n            @blur=\"saveAnalysis\"\n            ref=\"input\"\n            @click.stop\n            placeholder=\"请输入测试结果分析...\"\n        />\n        <p class=\"p_text\" v-else-if=\"!inputDlg || editingField !== 'desc'\" style=\"margin-top: 15px; line-height: 1.8;\">{{desc}}</p>\n      </div>\n      <div v-show=\"activeIndex === '2'\" class=\"munu\">\n        <div class=\"gui-controls\">\n          <div class=\"title-info\">\n            <el-divider direction=\"vertical\" class=\"divider\" />\n            GUI监控\n            <el-button\n              :type=\"getTestStatusType()\"\n              size=\"small\"\n              effect=\"light\">\n              {{ getTestStatusText() }}\n            </el-button>\n\n          </div>\n          <div class=\"gui-server-selection\" v-if=\"getTestStatusText() ==='运行中'\">\n            <el-button\n              type=\"success\"\n              @click=\"refreshGUI\"\n              size=\"small\">\n              刷新GUI\n            </el-button>\n          </div>\n        </div>\n\n        <!-- 测试未运行时的提示 -->\n        <div v-if=\"!isTestRunning\" class=\"test-not-running\">\n          <el-alert\n            title=\"测试未运行\"\n            :description=\"getTestNotRunningMessage()\"\n            type=\"info\"\n            show-icon\n            :closable=\"false\">\n          </el-alert>\n        </div>\n\n        <!-- GUI界面区域 -->\n        <div v-else-if=\"guiUrl\" class=\"iframe-container\">\n          <iframe\n            :src=\"guiUrl\"\n            @load=\"handleIframeLoad\"\n            @error=\"handleIframeError\"\n            >\n          </iframe>\n          <div v-if=\"iframeError\" class=\"iframe-error\">\n            <el-alert\n              title=\"GUI加载失败\"\n              :description=\"iframeErrorMessage\"\n              type=\"error\"\n              show-icon>\n              <template #default>\n                <div style=\"margin-top: 10px;\">\n                  <p>可能的原因：</p>\n                  <ul style=\"margin: 5px 0; padding-left: 20px;\">\n                    <li>测试服务未正确启动</li>\n                    <li>服务器不可访问</li>\n                    <li>测试已结束，GUI界面已关闭</li>\n                  </ul>\n                </div>\n              </template>\n            </el-alert>\n            <div style=\"margin-top: 10px; text-align: center;\">\n              <el-button type=\"primary\" @click=\"retryLoadGUI\">\n                重试加载\n              </el-button>\n            </div>\n          </div>\n        </div>\n\n        <!-- 无GUI配置时的提示 -->\n        <div v-else class=\"no-gui-message\">\n          <el-empty description=\"无可用的GUI界面\">\n            <p>后端未提供GUI URL，可能该测试没有可视化界面</p>\n          </el-empty>\n        </div>\n      </div>\n      <div v-if=\"activeIndex === '3'\" class=\"munu\">\n        <div class=\"title-info\">\n          <el-divider direction=\"vertical\" class=\"divider\" />\n          压力机监控\n          <el-button type=\"primary\" size=\"small\" @click=\"refreshMonitoringData\">刷新数据</el-button>\n          <!-- WebSocket连接状态指示器 -->\n          <div class=\"ws-status-indicator\">\n            <el-tooltip :content=\"getWebSocketStatusText()\" placement=\"top\">\n              <el-tag\n                :type=\"getWebSocketStatusType()\"\n                class=\"task-status-tag info\"\n                size=\"small\"\n                effect=\"light\"\n                @click=\"handleWebSocketStatusClick\"\n                style=\"cursor: pointer;\">\n                <el-icon><View /></el-icon>\n                {{ getWebSocketStatusText() }}\n              </el-tag>\n            </el-tooltip>\n          </div>\n        </div>\n        <div class=\"monitor-grid\">\n          <div class=\"monitor-card\">\n            <h4>系统资源</h4>\n            <div class=\"metric-item\">\n              <span>CPU使用率:</span>\n              <span :class=\"getCpuLevelClass(monitoringData.cpu_percent)\">{{monitoringData.cpu_percent || 0}}%</span>\n            </div>\n            <div class=\"metric-item\">\n              <span>内存使用率:</span>\n              <span :class=\"getMemoryLevelClass(monitoringData.memory_percent)\">{{monitoringData.memory_percent || 0}}%</span>\n            </div>\n            <div class=\"metric-item\">\n              <span>磁盘使用率:</span>\n              <span>{{monitoringData.disk_percent || 0}}%</span>\n            </div>\n            <div class=\"metric-item\">\n              <span>网络IO:</span>\n              <span>↑{{formatBytes(monitoringData.network_sent)}} ↓{{formatBytes(monitoringData.network_recv)}}</span>\n            </div>\n          </div>\n          <div class=\"monitor-card\">\n            <h4>压力机状态</h4>\n            <div class=\"metric-item\">\n              <span>活跃连接数:</span>\n              <span>{{monitoringData.active_connections || 0}}</span>\n            </div>\n            <div class=\"metric-item\">\n              <span>当前RPS:</span>\n              <span>{{monitoringData.current_rps || 0}}</span>\n            </div>\n            <div class=\"metric-item\">\n              <span>当前用户数:</span>\n              <span>{{monitoringData.current_users || 0}}</span>\n            </div>\n            <div class=\"metric-item\">\n              <span>错误率:</span>\n              <span :class=\"getErrorRateClass(monitoringData.error_rate)\">{{monitoringData.error_rate || 0}}%</span>\n            </div>\n          </div>\n          <div class=\"monitor-card\">\n            <h4>服务器信息</h4>\n            <div class=\"metric-item\">\n              <span>服务器类型:</span>\n              <span>{{monitoringData.server_type || 'single'}}</span>\n            </div>\n            <div class=\"metric-item\">\n              <span>CPU核心数:</span>\n              <span>{{monitoringData.cpu_cores || 'N/A'}}</span>\n            </div>\n            <div class=\"metric-item\">\n              <span>总内存:</span>\n              <span>{{formatBytes(monitoringData.total_memory)}}</span>\n            </div>\n            <div class=\"metric-item\">\n              <span>运行时长:</span>\n              <span>{{formatDuration(monitoringData.uptime)}}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div v-if=\"activeIndex === '4'\" class=\"munu\">\n        <div >\n          <!-- 新增：监控链接输入 -->\n          <el-input\n            v-model=\"monitorUrl\"\n            placeholder=\"输入被测服务监控页面URL\"\n            size=\"small\"\n            style=\"width: 320px; margin-left: 20px;\"\n            @keyup.enter=\"setMonitorIframeUrl\"\n            clearable\n          />\n          <el-button type=\"success\" size=\"small\" @click=\"setMonitorIframeUrl\" style=\"margin-left: 8px;\">确定</el-button>\n          <el-button type=\"info\" size=\"small\" @click=\"resetMonitorIframeUrl\" style=\"margin-left: 4px;\">重置</el-button>\n        </div>\n        <!-- 新增：iframe展示区 -->\n        <div v-if=\"monitorIframeUrl\" style=\"margin-top: 18px;\">\n          <iframe\n            :src=\"monitorIframeUrl\"\n            style=\"width: 100%; min-height: 600px; border: 1px solid #eee; border-radius: 8px;\"\n            frameborder=\"0\"\n          ></iframe>\n        </div>\n      </div>\n      <div v-if=\"activeIndex === '5'\" class=\"munu\">\n        <div class=\"title-info\">\n          <el-divider direction=\"vertical\" class=\"divider\" />\n          实时日志\n          <el-button type=\"primary\" size=\"small\" @click=\"refreshLogData\">刷新日志</el-button>\n          <el-button type=\"warning\" size=\"small\" @click=\"clearLogs\">清空日志</el-button>\n          <el-switch\n            v-model=\"autoRefresh\"\n            @change=\"toggleAutoRefresh\"\n            active-text=\"自动刷新\"\n            inactive-text=\"手动刷新\">\n          </el-switch>\n        </div>\n        <div class=\"log-controls\">\n          <el-input\n            v-model=\"logFilter\"\n            placeholder=\"过滤日志...\"\n            size=\"small\"\n            style=\"width: 200px; margin-right: 10px;\"\n            clearable>\n            <template #prefix>\n              <el-icon><Search /></el-icon>\n            </template>\n          </el-input>\n          <el-select v-model=\"logLevel\" placeholder=\"日志级别\" size=\"small\" style=\"width: 120px; margin-right: 10px;\">\n            <el-option label=\"全部\" value=\"all\"></el-option>\n            <el-option label=\"debug\" value=\"debug\"></el-option>\n            <el-option label=\"info\" value=\"info\"></el-option>\n            <el-option label=\"warning\" value=\"warning\"></el-option>\n            <el-option label=\"error\" value=\"error\"></el-option>\n          </el-select>\n          <el-select v-model=\"logCategory\" placeholder=\"日志类型\" size=\"small\" style=\"width: 120px; margin-right: 10px;\">\n            <el-option \n              v-for=\"(label, value) in logCategories\" \n              :key=\"value\" \n              :label=\"label\" \n              :value=\"value\">\n            </el-option>\n          </el-select>\n          <el-tag class=\"task-status-tag info\" type=\"info\">共 {{filteredLogs.length}} 条日志</el-tag>\n        </div>\n        <div class=\"log-container\">\n          <div v-for=\"(log, index) in filteredLogs\" :key=\"index\" :class=\"getLogClass(log.level)\" class=\"log-item\">\n            <span class=\"log-time\">[{{formatTime(log.timestamp)}}]</span>\n            <span class=\"log-level\">[{{log.level.toUpperCase()}}]</span>\n            <span class=\"log-category\" v-if=\"log.category\">\n              <el-icon :size=\"14\"><component :is=\"getLogCategoryIcon(log.category)\" /></el-icon>\n            </span>\n            <!-- 格式化的请求日志 -->\n            <template v-if=\"log.formatted && log.formatted.isFormatted\">\n              <span :class=\"getMethodClass(log.formatted.method)\" class=\"log-method\">{{log.formatted.method}}</span>\n              <span class=\"log-url\">{{log.formatted.url}}</span>\n              <span v-if=\"log.formatted.status\" :class=\"getStatusClass(log.formatted.status)\" class=\"log-status\">{{log.formatted.status}}</span>\n            </template>\n            <!-- 普通日志消息 -->\n            <span v-else class=\"log-message\">{{log.message}}</span>\n            <!-- 详细信息展开按钮 -->\n            <el-button \n              v-if=\"log.details\" \n              type=\"text\" \n              size=\"small\" \n              @click=\"log.showDetails = !log.showDetails\" \n              class=\"details-toggle\">\n              {{ log.showDetails ? '收起' : '详情' }}\n            </el-button>\n            <!-- 详细信息展示区 -->\n            <div v-if=\"log.details && log.showDetails\" class=\"log-details\">\n              <pre>{{log.details}}</pre>\n            </div>\n          </div>\n          <div v-if=\"filteredLogs.length === 0\" class=\"no-logs\">\n            <div class=\"empty-log-content\">\n              <el-icon class=\"empty-log-icon\"><Document /></el-icon>\n              <p class=\"empty-log-text\">暂无日志数据</p>\n              <p class=\"empty-log-hint\">日志将在测试运行时实时显示</p>\n            </div>\n          </div>\n        </div>\n      </div>\n  </div>\n  </div>\n  </el-scrollbar>\n\n  <!-- 性能分析结果对话框 -->\n  <el-dialog\n    v-model=\"analysisDialogData.visible\"\n    title=\"性能分析报告\"\n    width=\"60%\"\n    :destroy-on-close=\"true\"\n  >\n    <div class=\"analysis-dialog-content\">\n      <div class=\"analysis-section\">\n        <h4>性能得分</h4>\n        <div class=\"score-display\">\n          <el-progress\n            type=\"circle\"\n            :percentage=\"analysisDialogData.performance_score\"\n            :color=\"getScoreColor(analysisDialogData.performance_score)\"\n            :width=\"120\"\n          >\n            <template #default=\"{ percentage }\">\n              <span class=\"score-text\">{{ percentage }}/100</span>\n            </template>\n          </el-progress>\n        </div>\n      </div>\n\n      <div class=\"analysis-section\">\n        <h4>发现的瓶颈</h4>\n        <div v-if=\"analysisDialogData.bottlenecks.length > 0\">\n          <el-alert\n            v-for=\"(bottleneck, index) in analysisDialogData.bottlenecks\"\n            :key=\"index\"\n            :title=\"bottleneck.type\"\n            :description=\"bottleneck.description\"\n            :type=\"getSeverityType(bottleneck.severity)\"\n            show-icon\n            style=\"margin-bottom: 10px;\"\n          />\n        </div>\n        <el-empty v-else description=\"未发现明显性能瓶颈\" />\n      </div>\n\n      <div class=\"analysis-section\">\n        <h4>优化建议</h4>\n        <div v-if=\"analysisDialogData.recommendations.length > 0\">\n          <ul class=\"recommendations-list\">\n            <li\n              v-for=\"(recommendation, index) in analysisDialogData.recommendations\"\n              :key=\"index\"\n              class=\"recommendation-item\"\n            >\n              <el-icon class=\"recommendation-icon\"><Check /></el-icon>\n              {{ recommendation }}\n            </li>\n          </ul>\n        </div>\n        <el-empty v-else description=\"暂无优化建议\" />\n      </div>\n    </div>\n  </el-dialog>\n\n  <!-- 测试配置详情对话框 -->\n  <el-dialog\n    v-model=\"configDialogData.visible\"\n    title=\"压测配置详情\"\n    width=\"80%\"\n    :destroy-on-close=\"true\"\n  >\n    <div class=\"config-dialog-content\" v-if=\"configDialogData.taskConfig\">\n      <!-- 基础配置 -->\n      <el-card class=\"config-section\" shadow=\"never\">\n        <template #header>\n          <div class=\"config-section-title\">\n            <el-icon><Setting /></el-icon>\n            基础配置\n          </div>\n        </template>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"config-item\">\n              <span class=\"config-label\">任务名称：</span>\n              <span class=\"config-value\">{{ configDialogData.taskConfig.taskName || '未设置' }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"config-item\">\n              <span class=\"config-label\">任务类型：</span>\n              <span class=\"config-value\">{{ getTaskTypeText(configDialogData.taskConfig.taskType) }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"config-item\">\n              <span class=\"config-label\">压测模式：</span>\n              <span class=\"config-value\">{{ getRunPatternText(configDialogData.taskConfig.runPattern) }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"config-item\">\n              <span class=\"config-label\">控制模式：</span>\n              <span class=\"config-value\">{{ getDistributedModeText(configDialogData.taskConfig.distributed_mode) }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"config-item\">\n              <span class=\"config-label\">创建人：</span>\n              <span class=\"config-value\">{{ configDialogData.taskConfig.creator || '未知' }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"config-item\">\n              <span class=\"config-label\">创建时间：</span>\n              <span class=\"config-value\">{{ formatTime(configDialogData.taskConfig.create_time) }}</span>\n            </div>\n          </el-col>\n        </el-row>\n      </el-card>\n\n      <!-- 性能参数 -->\n      <el-card class=\"config-section\" shadow=\"never\">\n        <template #header>\n          <div class=\"config-section-title\">\n            <el-icon><DataAnalysis /></el-icon>\n            性能参数\n          </div>\n        </template>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"config-item\">\n              <span class=\"config-label\">并发用户数：</span>\n              <span class=\"config-value\">{{ configDialogData.taskConfig.concurrent_users || configDialogData.taskConfig.users || '未设置' }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"config-item\">\n              <span class=\"config-label\">持续时间：</span>\n              <span class=\"config-value\">{{ configDialogData.taskConfig.duration || '未设置' }}秒</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"config-item\">\n              <span class=\"config-label\">爬坡时间：</span>\n              <span class=\"config-value\">{{ configDialogData.taskConfig.spawn_rate || configDialogData.taskConfig.ramp_up || '未设置' }}秒</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"config-item\">\n              <span class=\"config-label\">QPS限制：</span>\n              <span class=\"config-value\">{{ configDialogData.taskConfig.qps_limit || '无限制' }}</span>\n            </div>\n          </el-col>\n        </el-row>\n      </el-card>\n\n      <!-- 环境配置 -->\n      <el-card class=\"config-section\" shadow=\"never\" v-if=\"configDialogData.taskConfig.environment\">\n        <template #header>\n          <div class=\"config-section-title\">\n            <el-icon><Monitor /></el-icon>\n            环境配置\n          </div>\n        </template>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"config-item\">\n              <span class=\"config-label\">环境名称：</span>\n              <span class=\"config-value\">{{ configDialogData.taskConfig.environment.name || '默认环境' }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"config-item\">\n              <span class=\"config-label\">基础URL：</span>\n              <span class=\"config-value\">{{ configDialogData.taskConfig.environment.base_url || configDialogData.taskConfig.environment.host || '未设置' }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"config-item\">\n              <span class=\"config-label\">协议：</span>\n              <span class=\"config-value\">{{ configDialogData.taskConfig.environment.protocol || 'HTTP' }}</span>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"config-item\">\n              <span class=\"config-label\">超时设置：</span>\n              <span class=\"config-value\">{{ configDialogData.taskConfig.environment.timeout || configDialogData.taskConfig.timeout || '30' }}秒</span>\n            </div>\n          </el-col>\n        </el-row>\n      </el-card>\n    </div>\n  </el-dialog>\n\n  <!-- 通知配置对话框 -->\n  <el-dialog \n    v-model=\"notificationDialogData.visible\" \n    title=\"测试报告推送\" \n    width=\"680px\" \n    custom-class=\"modern-notification-dialog\" \n    :required=\"true\" \n    :before-close=\"cancelNotification\"\n    top=\"5vh\"\n    center\n    append-to-body\n    destroy-on-close\n  >\n    <div class=\"notification-dialog-content\">\n      <div class=\"notification-dialog-header\">\n        <div class=\"notification-title\">\n          <el-icon class=\"notification-icon\"><Bell /></el-icon>\n          <span>分享测试报告结果</span>\n        </div>\n        <div class=\"notification-subtitle\">选择通知方式，将测试结果分享给团队成员</div>\n      </div>\n      \n      <el-form :model=\"notificationDialogData\" :rules=\"notificationRules\" ref=\"notificationFormRef\" label-position=\"top\" class=\"notification-form\">\n        <el-form-item prop=\"name\" label=\"通知标题\">\n          <el-input \n            v-model=\"notificationDialogData.name\" \n            maxlength=\"50\" \n            minlength=\"1\" \n            placeholder=\"例如：性能测试报告 - 系统优化后结果\"\n            class=\"notification-input\"\n          />\n      </el-form-item>\n      \n        <el-form-item prop=\"pushType\" label=\"选择通知方式\">\n          <div class=\"notification-type-selector\">\n          <div \n              class=\"notification-type-option\" \n              :class=\"{ 'selected': notificationDialogData.pushType === 'email' }\"\n            @click=\"notificationDialogData.pushType = 'email'\"\n          >\n              <div class=\"option-icon email-icon\"><el-icon><Message /></el-icon></div>\n              <span>邮件</span>\n          </div>\n            \n          <div \n              class=\"notification-type-option\" \n              :class=\"{ 'selected': notificationDialogData.pushType === 'webhook' }\"\n            @click=\"notificationDialogData.pushType = 'webhook'\"\n          >\n              <div class=\"option-icon webhook-icon\"><el-icon><Link /></el-icon></div>\n              <span>Webhook</span>\n          </div>\n            \n          <div \n              class=\"notification-type-option\" \n              :class=\"{ 'selected': notificationDialogData.pushType === 'dingtalk' }\"\n            @click=\"notificationDialogData.pushType = 'dingtalk'\"\n          >\n              <div class=\"option-icon dingtalk-icon\"><el-icon><ChatDotRound /></el-icon></div>\n              <span>钉钉</span>\n          </div>\n            \n          <div \n              class=\"notification-type-option\" \n              :class=\"{ 'selected': notificationDialogData.pushType === 'wechat' }\"\n            @click=\"notificationDialogData.pushType = 'wechat'\"\n          >\n              <div class=\"option-icon wechat-icon\"><el-icon><ChatRound /></el-icon></div>\n              <span>微信</span>\n          </div>\n        </div>\n      </el-form-item>\n      \n        <el-form-item prop=\"webhook\" label=\"推送地址\" v-if=\"notificationDialogData.pushType && notificationDialogData.pushType !== 'email'\">\n          <el-input \n            v-model=\"notificationDialogData.webhook\" \n            minlength=\"3\" \n            placeholder=\"请输入webhook地址\" \n            class=\"notification-input\"\n          >\n          <template #prefix>\n            <el-icon v-if=\"notificationDialogData.pushType === 'webhook'\"><Link /></el-icon>\n            <el-icon v-if=\"notificationDialogData.pushType === 'dingtalk'\"><ChatDotRound /></el-icon>\n            <el-icon v-if=\"notificationDialogData.pushType === 'wechat'\"><ChatRound /></el-icon>\n          </template>\n        </el-input>\n      </el-form-item>\n      \n      <el-form-item prop=\"recipients\" label=\"接收人\" v-if=\"notificationDialogData.pushType === 'email'\">\n          <el-select \n            multiple \n            v-model=\"notificationDialogData.recipients\" \n            placeholder=\"请选择接收人\" \n            style=\"width: 100%;\" \n            collapse-tags \n            collapse-tags-tooltip\n            class=\"notification-select\"\n          >\n          <el-option :label=\"'@all'\" :value=\"'@all'\" :key=\"'@all'\" :disabled=\"notificationDialogData.recipients.length > 0 && !notificationDialogData.recipients.includes('@all')\"></el-option>\n          <el-option \n            label=\"<EMAIL>\" \n            value=\"<EMAIL>\" \n            :disabled=\"notificationDialogData.recipients.includes('@all')\"\n          ></el-option>\n        </el-select>\n          <div class=\"notification-tip\">选择 @all 将通知所有人，与其他接收人互斥</div>\n      </el-form-item>\n\n        <div class=\"notification-report-preview\">\n          <div class=\"preview-header\">\n            <el-icon><Document /></el-icon>\n            <span>报告摘要</span>\n          </div>\n          <div class=\"preview-content\">\n            <div class=\"preview-item\">\n              <span class=\"item-label\">报告名称:</span>\n              <span class=\"item-value\">{{ reportData.reportName }}</span>\n            </div>\n            <div class=\"preview-item\">\n              <span class=\"item-label\">测试状态:</span>\n              <span class=\"item-value\">\n                <el-button :type=\"reportData.reportStatus === '0' ? 'success' : 'info'\" size=\"small\">\n              {{ getStatusText(reportData.reportStatus) }}\n                </el-button>\n              </span>\n        </div>\n            <div class=\"metrics-section\">\n              <div class=\"metrics-row\">\n                <div class=\"metric-item-wide\">\n                  <div class=\"metric-label\">响应时间</div>\n                  <div class=\"metric-value\">{{ formatResponseTime(reportData.avgResponseTime) }}</div>\n                </div>\n                <div class=\"metric-item-wide\">\n                  <div class=\"metric-label\">成功率</div>\n                  <div class=\"metric-value\">{{ getSuccessRate() }}%</div>\n                </div>\n              </div>\n              <div class=\"metrics-row\">\n                <div class=\"metric-item-wide\">\n                  <div class=\"metric-label\">平均TPS</div>\n                  <div class=\"metric-value\">{{ (reportData.avgTps || 0).toFixed(2) }}</div>\n                </div>\n                <div class=\"metric-item-wide\">\n                  <div class=\"metric-label\">并发用户</div>\n                  <div class=\"metric-value\">{{ reportData.maxUsers || reportData.avgUsers || '未设置' }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n    </el-form>\n    </div>\n    \n    <template #footer>\n      <div class=\"notification-dialog-footer\">\n        <el-button @click=\"cancelNotification\" plain>取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmSendNotification\">\n          <el-icon><Position /></el-icon> 发送通知\n        </el-button>\n      </div>\n    </template>\n  </el-dialog>\n\n  <!-- 创建基准线对话框 -->\n  <el-dialog\n    v-model=\"baselineDialogData.visible\"\n    :title=\"'创建性能基准线'\"\n    width=\"600px\"\n    destroy-on-close>\n    \n    <el-form :model=\"baselineDialogData.form\" :rules=\"baselineFormRules\" ref=\"baselineFormRef\" label-width=\"120px\">\n      <el-form-item label=\"基准线名称\" prop=\"name\">\n        <el-input v-model=\"baselineDialogData.form.name\" placeholder=\"请输入基准线名称\"></el-input>\n      </el-form-item>\n      \n      <el-form-item label=\"描述\" prop=\"description\">\n        <el-input\n          v-model=\"baselineDialogData.form.description\"\n          type=\"textarea\"\n          :rows=\"3\"\n          placeholder=\"请输入基准线描述\">\n        </el-input>\n      </el-form-item>\n      \n      <el-divider content-position=\"center\">性能指标</el-divider>\n      \n      <el-row :gutter=\"20\">\n        <el-col :span=\"12\">\n          <el-form-item label=\"响应时间(ms)\" prop=\"avg_response_time\">\n            <el-input-number\n              v-model=\"baselineDialogData.form.avg_response_time\"\n              :precision=\"2\"\n              :min=\"0\"\n              style=\"width: 100%\">\n            </el-input-number>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"TPS\" prop=\"avg_tps\">\n            <el-input-number\n              v-model=\"baselineDialogData.form.avg_tps\"\n              :precision=\"2\"\n              :min=\"0\"\n              style=\"width: 100%\">\n            </el-input-number>\n          </el-form-item>\n        </el-col>\n      </el-row>\n      \n      <el-row :gutter=\"20\">\n        <el-col :span=\"12\">\n          <el-form-item label=\"成功率(%)\" prop=\"success_rate\">\n            <el-input-number\n              v-model=\"baselineDialogData.form.success_rate\"\n              :precision=\"2\"\n              :min=\"0\"\n              :max=\"100\"\n              style=\"width: 100%\">\n            </el-input-number>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"CPU使用率(%)\" prop=\"avg_cpu\">\n            <el-input-number\n              v-model=\"baselineDialogData.form.avg_cpu\"\n              :precision=\"2\"\n              :min=\"0\"\n              :max=\"100\"\n              style=\"width: 100%\">\n            </el-input-number>\n          </el-form-item>\n        </el-col>\n      </el-row>\n      \n      <el-row :gutter=\"20\">\n        <el-col :span=\"12\">\n          <el-form-item label=\"内存使用率(%)\" prop=\"avg_memory\">\n            <el-input-number\n              v-model=\"baselineDialogData.form.avg_memory\"\n              :precision=\"2\"\n              :min=\"0\"\n              :max=\"100\"\n              style=\"width: 100%\">\n            </el-input-number>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"是否激活\">\n            <el-switch v-model=\"baselineDialogData.form.is_active\"></el-switch>\n          </el-form-item>\n        </el-col>\n      </el-row>\n      \n      <div class=\"baseline-info-hint\">\n        <el-icon><InfoFilled /></el-icon>\n        <span>这些性能指标将作为未来性能测试的参考基准</span>\n      </div>\n    </el-form>\n    \n    <template #footer>\n      <span class=\"dialog-footer\">\n        <el-button @click=\"baselineDialogData.visible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitBaselineForm\" :loading=\"baselineDialogData.loading\">创建</el-button>\n      </span>\n    </template>\n  </el-dialog>\n  \n  <!-- 基准线选择对话框 -->\n  <el-dialog\n    v-model=\"baselineCompareDialogData.visible\"\n    title=\"选择要对比的基准线\"\n    width=\"500px\"\n    destroy-on-close>\n    \n    <el-form label-width=\"100px\">\n      <el-form-item label=\"基准线\">\n        <el-select v-model=\"baselineCompareDialogData.selectedBaselineId\" style=\"width: 100%\">\n          <el-option\n            v-for=\"baseline in baselineCompareDialogData.baselineList\"\n            :key=\"baseline.id\"\n            :label=\"baseline.name\"\n            :value=\"baseline.id\">\n            <div style=\"display: flex; justify-content: space-between; align-items: center\">\n              <span>{{ baseline.name }}</span>\n              <span style=\"font-size: 12px; color: #999\">{{ formatTime(baseline.create_time) }}</span>\n            </div>\n          </el-option>\n        </el-select>\n      </el-form-item>\n    </el-form>\n    \n    <template #footer>\n      <span class=\"dialog-footer\">\n        <el-button @click=\"baselineCompareDialogData.visible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitCompareBaseline\" :loading=\"baselineCompareDialogData.loading\">开始对比</el-button>\n      </span>\n    </template>\n  </el-dialog>\n  \n  <!-- 基准线对比结果对话框 -->\n  <el-dialog\n    v-model=\"baselineCompareResultData.visible\"\n    title=\"性能基准线对比结果\"\n    width=\"800px\"\n    destroy-on-close>\n    \n    <div class=\"comparison-container\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"12\">\n          <div class=\"comparison-card baseline\">\n            <div class=\"comparison-header\">\n              <el-icon><TrendCharts /></el-icon>\n              <span>基准线</span>\n            </div>\n            <div class=\"comparison-metrics\">\n              <div class=\"metric-row\">\n                <span class=\"metric-label\">响应时间:</span>\n                <span class=\"metric-value\">{{ baselineCompareResultData.baseline_metrics.avg_response_time.toFixed(2) }}ms</span>\n              </div>\n              <div class=\"metric-row\">\n                <span class=\"metric-label\">TPS:</span>\n                <span class=\"metric-value\">{{ baselineCompareResultData.baseline_metrics.avg_tps.toFixed(2) }}</span>\n              </div>\n              <div class=\"metric-row\">\n                <span class=\"metric-label\">成功率:</span>\n                <span class=\"metric-value\">{{ baselineCompareResultData.baseline_metrics.success_rate.toFixed(2) }}%</span>\n              </div>\n              <div class=\"metric-row\">\n                <span class=\"metric-label\">CPU使用率:</span>\n                <span class=\"metric-value\">{{ baselineCompareResultData.baseline_metrics.avg_cpu?.toFixed(2) || '0.00' }}%</span>\n              </div>\n              <div class=\"metric-row\">\n                <span class=\"metric-label\">内存使用率:</span>\n                <span class=\"metric-value\">{{ baselineCompareResultData.baseline_metrics.avg_memory?.toFixed(2) || '0.00' }}%</span>\n              </div>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"12\">\n          <div class=\"comparison-card current\">\n            <div class=\"comparison-header\">\n              <el-icon><Document /></el-icon>\n              <span>当前报告</span>\n            </div>\n            <div class=\"comparison-metrics\">\n              <div class=\"metric-row\">\n                <span class=\"metric-label\">响应时间:</span>\n                <span class=\"metric-value\">{{ baselineCompareResultData.current_metrics.avg_response_time.toFixed(2) }}ms</span>\n                <span :class=\"getComparisonClass(\n                  baselineCompareResultData.current_metrics.avg_response_time,\n                  baselineCompareResultData.baseline_metrics.avg_response_time,\n                  'response_time'\n                )\">\n                  {{ getComparisonText(\n                    baselineCompareResultData.current_metrics.avg_response_time,\n                    baselineCompareResultData.baseline_metrics.avg_response_time,\n                    'response_time'\n                  ) }}\n                </span>\n              </div>\n              <div class=\"metric-row\">\n                <span class=\"metric-label\">TPS:</span>\n                <span class=\"metric-value\">{{ baselineCompareResultData.current_metrics.avg_tps.toFixed(2) }}</span>\n                <span :class=\"getComparisonClass(\n                  baselineCompareResultData.current_metrics.avg_tps,\n                  baselineCompareResultData.baseline_metrics.avg_tps,\n                  'tps'\n                )\">\n                  {{ getComparisonText(\n                    baselineCompareResultData.current_metrics.avg_tps,\n                    baselineCompareResultData.baseline_metrics.avg_tps,\n                    'tps'\n                  ) }}\n                </span>\n              </div>\n              <div class=\"metric-row\">\n                <span class=\"metric-label\">成功率:</span>\n                <span class=\"metric-value\">{{ baselineCompareResultData.current_metrics.success_rate.toFixed(2) }}%</span>\n                <span :class=\"getComparisonClass(\n                  baselineCompareResultData.current_metrics.success_rate,\n                  baselineCompareResultData.baseline_metrics.success_rate,\n                  'success_rate'\n                )\">\n                  {{ getComparisonText(\n                    baselineCompareResultData.current_metrics.success_rate,\n                    baselineCompareResultData.baseline_metrics.success_rate,\n                    'success_rate'\n                  ) }}\n                </span>\n              </div>\n              <div class=\"metric-row\">\n                <span class=\"metric-label\">CPU使用率:</span>\n                <span class=\"metric-value\">{{ baselineCompareResultData.current_metrics.avg_cpu?.toFixed(2) || '0.00' }}%</span>\n                <span :class=\"getComparisonClass(\n                  baselineCompareResultData.current_metrics.avg_cpu || 0,\n                  baselineCompareResultData.baseline_metrics.avg_cpu || 0,\n                  'cpu'\n                )\">\n                  {{ getComparisonText(\n                    baselineCompareResultData.current_metrics.avg_cpu || 0,\n                    baselineCompareResultData.baseline_metrics.avg_cpu || 0,\n                    'cpu'\n                  ) }}\n                </span>\n              </div>\n              <div class=\"metric-row\">\n                <span class=\"metric-label\">内存使用率:</span>\n                <span class=\"metric-value\">{{ baselineCompareResultData.current_metrics.avg_memory?.toFixed(2) || '0.00' }}%</span>\n                <span :class=\"getComparisonClass(\n                  baselineCompareResultData.current_metrics.avg_memory || 0,\n                  baselineCompareResultData.baseline_metrics.avg_memory || 0,\n                  'memory'\n                )\">\n                  {{ getComparisonText(\n                    baselineCompareResultData.current_metrics.avg_memory || 0,\n                    baselineCompareResultData.baseline_metrics.avg_memory || 0,\n                    'memory'\n                  ) }}\n                </span>\n              </div>\n            </div>\n          </div>\n        </el-col>\n      </el-row>\n      \n      <!-- 对比结论 -->\n      <div class=\"comparison-conclusion\">\n        <div class=\"conclusion-header\">\n          <el-icon><InfoFilled /></el-icon>\n          <span>对比结论</span>\n        </div>\n        <div class=\"conclusion-content\">\n          <p v-if=\"baselineCompareResultData.conclusion\">{{ baselineCompareResultData.conclusion }}</p>\n          <p v-else>{{ generateComparisonConclusion() }}</p>\n        </div>\n      </div>\n    </div>\n    \n    <template #footer>\n      <span class=\"dialog-footer\">\n        <el-button @click=\"baselineCompareResultData.visible = false\">关闭</el-button>\n        <el-button type=\"primary\" @click=\"exportComparisonResult\">导出对比报告</el-button>\n      </span>\n    </template>\n  </el-dialog>\n</template>\n\n<script>\nimport { Icon } from '@iconify/vue'\nimport ResponseTimeChart from '@/components/echart/ResponseTimeChart.vue'\nimport {\n  Edit,\n  View,\n  SwitchButton,\n  InfoFilled,\n  TrendCharts,\n  Search,\n  ArrowDown,\n  ArrowLeft,\n  Check,\n  Download,\n  Document,\n  Monitor,\n  DocumentCopy,\n  Files,\n  Bell,\n  Message,\n  Link,\n  ChatDotRound,\n  ChatRound,\n  MoreFilled,\n  DataAnalysis,\n  PieChart,\n  Setting,\n  // 添加新的图标组件\n  Connection,\n  CircleClose\n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'PerformanceResult-Detail',\n  components: {\n    Icon,\n    ResponseTimeChart,\n    Edit,\n    View,\n    SwitchButton,\n    InfoFilled,\n    TrendCharts,\n    Search,\n    ArrowDown,\n    ArrowLeft,\n    Check,\n    Download,\n    Document,\n    Monitor,\n    DocumentCopy,\n    Files,\n    Bell,\n    Message,\n    Link,\n    ChatDotRound,\n    ChatRound,\n    MoreFilled,\n    DataAnalysis,\n    PieChart,\n    Setting,\n    // 添加新的图标组件\n    Connection,\n    CircleClose\n  },\n  data() {\n    return {\n      reportId: null,\n      selectedKeys: [],\n      reportData: {},\n      taskName: '',\n      desc: '',\n      taskType: '',\n      inputDlg: false,\n      editingField: '',\n      tempTaskName: '', // 用于编辑任务名称时的临时存储\n      activeIndex: '1',\n      textResult: '',\n      reportList: [],\n      xData: [],\n      seriesData: [],\n      realTimeData: {},\n      monitoringData: {},\n      logData: [],\n      // 基准线对话框数据\n      baselineDialogData: {\n        visible: false,\n        loading: false,\n        mode: 'create',\n        form: {\n          name: '',\n          description: '',\n          task_id: null,\n          avg_response_time: 0,\n          avg_tps: 0,\n          success_rate: 0,\n          avg_cpu: 0,\n          avg_memory: 0,\n          is_active: true,\n          project_id: null\n        }\n      },\n      // 基准线表单验证规则\n      baselineFormRules: {\n        name: [\n          { required: true, message: '请输入基准线名称', trigger: 'blur' },\n          { min: 2, max: 100, message: '长度在2到100个字符', trigger: 'blur' }\n        ],\n        task_id: [\n          { required: true, message: '请选择关联任务', trigger: 'change' }\n        ]\n      },\n      // 基准线对比对话框数据\n      baselineCompareDialogData: {\n        visible: false,\n        loading: false,\n        baselineList: [],\n        selectedBaselineId: null\n      },\n      // 基准线对比结果数据\n      baselineCompareResultData: {\n        visible: false,\n        baseline_metrics: {\n          avg_response_time: 0,\n          avg_tps: 0,\n          success_rate: 0,\n          avg_cpu: 0,\n          avg_memory: 0\n        },\n        current_metrics: {\n          avg_response_time: 0,\n          avg_tps: 0,\n          success_rate: 0,\n          avg_cpu: 0,\n          avg_memory: 0\n        },\n        conclusion: ''\n      },\n      // 添加新的日志相关数据\n      logCategories: {\n        all: '全部',\n        system: '系统',\n        request: '请求',\n        error: '错误',\n        performance: '性能',\n        event: '事件'\n      },\n      logCategory: 'all',\n      // WebSocket相关数据\n      wsConnection: null,\n      wsReconnectAttempts: 0,\n      wsMaxReconnectAttempts: 5,\n      wsReconnectInterval: null,\n      wsConnectionStatus: 'disconnected', // disconnected, connecting, connected, error\n      autoRefresh: true,\n      refreshInterval: null,\n      // 添加缺失的数据属性\n      targetServiceData: {},\n      logFilter: '',\n      logLevel: 'all',\n      tableLoading: false, // 添加loading状态\n      chartUpdateKey: 0, // 用于强制更新图表\n      // 监控数据加载标志\n      monitoringDataLoaded: false,\n      targetServiceDataLoaded: false,\n      logDataLoaded: false,\n      // 监控数据刷新定时器\n      monitoringRefreshInterval: null,\n      targetServiceRefreshInterval: null,\n      // 分离的图表数据\n      responseTimeData: [],\n      rpsData: [],\n      tpsData: [],\n      usersData: [],\n      p50Data: [], // 添加p50数据数组\n      p90Data: [],\n      p95Data: [], // 添加p95数据数组\n      p99Data: [],\n      errorRateData: [],\n      // 多接口图表数据\n      interfaceChartData: {\n        responseTime: {},\n        rps: {},\n        tps: {},\n        users: {},\n        p50: {}, // 添加p50接口数据\n        p90: {},\n        p95: {}, // 添加p95接口数据\n        p99: {},\n        errorRate: {}\n      },\n      availableInterfaces: [], // 可用的接口列表\n      realTimeUpdateInterval: null, // 实时更新定时器\n      // 仅查看功能相关数据\n      isFilterMode: false, // 是否处于筛选模式\n      selectedRows: [], // 选中的行数据\n      // GUI相关数据\n      guiUrl: '',  // 由后端提供的完整GUI URL\n      hasGuiUrlLoaded: false,\n      iframeError: false,\n      iframeErrorMessage: '',\n\n      // 对话框数据\n      analysisDialogData: {\n        visible: false,\n        performance_score: 0,\n        bottlenecks: [],\n        recommendations: []\n      },\n      configDialogData: {\n        visible: false,\n        taskConfig: null\n      },\n      notificationDialogData: {\n        visible: false,\n        type: '',\n        pushType: 'wechat',\n        name: '',\n        webhook: '',\n        recipients: [],\n      },\n      // 添加验证规则\n      notificationRules: {\n        name: [\n          { required: true, message: '请输入推送名称', trigger: 'blur' },\n          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }\n        ],\n        pushType: [\n          { required: true, message: '请选择推送类型', trigger: 'change' }\n        ],\n        webhook: [\n          { required: true, message: '请输入webhook地址', trigger: 'blur' },\n          { min: 3, message: 'webhook地址至少3个字符', trigger: 'blur' }\n        ],\n        recipients: [\n          { required: true, message: '请选择接收人', trigger: 'change' }\n        ]\n      },\n      monitorUrl: '', // 新增：输入框内容\n      monitorIframeUrl: '', // 新增：iframe展示url\n    }\n  },\n  computed: {\n    // 测试是否正在运行\n    isTestRunning() {\n      return this.reportData.reportStatus === '1' || this.taskType === '运行中';\n    },\n\n    isEdit(){\n      return true  // 始终允许编辑结果分析\n    },\n    // 过滤日志\n    filteredLogs() {\n      let logs = this.logData\n\n      // 按日志级别过滤\n      if (this.logLevel && this.logLevel !== 'all') {\n        logs = logs.filter(log => log.level === this.logLevel)\n      }\n\n      // 按日志类别过滤\n      if (this.logCategory && this.logCategory !== 'all') {\n        logs = logs.filter(log => log.category === this.logCategory)\n      }\n\n      // 按关键词过滤\n      if (this.logFilter) {\n        logs = logs.filter(log =>\n          log.message.toLowerCase().includes(this.logFilter.toLowerCase()) ||\n          (log.details && log.details.toLowerCase().includes(this.logFilter.toLowerCase()))\n        )\n      }\n\n      // 按时间正序排列，旧的在上面，新的在下面\n      return logs.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))\n    },\n\n    // 响应时间图表数据\n    responseTimeSeriesData() {\n      const seriesData = [];\n      if (this.isFilterMode && this.selectedRows.length > 0) {\n        this.selectedKeys = this.selectedRows.map(row => row.method + '_' + row.name);\n      }Object.keys(this.interfaceChartData.responseTime).forEach(interfaceKey => {\n        if (this.isFilterMode && this.selectedKeys && !this.selectedKeys.includes(interfaceKey)) {\n          return;\n        }\n        if (this.interfaceChartData.responseTime[interfaceKey].length > 0) {\n          seriesData.push({\n            name: interfaceKey,\n            values: this.interfaceChartData.responseTime[interfaceKey]\n          });\n        }\n      });\n      return seriesData.length > 0 ? seriesData : [{ name: '平均响应时间', values: [] }];\n    },\n\n    // RPS图表数据\n    rpsSeriesData() {\n      const seriesData = [];\n      if (this.isFilterMode && this.selectedRows.length > 0) {\n        this.selectedKeys = this.selectedRows.map(row => row.method + '_' + row.name);\n      }\n\n      Object.keys(this.interfaceChartData.rps).forEach(interfaceKey => {\n        if (this.isFilterMode && this.selectedKeys && !this.selectedKeys.includes(interfaceKey)) {\n          return;\n        }\n        if (this.interfaceChartData.rps[interfaceKey].length > 0) {\n          seriesData.push({\n            name: interfaceKey,\n            values: this.interfaceChartData.rps[interfaceKey]\n          });\n        }\n      });\n      if (seriesData.length === 0 && this.rpsData && this.rpsData.length > 0) {\n        seriesData.push({\n          name: '总体RPS',\n          values: this.rpsData\n        });\n      }\n      return seriesData.length > 0 ? seriesData : [{ name: 'RPS', values: [] }];\n    },\n\n    // TPS图表数据\n    tpsSeriesData() {\n      const seriesData = [];\n      if (this.isFilterMode && this.selectedRows.length > 0) {\n        this.selectedKeys = this.selectedRows.map(row => row.method + '_' + row.name);\n      }\n      Object.keys(this.interfaceChartData.tps).forEach(interfaceKey => {\n        if (this.isFilterMode && this.selectedKeys && !this.selectedKeys.includes(interfaceKey)) {\n          return;\n        }\n        if (this.interfaceChartData.tps[interfaceKey].length > 0) {\n          seriesData.push({\n            name: interfaceKey,\n            values: this.interfaceChartData.tps[interfaceKey]\n          });\n        }\n      });\n      if (seriesData.length === 0 && this.tpsData && this.tpsData.length > 0) {\n        seriesData.push({\n          name: '总体TPS',\n          values: this.tpsData\n        });\n      }\n      return seriesData.length > 0 ? seriesData : [{ name: 'TPS', values: [] }];\n    },\n\n    // 用户数图表数据\n    usersSeriesData() {\n      const seriesData = [];\n      // 用户数只显示总体数据（因为用户数是全局的）\n      if (this.usersData.length > 0) {\n        seriesData.push({\n          name: '并发用户数',\n          values: this.usersData\n        });\n      }\n      return seriesData.length > 0 ? seriesData : [{ name: '并发用户数', values: [] }];\n    },\n\n    // P50响应时间图表数据\n    p50SeriesData() {\n      const seriesData = [];\n      if (this.isFilterMode && this.selectedRows.length > 0) {\n        this.selectedKeys = this.selectedRows.map(row => row.method + '_' + row.name);\n      }\n      Object.keys(this.interfaceChartData.p50).forEach(interfaceKey => {\n        if (this.isFilterMode && this.selectedKeys && !this.selectedKeys.includes(interfaceKey)) {\n          return;\n        }\n        if (this.interfaceChartData.p50[interfaceKey].length > 0) {\n          seriesData.push({\n            name: interfaceKey,\n            values: this.interfaceChartData.p50[interfaceKey]\n          });\n        }\n      });\n      return seriesData.length > 0 ? seriesData : [{ name: '50%响应时间线', values: [] }];\n    },\n\n    // P90响应时间图表数据\n    p90SeriesData() {\n      const seriesData = [];\n      if (this.isFilterMode && this.selectedRows.length > 0) {\n        this.selectedKeys = this.selectedRows.map(row => row.method + '_' + row.name);\n      }\n      Object.keys(this.interfaceChartData.p90).forEach(interfaceKey => {if (this.isFilterMode && this.selectedKeys && !this.selectedKeys.includes(interfaceKey)) {\n          return;\n        }\n        if (this.interfaceChartData.p90[interfaceKey].length > 0) {\n          seriesData.push({\n            name: interfaceKey,\n            values: this.interfaceChartData.p90[interfaceKey]\n          });\n        }\n      });\n      return seriesData.length > 0 ? seriesData : [{ name: '90%响应时间线', values: [] }];\n    },\n\n    // P95响应时间图表数据\n    p95SeriesData() {\n      const seriesData = [];\n      if (this.isFilterMode && this.selectedRows.length > 0) {\n        this.selectedKeys = this.selectedRows.map(row => row.method + '_' + row.name);\n      }\n      Object.keys(this.interfaceChartData.p95).forEach(interfaceKey => {\n        if (this.isFilterMode && this.selectedKeys && !this.selectedKeys.includes(interfaceKey)) {\n          return;\n        }\n        if (this.interfaceChartData.p95[interfaceKey].length > 0) {\n          seriesData.push({\n            name: interfaceKey,\n            values: this.interfaceChartData.p95[interfaceKey]\n          });\n        }\n      });\n      return seriesData.length > 0 ? seriesData : [{ name: '95%响应时间线', values: [] }];\n    },\n\n    // P99响应时间图表数据\n    p99SeriesData() {\n      const seriesData = [];\n      if (this.isFilterMode && this.selectedRows.length > 0) {\n        this.selectedKeys = this.selectedRows.map(row => row.method + '_' + row.name);\n      }\n      Object.keys(this.interfaceChartData.p99).forEach(interfaceKey => {\n        if (this.isFilterMode && this.selectedKeys && !this.selectedKeys.includes(interfaceKey)) {\n          return;\n        }\n        if (this.interfaceChartData.p99[interfaceKey].length > 0) {\n          seriesData.push({\n            name: interfaceKey,\n            values: this.interfaceChartData.p99[interfaceKey]\n          });\n        }\n      });\n      return seriesData.length > 0 ? seriesData : [{ name: '99%响应时间线', values: [] }];\n    },\n\n    // 筛选后的指标数据\n    filteredMetrics() {\n      const allMetrics = this.getDetailedMetrics();\n\n      if (!this.isFilterMode || this.selectedRows.length === 0) {\n        // 如果不在筛选模式或没有选中任何行，返回所有数据\n        return allMetrics;\n      }\n\n      // 获取选中行的接口名称\n      const selectedNames = this.selectedRows.map(row => row.name);\n\n      // 保留总体数据和选中的接口数据\n      const filteredData = allMetrics.filter(metric =>\n        metric.method === 'ALL' || selectedNames.includes(metric.name)\n      );\n      \n      // 确保总体数据始终显示在第一行\n      const totalData = filteredData.filter(metric => metric.method === 'ALL');\n      const detailData = filteredData.filter(metric => metric.method !== 'ALL');\n      \n      return [...totalData, ...detailData];\n    }\n  },\n\n  methods: {\n    // 获取成功率\n    getSuccessRate() {\n      if (!this.reportData || !this.reportData.totalRequests || this.reportData.totalRequests <= 0) {\n        return 0;\n      }\n      const successRequests = this.reportData.successRequests || 0;\n      return Math.round((successRequests / this.reportData.totalRequests) * 100);\n    },\n\n    // 初始化WebSocket连接\n    initWebSocket() {\n      // 清理现有连接\n      this.cleanupWebSocket()\n\n      // 检查是否需要WebSocket连接\n      if (!this.reportId || this.reportData.reportStatus !== '1') {\n        return\n      }\n\n      this.wsConnectionStatus = 'connecting'\n\n      try {\n        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'\n        const baseUrl = process.env.VUE_APP_WS_BASE_URL || window.location.host\n        const wsUrl = `${protocol}//${baseUrl}/ws/performance/report/${this.reportId}/`\n\n        this.wsConnection = new WebSocket(wsUrl)\n\n        // 设置连接超时\n        const connectionTimeout = setTimeout(() => {\n          if (this.wsConnection && this.wsConnection.readyState === WebSocket.CONNECTING) {\n            this.wsConnection.close()\n            this.handleWebSocketError('连接超时')\n          }\n        }, 10000) // 10秒超时\n\n        this.wsConnection.onopen = () => {\n          clearTimeout(connectionTimeout)\n          this.wsConnectionStatus = 'connected'\n          this.wsReconnectAttempts = 0\n\n          // 发送心跳检测\n          this.startHeartbeat()\n\n          this.$message.success('实时监控已启动')\n        }\n\n        this.wsConnection.onmessage = (event) => {\n          try {\n            const data = JSON.parse(event.data)\n            this.handleWebSocketMessage(data)\n          } catch (error) {\n            console.error('解析WebSocket消息失败:', error)\n          }\n        }\n\n        this.wsConnection.onerror = (error) => {\n          clearTimeout(connectionTimeout)\n          console.error('WebSocket连接错误:', error)\n          this.handleWebSocketError('连接错误')\n        }\n\n        this.wsConnection.onclose = (event) => {\n          clearTimeout(connectionTimeout)\n          this.wsConnectionStatus = 'disconnected'\n          this.stopHeartbeat()\n\n          // 判断是否需要重连\n          if (this.shouldReconnect(event)) {\n            this.attemptReconnect()\n          }\n        }\n      } catch (error) {\n        this.handleWebSocketError('创建连接失败')\n      }\n    },\n\n    // 清理WebSocket连接\n    cleanupWebSocket() {\n      if (this.wsConnection) {\n        this.wsConnection.close()\n        this.wsConnection = null\n      }\n      this.stopHeartbeat()\n      this.clearReconnectTimer()\n      this.wsConnectionStatus = 'disconnected'\n    },\n\n    // 判断是否应该重连\n    shouldReconnect(event) {\n      // 正常关闭不重连\n      if (event.code === 1000) {\n        return false\n      }\n\n      // 测试已完成不重连\n      if (this.reportData.reportStatus !== '1') {\n        return false\n      }\n\n      // 超过最大重连次数不重连\n      if (this.wsReconnectAttempts >= this.wsMaxReconnectAttempts) {\n        this.$message.warning('实时监控连接已断开，请手动刷新页面')\n        return false\n      }\n\n      // 用户关闭了自动刷新不重连\n      if (!this.autoRefresh) {\n        return false\n      }\n\n      return true\n    },\n\n    // 尝试重连\n    attemptReconnect() {\n      this.wsReconnectAttempts++\n      const delay = Math.min(1000 * Math.pow(2, this.wsReconnectAttempts), 30000) // 指数退避，最大30秒\n\n      console.log(`尝试第${this.wsReconnectAttempts}次重连，${delay}ms后重试`)\n\n      this.wsReconnectInterval = setTimeout(() => {\n        if (this.wsConnectionStatus !== 'connected') {\n          this.initWebSocket()\n        }\n      }, delay)\n    },\n\n    // 清理重连定时器\n    clearReconnectTimer() {\n      if (this.wsReconnectInterval) {\n        clearTimeout(this.wsReconnectInterval)\n        this.wsReconnectInterval = null\n      }\n    },\n\n    // 处理WebSocket错误\n    handleWebSocketError(errorMessage) {\n      this.wsConnectionStatus = 'error'\n      console.error('WebSocket错误:', errorMessage)\n\n      // 如果是首次连接失败，提示用户\n      if (this.wsReconnectAttempts === 0) {\n        // 启用定时刷新作为备用方案\n        this.enablePollingMode()\n      }\n    },\n\n    // 启用轮询模式作为WebSocket的备用方案\n    enablePollingMode() {\n      if (this.refreshInterval) {\n        clearInterval(this.refreshInterval)\n      }\n\n      if (this.autoRefresh && this.reportData.reportStatus === '1') {\n        this.refreshInterval = setInterval(() => {\n          this.loadMonitoringData()\n          this.loadTargetServiceData()\n          this.loadLogData()\n        }, 5000) // 每5秒刷新一次\n\n      }\n    },\n\n    // 心跳检测\n    startHeartbeat() {\n      this.heartbeatInterval = setInterval(() => {\n        if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {\n          this.wsConnection.send(JSON.stringify({ type: 'heartbeat' }))\n        }\n      }, 30000) // 每30秒发送一次心跳\n    },\n\n    // 停止心跳检测\n    stopHeartbeat() {\n      if (this.heartbeatInterval) {\n        clearInterval(this.heartbeatInterval)\n        this.heartbeatInterval = null\n      }\n    },\n\n    // 手动重连WebSocket\n    reconnectWebSocket() {\n      this.wsReconnectAttempts = 0\n      this.initWebSocket()\n    },\n\n    // 处理WebSocket消息\n    handleWebSocketMessage(data) {\n\n      if (data.type === 'performance_update') {\n        // 处理性能更新数据\n        this.realTimeData = data.data;\n        this.updateChartData();\n\n        // 更新实时指标显示\n        if (data.data && data.data.total) {\n          const total = data.data.total;\n\n          // 更新报告基础数据的实时指标\n          this.updateBasicMetricsFromWebSocket(total);\n\n          \n          // 添加性能指标日志\n          this.addPerformanceLog({\n            tps: total.current_rps,\n            responseTime: total.avg_response_time,\n            users: total.current_users,\n            errorRate: total.error_rate\n          });\n        }\n\n        // 如果有详细统计数据，也更新到指标详情表格\n        if (data.data && data.data.detailed_stats) {\n          // 强制触发表格数据更新\n          this.$nextTick(() => {\n            this.$forceUpdate();\n          });\n        }\n\n      } else if (data.type === 'log_update') {\n        // 处理日志更新 - 增强日志处理\n        const logData = this.enhanceLogData(data.data);\n        this.logData.push(logData);\n        // 保持最新2000条日志\n        if (this.logData.length > 2000) {\n          this.logData = this.logData.slice(-2000);\n        }\n        \n        this.scrollToLogBottom();\n\n      } else if (data.type === 'monitoring_update' || data.type === 'system_resources') {\n        // 处理系统资源监控更新\n        this.monitoringData = data.data;\n        \n        // 添加系统资源日志\n        this.addSystemResourceLog(data.data);\n        \n        // 强制更新系统资源卡片显示\n        this.$forceUpdate();\n\n      } else if (data.type === 'test_complete') {\n        \n        // 添加测试完成事件日志\n        this.addEventLog('测试完成', '收到测试完成信号，正在验证测试状态...', 'info');\n\n        // 延迟验证，给测试更多时间完成清理工作\n        setTimeout(() => {\n          this.verifyTestCompletion();\n        }, 3000); // 延长到3秒\n\n      } else if (data.type === 'test_failed') {\n        // 处理测试失败\n        this.taskType = '运行失败';\n        this.reportData.reportStatus = '99';\n        \n        // 添加测试失败事件日志\n        this.addEventLog('测试失败', '测试执行失败，请检查详细错误信息', 'error');\n\n        // 停止所有实时数据更新\n        this.stopAllRealTimeUpdates();\n      } else if (data.type === 'request_success' || data.type === 'request_failure') {\n        // 处理请求成功/失败事件\n        this.addRequestLog(data);\n      }\n    },\n    \n    // 增强日志数据\n    enhanceLogData(logData) {\n      // 如果日志数据已经包含category，直接返回\n      if (logData.category) {\n        return logData;\n      }\n      \n      // 根据日志内容推断类别\n      let category = 'system'; // 默认为系统日志\n      \n      const message = logData.message || '';\n      if (message.includes('ERROR') || message.includes('Exception') || logData.level === 'error') {\n        category = 'error';\n      } else if (message.includes('Request') || message.includes('HTTP') || message.includes('GET ') || \n                message.includes('POST ') || message.includes('PUT ') || message.includes('DELETE ')) {\n        category = 'request';\n      } else if (message.includes('TPS') || message.includes('RPS') || message.includes('响应时间') || \n                message.includes('用户数') || message.includes('性能')) {\n        category = 'performance';\n      } else if (message.includes('开始') || message.includes('结束') || message.includes('启动') || \n                message.includes('停止') || message.includes('完成')) {\n        category = 'event';\n      }\n      \n      // 添加详细信息字段\n      let details = '';\n      if (message.includes('{') && message.includes('}')) {\n        try {\n          // 尝试提取JSON格式的详细信息\n          const jsonStart = message.indexOf('{');\n          const jsonEnd = message.lastIndexOf('}') + 1;\n          const jsonStr = message.substring(jsonStart, jsonEnd);\n          const jsonData = JSON.parse(jsonStr);\n          details = JSON.stringify(jsonData, null, 2);\n        } catch (e) {\n          // 提取失败，忽略错误\n        }\n      }\n      \n      return {\n        ...logData,\n        category,\n        details,\n        formatted: this.formatLogMessage(logData)\n      };\n    },\n    \n    // 格式化日志消息\n    formatLogMessage(log) {\n      const message = log.message || '';\n      \n      // 格式化请求日志\n      if (message.includes('HTTP') && (message.includes('GET') || message.includes('POST') || \n          message.includes('PUT') || message.includes('DELETE'))) {\n        // 提取HTTP方法、URL和状态码\n        const methodMatch = message.match(/(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)\\s+([^\\s]+)/);\n        const statusMatch = message.match(/status\\s+(\\d+)/i) || message.match(/(\\d{3})/);\n        \n        if (methodMatch) {\n          const method = methodMatch[1];\n          const url = methodMatch[2];\n          const status = statusMatch ? statusMatch[1] : '';\n          \n          return {\n            method,\n            url,\n            status,\n            isFormatted: true\n          };\n        }\n      }\n      \n      return null;\n    },\n    \n    // 滚动到日志底部的辅助方法\n    scrollToLogBottom() {\n      // 如果在日志标签页，自动滚动到底部\n      if (this.activeIndex === '5') {\n        this.$nextTick(() => {\n          const logContainer = document.querySelector('.log-container');\n          if (logContainer) {\n            logContainer.scrollTop = logContainer.scrollHeight;\n          }\n        });\n      }\n    },\n\n    // 添加系统资源日志\n    addSystemResourceLog(data) {\n      if (!data) return;\n      \n      // 每分钟只记录一次系统资源日志，避免日志过多\n      const now = new Date();\n      const lastSystemLog = this.logData.find(log => log.category === 'system' && log.type === 'resource');\n      if (lastSystemLog) {\n        const lastTime = new Date(lastSystemLog.timestamp);\n        if (now - lastTime < 60000) { // 小于1分钟不记录\n          return;\n        }\n      }\n      \n      this.logData.push({\n        timestamp: new Date().toISOString(),\n        level: 'info',\n        category: 'system',\n        type: 'resource',\n        message: `系统资源: CPU ${data.cpu_percent || 0}%, 内存 ${data.memory_percent || 0}%, 磁盘 ${data.disk_percent || 0}%`,\n        details: JSON.stringify({\n          cpu: data.cpu_percent || 0,\n          memory: data.memory_percent || 0,\n          disk: data.disk_percent || 0,\n          network_sent: this.formatBytes(data.network_sent || 0),\n          network_recv: this.formatBytes(data.network_recv || 0)\n        }, null, 2)\n      });\n      \n      // 保持最新2000条日志\n      if (this.logData.length > 2000) {\n        this.logData = this.logData.slice(-2000);\n      }\n      \n      this.scrollToLogBottom();\n    },\n    \n    // 添加性能指标日志\n    addPerformanceLog(data) {\n      if (!data) return;\n      \n      // 每分钟只记录一次性能指标日志，避免日志过多\n      const now = new Date();\n      const lastPerfLog = this.logData.find(log => log.category === 'performance' && log.type === 'metrics');\n      if (lastPerfLog) {\n        const lastTime = new Date(lastPerfLog.timestamp);\n        if (now - lastTime < 60000) { // 小于1分钟不记录\n          return;\n        }\n      }\n      \n      this.logData.push({\n        timestamp: new Date().toISOString(),\n        level: 'info',\n        category: 'performance',\n        type: 'metrics',\n        message: `性能指标: TPS ${data.tps?.toFixed(2) || 0}, 响应时间 ${data.responseTime?.toFixed(2) || 0}ms, 用户数 ${data.users || 0}, 错误率 ${data.errorRate?.toFixed(2) || 0}%`,\n        details: JSON.stringify({\n          tps: data.tps?.toFixed(2) || 0,\n          responseTime: data.responseTime?.toFixed(2) || 0,\n          users: data.users || 0,\n          errorRate: data.errorRate?.toFixed(2) || 0\n        }, null, 2)\n      });\n      \n      // 保持最新2000条日志\n      if (this.logData.length > 2000) {\n        this.logData = this.logData.slice(-2000);\n      }\n      \n      this.scrollToLogBottom();\n    },\n    \n    // 添加事件日志\n    addEventLog(title, message, level = 'info') {\n      this.logData.push({\n        timestamp: new Date().toISOString(),\n        level,\n        category: 'event',\n        type: 'event',\n        message: `${title}: ${message}`\n      });\n      \n      // 保持最新2000条日志\n      if (this.logData.length > 2000) {\n        this.logData = this.logData.slice(-2000);\n      }\n      \n      this.scrollToLogBottom();\n    },\n    \n    // 添加请求日志\n    addRequestLog(data) {\n      if (!data || !data.data) return;\n      \n      const reqData = data.data;\n      const isSuccess = data.type === 'request_success';\n      const level = isSuccess ? 'info' : 'error';\n      \n      const message = isSuccess \n        ? `请求成功: ${reqData.method || 'GET'} ${reqData.url || '/'} (${reqData.response_time || 0}ms)`\n        : `请求失败: ${reqData.method || 'GET'} ${reqData.url || '/'} - ${reqData.error || '未知错误'}`;\n      \n      this.logData.push({\n        timestamp: new Date().toISOString(),\n        level,\n        category: 'request',\n        type: isSuccess ? 'success' : 'failure',\n        message,\n        details: JSON.stringify({\n          method: reqData.method || 'GET',\n          url: reqData.url || '/',\n          status: reqData.status || (isSuccess ? 200 : 500),\n          response_time: reqData.response_time || 0,\n          error: reqData.error || null,\n          exception: reqData.exception || null\n        }, null, 2),\n        formatted: {\n          method: reqData.method || 'GET',\n          url: reqData.url || '/',\n          status: reqData.status || (isSuccess ? 200 : 500),\n          isFormatted: true\n        }\n      });\n      \n      // 保持最新2000条日志\n      if (this.logData.length > 2000) {\n        this.logData = this.logData.slice(-2000);\n      }\n      \n      this.scrollToLogBottom();\n    },\n    \n    // 获取日志样式\n    getLogClass(level) {\n      return `log-${level}`\n    },\n    \n    // 获取请求状态样式\n    getStatusClass(status) {\n      if (!status) return '';\n      const code = parseInt(status);\n      if (code >= 200 && code < 300) return 'status-success';\n      if (code >= 300 && code < 400) return 'status-redirect';\n      if (code >= 400 && code < 500) return 'status-client-error';\n      if (code >= 500) return 'status-server-error';\n      return '';\n    },\n    \n    // 获取HTTP方法样式\n    getMethodClass(method) {\n      if (!method) return '';\n      switch(method.toUpperCase()) {\n        case 'GET': return 'method-get';\n        case 'POST': return 'method-post';\n        case 'PUT': return 'method-put';\n        case 'DELETE': return 'method-delete';\n        case 'PATCH': return 'method-patch';\n        case 'HEAD': return 'method-head';\n        case 'OPTIONS': return 'method-options';\n        default: return '';\n      }\n    },\n    \n    // 获取日志类别图标\n    getLogCategoryIcon(category) {\n      switch(category) {\n        case 'system': return 'Monitor';\n        case 'request': return 'Connection';\n        case 'error': return 'CircleClose';\n        case 'performance': return 'DataAnalysis';\n        case 'event': return 'Bell';\n        default: return 'Document';\n      }\n    },\n    \n    // 加载日志数据\n    async loadLogData() {\n      try {\n        // 构建查询参数\n        const params = {\n          level: this.logLevel === 'all' ? '' : this.logLevel,\n          limit: 2000, // 增加日志获取数量\n          offset: 0\n        }\n\n        const response = await this.$api.getTaskReportLogs(this.reportId, params)\n        // 增强日志数据\n        this.logData = (response.data.logs || []).map(log => this.enhanceLogData(log));\n\n        // 添加初始化完成事件日志\n        if (this.logData.length === 0) {\n          this.logData.push({\n            timestamp: new Date().toISOString(),\n            level: 'info',\n            category: 'event',\n            type: 'event',\n            message: '日志初始化: 日志系统已初始化，等待测试数据...'\n          });\n        }\n        \n        // 滚动到日志底部\n        this.scrollToLogBottom();\n      } catch (error) {\n        // 清空日志数据，不生成模拟日志\n        this.logData = []\n        // 添加错误日志\n        this.logData.push({\n          timestamp: new Date().toISOString(),\n          level: 'error',\n          category: 'event',\n          type: 'event',\n          message: `日志加载失败: ${error.message || '未知错误'}`\n        });\n        \n        // 滚动到日志底部\n        this.scrollToLogBottom();\n      }\n    },\n\n    // 重新运行测试\n    async rerunTest() {\n      try {\n        // 确认操作\n        await this.$confirm(\n          `将使用相同的测试配置，创建新的测试报告进行重新测试？`,\n          '确认重新运行',\n          {\n            confirmButtonText: '确定运行',\n            cancelButtonText: '取消',\n            type: 'warning',\n            dangerouslyUseHTMLString: true\n          }\n        );\n\n        // 显示重新运行开始的提示\n        this.$message.info('正在启动测试，请稍候...');\n\n        // 获取原始任务配置\n        // task 字段可能是一个对象或者一个ID\n        let taskId = null;\n\n\n        if (this.reportData.task) {\n          // 如果 task 是对象，取其 id\n          if (typeof this.reportData.task === 'object' && this.reportData.task.id) {\n            taskId = this.reportData.task.id;\n          } else if (typeof this.reportData.task === 'number') {\n            // 如果 task 直接是 ID\n            taskId = this.reportData.task;\n          }\n        }\n\n        // 如果还是没有找到，尝试其他字段\n        if (!taskId) {\n          taskId = this.reportData.taskId || this.reportData.task_id;\n        }\n\n        if (!taskId) {\n          throw new Error('无法获取任务ID');\n        }\n\n        // 获取环境ID\n        let envId = null;\n        if (this.reportData.env) {\n          // 如果env是对象，取其id\n          envId = typeof this.reportData.env === 'object' ? this.reportData.env.id : this.reportData.env;\n        } else {\n          // 尝试其他字段\n          envId = this.reportData.envId || this.reportData.env_id;\n        }\n\n        // 调用现有的运行任务API来实现重新运行\n        const response = await this.$api.runTask(taskId, {\n          rerun: true,\n          copy_settings: true,\n          env_id: envId, // 添加环境ID参数\n          new_report_name: `${this.reportData.reportName}_重跑_${new Date().toISOString().slice(0, 16).replace('T', '_').replace(/:/g, '')}`\n        });\n\n        if (response.status === 200) {\n          this.$message.success('测试重新运行成功！');\n\n          // 获取新报告ID\n          const newReportId = response.data.report_id || response.data.id;\n\n          if (newReportId) {\n            // 跳转到新的报告详情页面\n            this.$router.push({\n              name: 'PerformanceResult-Detail',\n              params: { id: newReportId }\n            });\n          } else {\n            // 如果没有返回新报告ID，刷新当前页面\n            setTimeout(() => {\n              location.reload();\n            }, 2000);\n          }\n        }\n\n      } catch (error) {\n        if (error === 'cancel') {\n          this.$message.info('已取消重新运行');\n          return;\n        }\n        this.$message.error('重新运行失败: ' + (error.response?.data?.message || error.message || '未知错误'));\n      }\n    },\n\n    // 验证测试是否真正完成\n    async verifyTestCompletion() {\n      try {\n\n        // 重新获取报告数据来验证状态\n        const response = await this.$api.getTaskReportDetail(this.reportId);\n        const latestReportData = response.data;\n\n\n        // 检查多个条件来确认测试真正完成\n        const isStatusCompleted = latestReportData.reportStatus === '0';\n        const hasEndTime = latestReportData.endTime && latestReportData.endTime !== null;\n        const hasFinalData = latestReportData.totalRequests && latestReportData.totalRequests > 0;\n\n\n        // 只有当所有条件都满足时，才认为测试真正完成\n        if (isStatusCompleted && (hasEndTime || hasFinalData)) {\n          this.taskType = '已完成';\n          this.reportData.reportStatus = '0';\n\n          // 更新报告数据\n          Object.assign(this.reportData, latestReportData);\n\n          // 停止所有实时数据更新\n          this.stopAllRealTimeUpdates();\n\n          // 显示完成通知\n          this.$message.success('性能测试已完成');\n\n          // 重新生成图表数据\n          this.generateChartData();\n\n        } else {\n\n          // 如果测试还没真正完成，继续等待并重新验证\n          setTimeout(() => {\n            this.verifyTestCompletion();\n          }, 5000); // 5秒后重新验证\n        }\n\n      } catch (error) {\n\n        // 如果验证失败，降级处理：直接更新为完成状态\n        this.taskType = '已完成';\n        this.reportData.reportStatus = '0';\n        this.stopAllRealTimeUpdates();\n        this.loadReportData();\n      }\n    },\n    updateBasicMetricsFromWebSocket(totalStats) {\n      // 更新所有实时指标\n      if (totalStats.current_rps !== undefined) {\n        this.reportData.avgTps = totalStats.current_rps;\n      }\n      if (totalStats.avg_response_time !== undefined) {\n        this.reportData.avgResponseTime = totalStats.avg_response_time;\n      }\n      if (totalStats.min_response_time !== undefined) {\n        this.reportData.minResponseTime = totalStats.min_response_time;\n      }\n      if (totalStats.max_response_time !== undefined) {\n        this.reportData.maxResponseTime = totalStats.max_response_time;\n      }\n      if (totalStats.median_response_time !== undefined) {\n        this.reportData.p50ResponseTime = totalStats.median_response_time;\n      }\n      if (totalStats.p90_response_time !== undefined) {\n        this.reportData.p90ResponseTime = totalStats.p90_response_time;\n      }\n      if (totalStats.p95_response_time !== undefined) {\n        this.reportData.p95ResponseTime = totalStats.p95_response_time;\n      }\n      if (totalStats.p99_response_time !== undefined) {\n        this.reportData.p99ResponseTime = totalStats.p99_response_time;\n      }\n      if (totalStats.num_requests !== undefined) {\n        this.reportData.totalRequests = totalStats.num_requests;\n      }\n      if (totalStats.num_failures !== undefined) {\n        this.reportData.failedRequests = totalStats.num_failures;\n        this.reportData.successRequests = (totalStats.num_requests || 0) - totalStats.num_failures;\n      }\n      if (totalStats.error_rate !== undefined) {\n        this.reportData.errorRate = totalStats.error_rate;\n      }\n      if (totalStats.current_users !== undefined) {\n        this.reportData.currentUsers = totalStats.current_users;\n      }\n      if (totalStats.elapsed_time !== undefined) {\n        this.reportData.duration = Math.floor(totalStats.elapsed_time);\n      }\n\n      // 强制更新视图以确保数据同步显示\n      this.$forceUpdate();\n    },\n\n    // 加载报告数据\n    async loadReportData() {\n      try {\n        const response = await this.$api.getTaskReportDetail(this.reportId)\n        this.reportData = response.data\n        this.taskName = this.reportData.reportName\n        this.desc = this.reportData.resultAnalyse || '暂无分析结果'\n        this.taskType = this.getStatusText(this.reportData.reportStatus)\n\n        // 如果报告已完成或失败，生成分析结果\n        if (this.reportData.reportStatus === '0' || this.reportData.reportStatus === '99') {\n          this.generateAnalysisResult()\n        }\n\n        // 生成图表数据\n        this.generateChartData()\n\n        // 如果测试正在运行，启动WebSocket连接和实时数据更新\n        if (this.reportData.reportStatus === '1') {\n          this.initWebSocket()\n          // 启动实时数据更新（轮询）\n          this.startRealTimeDataUpdate()\n        }\n      } catch (error) {\n        this.$message.error('加载报告数据失败')\n      }\n    },\n\n    // 生成分析结果\n    generateAnalysisResult() {\n      if (!this.reportData) return\n\n      let analysisText = this.desc || ''\n\n      // 如果没有分析结果，根据报告状态生成默认分析\n      if (!analysisText || analysisText === '暂无分析结果') {\n        if (this.reportData.reportStatus === '0') {\n          // 测试成功完成的分析\n          const successRate = this.getSuccessRate()\n          const avgResponseTime = (this.reportData.avgResponseTime || 0).toFixed(2)\n          const avgTps = this.reportData.avgTps || 0\n          const errorRate = this.reportData.errorRate || 0\n\n          analysisText = `测试已成功完成。\\n`\n          analysisText += `总体性能表现：平均响应时间 ${avgResponseTime}ms，平均TPS ${avgTps}，成功率 ${successRate}%。\\n`\n\n          if (avgResponseTime < 100) {\n            analysisText += `响应时间表现优秀，系统响应迅速。`\n          } else if (avgResponseTime < 500) {\n            analysisText += `响应时间表现良好，符合一般性能要求。`\n          } else if (avgResponseTime < 1000) {\n            analysisText += `响应时间偏高，建议进行性能优化。`\n          } else {\n            analysisText += `响应时间过高，存在明显性能瓶颈，需要立即优化。`\n          }\n\n          if (errorRate > 5) {\n            analysisText += `\\n错误率较高(${errorRate}%)，建议检查系统稳定性。`\n          } else if (errorRate > 1) {\n            analysisText += `\\n存在少量错误(${errorRate}%)，建议关注异常情况。`\n          }\n\n        } else if (this.reportData.reportStatus === '99') {\n          // 测试失败的分析\n          analysisText = `测试运行失败。\\n`\n\n          if (this.reportData.totalRequests > 0) {\n            const failedRequests = this.reportData.failedRequests || 0\n            const errorRate = this.reportData.errorRate || 0\n            analysisText += `在执行 ${this.reportData.totalRequests} 个请求中，失败 ${failedRequests} 个，错误率 ${errorRate}%。\\n`\n          }\n\n          analysisText += `建议检查：\\n`\n          analysisText += `1. 目标服务是否正常运行\\n`\n          analysisText += `2. 网络连接是否稳定\\n`\n          analysisText += `3. 测试脚本配置是否正确\\n`\n          analysisText += `4. 服务器资源是否充足`\n        }\n      }\n\n      // 添加资源使用情况分析\n      if (this.reportData.avgCpu && this.reportData.avgMemory) {\n        analysisText += `\\n\\n资源使用情况：平均CPU ${this.reportData.avgCpu}%，平均内存 ${this.reportData.avgMemory}%。`\n\n        if (this.reportData.avgCpu > 80) {\n          analysisText += `CPU使用率过高，可能成为性能瓶颈。`\n        } else if (this.reportData.avgMemory > 85) {\n          analysisText += `内存使用率过高，建议增加内存或优化内存使用。`\n        }\n      }\n\n      this.desc = analysisText\n    },\n\n    // 加载监控数据\n    async loadMonitoringData() {\n      try {\n        const response = await this.$api.getSystemResourceStatus()\n        const currentData = response.data.current || {}\n\n        // 转换后端数据格式为前端期望的格式\n        this.monitoringData = {\n          cpu_percent: currentData.cpu?.percent || 0,\n          memory_percent: currentData.memory?.percent || 0,\n          disk_percent: currentData.disk?.percent || 0,\n          network_sent: currentData.network?.bytes_sent || 0,\n          network_recv: currentData.network?.bytes_recv || 0,\n          active_connections: currentData.connections || 0,\n          current_rps: currentData.rps || 0,\n          current_users: currentData.users || 0,\n          error_rate: currentData.error_rate || 0,\n          server_type: currentData.server_type || 'single',\n          cpu_cores: currentData.cpu?.count || 0,\n          total_memory: currentData.memory?.total || 0,\n          uptime: currentData.uptime || null\n        }\n      } catch (error) {\n        console.error('加载监控数据失败:', error)\n        // 清空监控数据，不显示模拟数据\n        this.monitoringData = {\n          cpu_percent: 0,\n          memory_percent: 0,\n          disk_percent: 0,\n          network_sent: 0,\n          network_recv: 0,\n          active_connections: 0,\n          current_rps: 0,\n          current_users: 0,\n          error_rate: 0,\n          server_type: 'unknown',\n          cpu_cores: 0,\n          total_memory: 0,\n          uptime: null\n        }\n      }\n    },\n\n    // 加载目标服务数据\n    async loadTargetServiceData() {\n      try {\n        const response = await this.$api.getTargetServiceStatus(this.reportId)\n        this.targetServiceData = response.data || {}\n      } catch (error) {\n        console.error('加载目标服务数据失败:', error)\n        // 清空目标服务数据，不显示模拟数据\n        this.targetServiceData = {\n          service_status: 'unknown',\n          connection_pool_active: 0,\n          connection_pool_total: 0,\n          db_connections: 0,\n          cache_hit_rate: 0,\n          response_time_trend: {\n            labels: [],\n            values: []\n          },\n          recent_errors: []\n        }\n      }\n    },\n\n    // 刷新日志数据\n    async refreshLogData() {\n      await this.loadLogData()\n      this.$message.success('日志已刷新')\n      this.scrollToLogBottom()\n    },\n\n    // 清空日志显示\n    clearLogs() {\n      this.logData = []\n      this.$message.success('日志已清空')\n    },\n\n\n\n    // 初始化空的图表数据\n    initEmptyChartData() {\n      this.xData = [];\n      this.responseTimeData = [];\n      this.rpsData = [];\n      this.tpsData = [];\n      this.usersData = [];\n      this.p50Data = [];\n      this.p90Data = [];\n      this.p95Data = [];\n      this.p99Data = [];\n      this.errorRateData = [];\n    },\n\n    // 生成图表数据\n    generateChartData() {\n      console.log('开始生成图表数据');\n\n      // 初始化空的图表数据\n      this.initEmptyChartData();\n\n      let dataUpdated = false;\n\n      if (this.reportData.reportResult) {\n        try {\n          const result = JSON.parse(this.reportData.reportResult);\n          if (result.stats_history && result.stats_history.length > 0) {\n            this.updateChartDataFromHistory(result.stats_history);\n            dataUpdated = true;\n          } else if (result.detailed_stats && this.reportData.reportStatus === '0') {\n            this.updateChartDataFromDetailedStats(result);\n            dataUpdated = true;\n          }\n        } catch (error) {\n          console.error('解析报告结果失败:', error);\n        }\n      }\n\n      // 如果没有历史数据且任务已完成，使用报告中的基础数据生成图表\n      if (!dataUpdated && this.reportData.totalRequests && this.reportData.totalRequests > 0 && this.reportData.reportStatus === '0') {\n        this.generateChartFromReportData();\n        dataUpdated = true;\n      }\n\n      // 如果是运行中的任务，启动实时数据更新\n      if (!dataUpdated && this.reportData.reportStatus === '1') {\n        console.log('测试运行中，启动实时数据更新');\n        this.startRealTimeDataUpdate();\n      } else if (!dataUpdated) {\n        console.log('无可用数据，显示空图表');\n      }\n\n      // 更新详细指标数据表格\n      this.$nextTick(() => {\n        if (this.activeIndex === '1') {\n          // 如果当前在指标详情页，强制更新表格\n          const tableElement = this.$refs.table;\n          if (tableElement) {\n            tableElement.$forceUpdate();\n          }\n          // 也强制更新组件本身\n          this.$forceUpdate();\n        }\n      });\n    },\n\n    // 从历史数据更新图表\n    updateChartDataFromHistory(statsHistory) {\n      this.xData = [];\n      this.responseTimeData = [];\n      this.rpsData = [];\n      this.tpsData = [];\n      this.usersData = [];\n      this.p50Data = [];\n      this.p90Data = [];\n      this.p95Data = [];\n      this.p99Data = [];\n      this.errorRateData = [];\n\n      // 清空接口数据\n      this.interfaceChartData = {\n        responseTime: {},\n        rps: {},\n        tps: {},\n        users: {},\n        p50: {},\n        p90: {},\n        p95: {},\n        p99: {},\n        errorRate: {}\n      };\n      this.availableInterfaces = [];\n\n      // 添加更新realTimeData的代码，使用最后一个数据点，以便详细指标能够显示\n      if (statsHistory.length > 0) {\n        const lastStats = statsHistory[statsHistory.length - 1];\n        this.realTimeData = {\n          total: lastStats.total || {},\n          detailed_stats: lastStats.detailed_stats || {}\n        };\n      }\n\n      statsHistory.forEach((stat, index) => {\n        // 处理时间戳\n        const timestamp = stat.timestamp || new Date().toISOString();\n        this.xData.push(new Date(timestamp).toLocaleTimeString());\n\n        // 从total统计数据中提取指标\n        const total = stat.total || {};\n        this.responseTimeData.push(total.avg_response_time || 0);\n        this.rpsData.push(total.current_rps || 0);\n        this.tpsData.push(total.current_tps || total.current_rps || 0);\n        this.usersData.push(total.current_users || 0);\n        this.p50Data.push(total.p50_response_time || 0);\n        this.p90Data.push(total.p90_response_time || 0);\n        this.p95Data.push(total.p95_response_time || 0);\n        this.p99Data.push(total.p99_response_time || 0);\n        this.errorRateData.push(total.error_rate || 0);\n\n        // 处理各接口的详细数据\n        if (stat.detailed_stats) {\n          Object.keys(stat.detailed_stats).forEach(key => {\n            const stats = stat.detailed_stats[key];\n            const method = this.getCorrectHttpMethod(key, stats);\n            const interfaceName = this.getCorrectInterfaceName(key, stats);\n            const uniqueKey = method + '_' + interfaceName;\n            // 初始化接口数据数组\n            if (!this.interfaceChartData.responseTime[uniqueKey]) {\n              this.interfaceChartData.responseTime[uniqueKey] = [];\n              this.interfaceChartData.rps[uniqueKey] = [];\n              this.interfaceChartData.tps[uniqueKey] = [];\n              this.interfaceChartData.users[uniqueKey] = [];\n              this.interfaceChartData.p50[uniqueKey] = [];\n              this.interfaceChartData.p90[uniqueKey] = [];\n              this.interfaceChartData.p95[uniqueKey] = [];\n              this.interfaceChartData.p99[uniqueKey] = [];\n              this.interfaceChartData.errorRate[uniqueKey] = [];\n            }\n            // 添加接口数据\n            this.interfaceChartData.responseTime[uniqueKey].push(stats.avg_response_time || 0);\n            this.interfaceChartData.rps[uniqueKey].push(stats.current_rps || 0);\n            this.interfaceChartData.tps[uniqueKey].push(stats.current_tps || stats.current_rps || 0);\n            this.interfaceChartData.users[uniqueKey].push(stats.current_users || 0);\n            this.interfaceChartData.p50[uniqueKey].push(stats.p50_response_time || 0);\n            this.interfaceChartData.p90[uniqueKey].push(stats.p90_response_time || 0);\n            this.interfaceChartData.p95[uniqueKey].push(stats.p95_response_time || 0);\n            this.interfaceChartData.p99[uniqueKey].push(stats.p99_response_time || 0);\n            this.interfaceChartData.errorRate[uniqueKey].push(stats.error_rate || 0);\n            // 更新可用接口列表\n            if (!this.availableInterfaces.includes(uniqueKey)) {\n              this.availableInterfaces.push(uniqueKey);\n            }\n          });\n        }\n      });\n    },\n\n    // 从详细统计数据更新图表\n    updateChartDataFromDetailedStats(result) {\n\n      // 更新realTimeData以便详细指标能够显示\n      this.realTimeData = {\n        total: result.total || {},\n        detailed_stats: result.detailed_stats || {}\n      };\n\n      // 创建单个数据点，用于已完成的测试\n      const total = result.total || {};\n      const timestamp = result.timestamp || new Date().toISOString();\n\n      this.xData = [new Date(timestamp).toLocaleTimeString()];\n      this.responseTimeData = [total.avg_response_time || 0];\n      this.rpsData = [total.current_rps || 0];\n      this.tpsData = [total.current_tps || total.current_rps || 0];\n      this.usersData = [total.current_users || 0];\n      this.p50Data = [total.p50_response_time || 0];\n      this.p90Data = [total.p90_response_time || 0];\n      this.p95Data = [total.p95_response_time || 0];\n      this.p99Data = [total.p99_response_time || 0];\n      this.errorRateData = [total.error_rate || 0];\n\n      },\n\n    // 从报告基础数据生成图表\n    generateChartFromReportData() {\n      // 使用报告中的现有数据\n      const duration = this.reportData.duration || 300; // 默认5分钟\n      const dataPoints = Math.min(duration / 10, 30); // 每10秒一个点，最多30个点\n\n      this.xData = [];\n      this.responseTimeData = [];\n      this.rpsData = [];\n      this.tpsData = [];\n      this.usersData = [];\n      this.p50Data = [];\n      this.p90Data = [];\n      this.p95Data = [];\n      this.p99Data = [];\n      this.errorRateData = [];\n\n      // 计算基础值\n      const avgResponse = this.reportData.avgResponseTime || 0;\n      const avgTps = this.reportData.avgTps || 0;\n      const totalRequests = this.reportData.totalRequests || 0;\n      const avgRps = duration > 0 ? totalRequests / duration : avgTps;\n      const p50Response = this.reportData.p50ResponseTime || avgResponse * 1.5;\n      const p90Response = this.reportData.p90ResponseTime || avgResponse * 2;\n      const p95Response = this.reportData.p95ResponseTime || avgResponse * 2.5;\n      const p99Response = this.reportData.p99ResponseTime || avgResponse * 3;\n      const errorRate = this.reportData.errorRate || 0;\n\n      // 生成时间序列数据\n      const startTime = this.reportData.startTime ? new Date(this.reportData.startTime) : new Date();\n\n      for (let i = 0; i < dataPoints; i++) {\n        const time = new Date(startTime.getTime() + i * 10000); // 每10秒\n        this.xData.push(time.toLocaleTimeString());\n\n        // 添加轻微的随机变化，使数据更真实\n        const variation = 0.1; // 10%变化范围\n        this.responseTimeData.push(this.addVariation(avgResponse, variation));\n        this.rpsData.push(this.addVariation(avgRps, variation));\n        this.tpsData.push(this.addVariation(avgTps, variation));\n        this.usersData.push(Math.max(1, Math.floor(this.addVariation(50, variation)))); // 估算用户数\n        this.p50Data.push(this.addVariation(p50Response, variation));\n        this.p90Data.push(this.addVariation(p90Response, variation));\n        this.p95Data.push(this.addVariation(p95Response, variation));\n        this.p99Data.push(this.addVariation(p99Response, variation));\n        this.errorRateData.push(Math.max(0, this.addVariation(errorRate, variation)));\n      }\n\n      },\n\n    // 添加数据变化\n    addVariation(baseValue, variationPercent) {\n      const variation = (Math.random() - 0.5) * 2 * variationPercent;\n      const newValue = baseValue * (1 + variation);\n      return Math.max(0, Number(newValue.toFixed(2)));\n    },\n\n\n    // 生成真实感的随机数据\n    generateRealisticData(count, min, max, type) {\n      const data = [];\n      let baseValue = (min + max) / 2;\n      let trend = (Math.random() - 0.5) * 0.1; // 随机趋势\n\n      for (let i = 0; i < count; i++) {\n        // 添加趋势变化\n        baseValue += trend * (max - min) * 0.05;\n\n        // 添加随机波动\n        const variation = (Math.random() - 0.5) * (max - min) * 0.3;\n        let value = baseValue + variation;\n\n        // 确保值在合理范围内\n        value = Math.max(min, Math.min(max, value));\n\n        // 针对不同类型的数据进行特殊处理\n        if (type === 'error_rate') {\n          value = Math.max(0, Math.min(10, value)); // 错误率不超过10%\n        } else if (type === 'users') {\n          value = Math.floor(value); // 用户数为整数\n        }\n\n        data.push(Number(value.toFixed(2)));\n\n        // 偶尔改变趋势\n        if (Math.random() < 0.1) {\n          trend = (Math.random() - 0.5) * 0.1;\n        }\n      }\n\n      return data;\n    },\n\n    // 启动实时数据更新\n    startRealTimeDataUpdate() {\n      console.log('启动实时数据更新');\n\n      if (this.realTimeUpdateInterval) {\n        clearInterval(this.realTimeUpdateInterval);\n      }\n\n      // 每3秒获取真实的报告数据\n      this.realTimeUpdateInterval = setInterval(() => {\n        this.fetchRealTimeReportData();\n      }, 3000);\n    },\n\n    // 获取实时报告数据\n    async fetchRealTimeReportData() {\n      try {\n        const response = await this.$api.getTaskReportDetail(this.reportId);\n        const reportData = response.data;\n\n        if (reportData.gui_url !== null && reportData.gui_url !== '') {\n          this.guiUrl = reportData.gui_url\n          if (this.hasGuiUrlLoaded === false) {\n            this.refreshGUI()\n          }\n          this.hasGuiUrlLoaded = true\n        }\n\n        // 更新报告基础数据\n        this.updateReportMetrics(reportData);\n\n        // 更新reportResult以便压测列表能更新\n        if (reportData.reportResult) {\n          this.reportData.reportResult = reportData.reportResult;\n        }\n\n        // 如果有新的统计数据，更新图表\n        if (reportData.reportResult) {\n          try {\n            const result = JSON.parse(reportData.reportResult);\n            if (result.stats_history && result.stats_history.length > 0) {\n              this.updateChartDataFromHistory(result.stats_history);\n            } else if (result.total) {\n              // 如果只有当前统计数据，添加到图表中\n              this.addRealTimeDataFromStats(result.total, result.detailed_stats);\n            }\n\n            // 强制触发压测列表更新\n            if (result.detailed_stats) {\n              // 使用nextTick确保在DOM更新后触发\n              this.$nextTick(() => {\n                // 强制更新组件以刷新压测列表\n                this.$forceUpdate();\n                // 如果在指标详情页面，确保表格可见\n                if (this.activeIndex === '1') {\n                  const tableElement = this.$refs.table;\n                  if (tableElement) {\n                    tableElement.$forceUpdate();\n                  }\n                }\n              });\n            }\n          } catch (error) {\n            console.error('解析实时统计数据失败:', error);\n          }\n        }\n\n        // 检查测试是否已完成\n        if (reportData.reportStatus !== '1') {\n          console.log('测试已完成，停止实时更新');\n          this.stopAllRealTimeUpdates();\n          // 重新加载完整数据\n          await this.loadReportData();\n        }\n\n      } catch (error) {\n        console.error('获取实时报告数据失败:', error);\n        // 如果API调用失败，可能测试已经结束，停止更新\n        this.stopAllRealTimeUpdates();\n      }\n    },\n\n    // 停止所有实时数据更新\n    stopAllRealTimeUpdates() {\n      console.log('停止所有实时数据更新...');\n      \n      // 停止实时数据更新\n      this.stopRealTimeDataUpdate();\n      \n      // 停止监控数据刷新\n      this.stopMonitoringRefresh();\n      \n      // 停止目标服务数据刷新\n      this.stopTargetServiceRefresh();\n      \n      // 停止自动刷新（包括日志）\n      if (this.refreshInterval) {\n        clearInterval(this.refreshInterval);\n        this.refreshInterval = null;\n      }\n      \n      // 停止心跳检测\n      if (this.heartbeatInterval) {\n        clearInterval(this.heartbeatInterval);\n        this.heartbeatInterval = null;\n      }\n      \n      // 关闭自动刷新开关\n      this.autoRefresh = false;\n\n      // 清理WebSocket连接\n      this.cleanupWebSocket();\n      \n      console.log('所有实时数据更新已停止');\n    },\n\n    // 停止实时数据更新\n    stopRealTimeDataUpdate() {\n      if (this.realTimeUpdateInterval) {\n        clearInterval(this.realTimeUpdateInterval);\n        this.realTimeUpdateInterval = null;\n        console.log('实时数据更新已停止');\n      }\n    },\n\n    // 更新报告指标数据\n    updateReportMetrics(reportData) {\n      // 更新基础指标\n      if (reportData.avgTps !== undefined) this.reportData.avgTps = reportData.avgTps;\n      if (reportData.avgResponseTime !== undefined) this.reportData.avgResponseTime = reportData.avgResponseTime;\n      if (reportData.avgCpu !== undefined) this.reportData.avgCpu = reportData.avgCpu;\n      if (reportData.avgMemory !== undefined) this.reportData.avgMemory = reportData.avgMemory;\n      if (reportData.totalRequests !== undefined) this.reportData.totalRequests = reportData.totalRequests;\n      if (reportData.successRequests !== undefined) this.reportData.successRequests = reportData.successRequests;\n      if (reportData.failedRequests !== undefined) this.reportData.failedRequests = reportData.failedRequests;\n      if (reportData.errorRate !== undefined) this.reportData.errorRate = reportData.errorRate;\n      if (reportData.duration !== undefined) this.reportData.duration = reportData.duration;\n\n      // 更新状态\n      if (reportData.reportStatus !== undefined) {\n        this.reportData.reportStatus = reportData.reportStatus;\n        this.taskType = this.getStatusText(reportData.reportStatus);\n      }\n    },\n\n    // 从统计数据添加实时数据点\n    addRealTimeDataFromStats(totalStats, detailedStats = null) {\n      const now = new Date().toLocaleTimeString();\n\n      // 添加新的时间点\n      this.xData.push(now);\n\n      // 从真实统计数据中提取总体指标\n      this.responseTimeData.push(totalStats.avg_response_time || 0);\n      this.rpsData.push(totalStats.current_rps || 0);\n      this.tpsData.push(totalStats.current_tps || totalStats.current_rps || 0);\n      this.usersData.push(totalStats.current_users || 0);\n      this.p50Data.push(totalStats.p50_response_time || 0);\n      this.p90Data.push(totalStats.p90_response_time || 0);\n      this.p95Data.push(totalStats.p95_response_time || 0);\n      this.p99Data.push(totalStats.p99_response_time || 0);\n      this.errorRateData.push(totalStats.error_rate || 0);\n\n      // 处理各接口的详细数据\n      if (detailedStats) {\n        Object.entries(detailedStats).forEach(([key, stats]) => {\n          const interfaceName = this.getCorrectInterfaceName(key, stats);  // 使用新的接口名称提取方法\n\n          // 初始化接口数据数组\n          if (!this.interfaceChartData.responseTime[interfaceName]) {\n            this.interfaceChartData.responseTime[interfaceName] = [];\n            this.interfaceChartData.rps[interfaceName] = [];\n            this.interfaceChartData.tps[interfaceName] = [];\n            this.interfaceChartData.users[interfaceName] = [];\n            this.interfaceChartData.p50[interfaceName] = [];\n            this.interfaceChartData.p90[interfaceName] = [];\n            this.interfaceChartData.p95[interfaceName] = [];\n            this.interfaceChartData.p99[interfaceName] = [];\n            this.interfaceChartData.errorRate[interfaceName] = [];\n          }\n\n          // 添加接口数据\n          this.interfaceChartData.responseTime[interfaceName].push(stats.avg_response_time || 0);\n          this.interfaceChartData.rps[interfaceName].push(stats.current_rps || 0);\n          this.interfaceChartData.tps[interfaceName].push(stats.current_tps || stats.current_rps || 0);\n          this.interfaceChartData.users[interfaceName].push(stats.current_users || totalStats.current_users || 0);\n          this.interfaceChartData.p50[interfaceName].push(stats.p50_response_time || 0);\n          this.interfaceChartData.p90[interfaceName].push(stats.p90_response_time || 0);\n          this.interfaceChartData.p95[interfaceName].push(stats.p95_response_time || 0);\n          this.interfaceChartData.p99[interfaceName].push(stats.p99_response_time || 0);\n          this.interfaceChartData.errorRate[interfaceName].push(stats.error_rate || 0);\n\n          // 更新可用接口列表\n          if (!this.availableInterfaces.includes(interfaceName)) {\n            this.availableInterfaces.push(interfaceName);\n          }\n        });\n      }\n\n    },\n\n    // 生成下一个数值（基于上一个值的小幅波动）\n    generateNextValue(dataArray, min, max) {\n      if (dataArray.length === 0) {\n        return (min + max) / 2;\n      }\n\n      const lastValue = dataArray[dataArray.length - 1];\n      const range = max - min;\n      const maxChange = range * 0.1; // 最大变化10%\n\n      const change = (Math.random() - 0.5) * maxChange;\n      let newValue = lastValue + change;\n\n      // 确保值在合理范围内\n      newValue = Math.max(min, Math.min(max, newValue));\n\n      return Number(newValue.toFixed(2));\n    },\n\n\n\n    // 更新图表数据\n    updateChartData() {\n      if (this.realTimeData) {\n        const now = new Date().toLocaleTimeString();\n        this.xData.push(now);\n\n        // 保持最新20个数据点\n        if (this.xData.length > 20) {\n          this.xData.shift();\n          this.responseTimeData.shift();\n          this.rpsData.shift();\n          this.tpsData.shift();\n          this.usersData.shift();\n          this.p50Data.shift();\n          this.p90Data.shift();\n          this.p95Data.shift();\n          this.p99Data.shift();\n          this.errorRateData.shift();\n\n          // 同时清理接口数据\n          Object.keys(this.interfaceChartData.responseTime).forEach(interfaceName => {\n            this.interfaceChartData.responseTime[interfaceName].shift();\n            this.interfaceChartData.rps[interfaceName].shift();\n            this.interfaceChartData.tps[interfaceName].shift();\n            this.interfaceChartData.users[interfaceName].shift();\n            this.interfaceChartData.p50[interfaceName].shift();\n            this.interfaceChartData.p90[interfaceName].shift();\n            this.interfaceChartData.p95[interfaceName].shift();\n            this.interfaceChartData.p99[interfaceName].shift();\n            this.interfaceChartData.errorRate[interfaceName].shift();\n          });\n        }\n\n        // 从实时数据中提取总体指标\n        const total = this.realTimeData.total || {};\n        this.responseTimeData.push(total.avg_response_time || 0);\n        this.rpsData.push(total.current_rps || 0);\n        this.tpsData.push(total.current_tps || total.current_rps || 0);\n        this.usersData.push(total.current_users || 0);\n        this.p50Data.push(total.p50_response_time || 0);\n        this.p90Data.push(total.p90_response_time || 0);\n        this.p95Data.push(total.p95_response_time || 0);\n        this.p99Data.push(total.p99_response_time || 0);\n        this.errorRateData.push(total.error_rate || 0);\n\n        // 从实时数据中提取每个接口的指标\n        if (this.realTimeData.detailed_stats) {\n          Object.entries(this.realTimeData.detailed_stats).forEach(([key, stats]) => {\n            const interfaceName = this.getCorrectInterfaceName(key, stats);  // 使用新的接口名称提取方法\n\n            // 初始化接口数据数组\n            if (!this.interfaceChartData.responseTime[interfaceName]) {\n              this.interfaceChartData.responseTime[interfaceName] = [];\n              this.interfaceChartData.rps[interfaceName] = [];\n              this.interfaceChartData.tps[interfaceName] = [];\n              this.interfaceChartData.users[interfaceName] = [];\n              this.interfaceChartData.p50[interfaceName] = [];\n              this.interfaceChartData.p90[interfaceName] = [];\n              this.interfaceChartData.p95[interfaceName] = [];\n              this.interfaceChartData.p99[interfaceName] = [];\n              this.interfaceChartData.errorRate[interfaceName] = [];\n            }\n\n            // 添加接口数据\n            this.interfaceChartData.responseTime[interfaceName].push(stats.avg_response_time || 0);\n            this.interfaceChartData.rps[interfaceName].push(stats.current_rps || 0);\n            this.interfaceChartData.tps[interfaceName].push(stats.current_tps || stats.current_rps || 0);\n            this.interfaceChartData.users[interfaceName].push(stats.current_users || total.current_users || 0);\n            this.interfaceChartData.p50[interfaceName].push(stats.p50_response_time || 0);\n            this.interfaceChartData.p90[interfaceName].push(stats.p90_response_time || 0);\n            this.interfaceChartData.p95[interfaceName].push(stats.p95_response_time || 0);\n            this.interfaceChartData.p99[interfaceName].push(stats.p99_response_time || 0);\n            this.interfaceChartData.errorRate[interfaceName].push(stats.error_rate || 0);\n\n            // 更新可用接口列表\n            if (!this.availableInterfaces.includes(interfaceName)) {\n              this.availableInterfaces.push(interfaceName);\n            }\n          });\n        }\n\n      }\n    },\n\n    // 获取详细指标数据\n    getDetailedMetrics() {\n      if (!this.reportData) return []\n\n      // 如果有实时数据，优先使用实时数据\n      if (this.realTimeData) {\n        if (this.realTimeData.detailed_stats || this.realTimeData.total) {\n          // 如果没有detailed_stats，创建一个空对象\n          const detailedStats = this.realTimeData.detailed_stats || {};\n          return this.formatDetailedStats(detailedStats);\n        }\n      }\n\n      // 如果有reportResult中的详细统计数据，使用历史数据\n      if (this.reportData.reportResult) {\n        try {\n          const result = JSON.parse(this.reportData.reportResult);\n\n          // 将total数据保存到realTimeData以便格式化时使用\n          if (result.total) {\n            this.realTimeData = this.realTimeData || {};\n            this.realTimeData.total = result.total;\n          }\n\n          if (result.detailed_stats) {\n            return this.formatDetailedStats(result.detailed_stats);\n          }\n        } catch (error) {\n          console.error('解析详细统计数据失败:', error);\n        }\n      }\n\n      // 只有在任务已完成且有实际数据时，才显示总体数据\n      if (this.reportData.reportStatus === '0' && this.reportData.totalRequests && this.reportData.totalRequests > 0) {\n        return [{\n          name: this.reportData.reportName || '总体',\n          method: 'ALL',\n          totalRequests: this.reportData.totalRequests || 0,\n          successRequests: this.reportData.successRequests || 0,\n          failedRequests: this.reportData.failedRequests || 0,\n          maxResponseTime: this.reportData.maxResponseTime || 0,\n          minResponseTime: this.reportData.minResponseTime || 0,\n          avgResponseTime: this.reportData.avgResponseTime || 0,\n          p50ResponseTime: this.reportData.p50ResponseTime || 0, // 添加p50ResponseTime\n          p90ResponseTime: this.reportData.p90ResponseTime || 0,\n          p95ResponseTime: this.reportData.p95ResponseTime || 0, // 添加p95ResponseTime\n          p99ResponseTime: this.reportData.p99ResponseTime || 0,\n          rps: this.calculateRPS(),\n          avgRps: this.calculateAvgRPS(),\n          avgTps: this.reportData.avgTps || 0,\n          avgTpsAvg: this.reportData.avgTps || 0,\n          errorRate: (this.reportData.errorRate || 0).toFixed(2)\n        }]\n      }\n\n      // 如果是运行中的任务或没有数据，返回空数组\n      return []\n    },\n\n    // 格式化详细统计数据\n    formatDetailedStats(detailedStats) {\n      const results = [];\n\n      // 收集所有接口数据用于计算总体统计\n      const interfaceData = [];\n      for (const [key, stats] of Object.entries(detailedStats)) {\n        const interfaceMetrics = {\n          name: this.getCorrectInterfaceName(key, stats),\n          method: this.getCorrectHttpMethod(key, stats),\n          totalRequests: stats.num_requests || 0,\n          successRequests: stats.success_requests || (stats.num_requests - stats.num_failures) || 0,\n          failedRequests: stats.num_failures || 0,\n          maxResponseTime: Math.round(stats.max_response_time || 0),\n          minResponseTime: Math.round(stats.min_response_time || 0),\n          avgResponseTime: Math.round(stats.avg_response_time || 0),\n          p50ResponseTime: Math.round(stats.median_response_time || 0), // 添加p50ResponseTime\n          p90ResponseTime: Math.round(stats.p90_response_time || 0),\n          p95ResponseTime: Math.round(stats.p95_response_time || 0), // 添加p95ResponseTime\n          p99ResponseTime: Math.round(stats.p99_response_time || 0),\n          rps: typeof stats.current_rps === 'number' ? Number(stats.current_rps).toFixed(2) : '0.00',\n          avgRps: typeof stats.current_rps === 'number' ? Number(stats.current_rps).toFixed(2) : '0.00',\n          avgTps: typeof stats.current_rps === 'number' ? Number(stats.current_rps).toFixed(2) : '0.00',\n          avgTpsAvg: typeof stats.current_rps === 'number' ? Number(stats.current_rps).toFixed(2) : '0.00',\n          errorRate: typeof stats.error_rate === 'number' ? Number(stats.error_rate).toFixed(2) : '0.00',\n          currentUsers: stats.current_users || stats.users || 0\n        };\n        interfaceData.push(interfaceMetrics);\n        results.push(interfaceMetrics);\n      }\n\n      // 计算总体统计数据（所有接口相加）\n      if (interfaceData.length > 0) {\n        const totalMetrics = this.calculateTotalFromInterfaces(interfaceData);\n        results.unshift(totalMetrics); // 将总体数据放在第一位\n      } else if (this.realTimeData && this.realTimeData.total) {\n        // 如果没有接口数据，使用实时数据中的总体数据\n        const total = this.realTimeData.total;\n        const duration = this.reportData.duration || 1;\n        const avgRps = total.num_requests ? (total.num_requests / duration).toFixed(2) : '0.00';\n\n        results.unshift({\n          name: '总体',\n          method: 'ALL',\n          totalRequests: total.num_requests || 0,\n          successRequests: (total.num_requests - total.num_failures) || 0,\n          failedRequests: total.num_failures || 0,\n          maxResponseTime: Math.round(total.max_response_time || 0),\n          minResponseTime: Math.round(total.min_response_time || 0),\n          avgResponseTime: Math.round(total.avg_response_time || 0),\n          p50ResponseTime: Math.round(total.median_response_time || 0), // 添加p50ResponseTime\n          p90ResponseTime: Math.round(total.p90_response_time || 0),\n          p95ResponseTime: Math.round(total.p95_response_time || 0), // 添加p95ResponseTime\n          p99ResponseTime: Math.round(total.p99_response_time || 0),\n          rps: (total.current_rps || 0).toFixed(2),\n          avgRps: avgRps,\n          avgTps: (total.current_tps || total.current_rps || 0).toFixed(2),\n          avgTpsAvg: avgRps,\n          errorRate: (total.error_rate || 0).toFixed(2),\n          currentUsers: total.current_users || this.getCurrentUserCount()\n        });\n      } else if (this.reportData && this.reportData.totalRequests > 0) {\n        // 如果没有实时数据，使用报告的总体数据\n        const duration = this.reportData.duration || 1;\n        const avgRps = this.reportData.totalRequests ? (this.reportData.totalRequests / duration).toFixed(2) : '0.00';\n\n        results.unshift({\n          name: '总体',\n          method: 'ALL',\n          totalRequests: this.reportData.totalRequests || 0,\n          successRequests: this.reportData.successRequests || 0,\n          failedRequests: this.reportData.failedRequests || 0,\n          maxResponseTime: Math.round(this.reportData.maxResponseTime || 0),\n          minResponseTime: Math.round(this.reportData.minResponseTime || 0),\n          avgResponseTime: Math.round(this.reportData.avgResponseTime || 0),\n          p50ResponseTime: Math.round(this.reportData.p50ResponseTime || 0), // 添加p50ResponseTime\n          p90ResponseTime: Math.round(this.reportData.p90ResponseTime || 0),\n          p95ResponseTime: Math.round(this.reportData.p95ResponseTime || 0), // 添加p95ResponseTime\n          p99ResponseTime: Math.round(this.reportData.p99ResponseTime || 0),\n          rps: avgRps,\n          avgRps: avgRps,\n          avgTps: (this.reportData.avgTps || 0).toFixed(2),\n          avgTpsAvg: (this.reportData.avgTps || 0).toFixed(2),\n          errorRate: (this.reportData.errorRate || 0).toFixed(2),\n          currentUsers: this.getCurrentUserCount()\n        });\n      }\n\n      return results;\n    },\n\n    // 计算所有接口的总体统计（相加模式）\n    calculateTotalFromInterfaces(interfaceData) {\n      let totalRequests = 0;\n      let totalSuccessRequests = 0;\n      let totalFailedRequests = 0;\n      let totalRps = 0;\n      let totalTps = 0;\n      let totalUsers = 0;\n      let maxResponseTime = 0;\n      let minResponseTime = Infinity;\n      let weightedAvgResponseTime = 0;\n      let weightedP50ResponseTime = 0; // 添加p50加权计算\n      let weightedP90ResponseTime = 0;\n      let weightedP95ResponseTime = 0; // 添加p95加权计算\n      let weightedP99ResponseTime = 0;\n\n      // 汇总所有接口的数据\n      interfaceData.forEach(item => {\n        totalRequests += item.totalRequests;\n        totalSuccessRequests += item.successRequests;\n        totalFailedRequests += item.failedRequests;\n        totalRps += parseFloat(item.rps);\n        totalTps += parseFloat(item.avgTps);\n        totalUsers = Math.max(totalUsers, item.currentUsers || 0);\n        maxResponseTime = Math.max(maxResponseTime, item.maxResponseTime);\n        if (item.minResponseTime > 0) {\n          minResponseTime = Math.min(minResponseTime, item.minResponseTime);\n        }\n\n        // 加权平均响应时间（按请求数加权）\n        weightedAvgResponseTime += item.avgResponseTime * item.totalRequests;\n        weightedP50ResponseTime += item.p50ResponseTime * item.totalRequests; // 添加p50加权计算\n        weightedP90ResponseTime += item.p90ResponseTime * item.totalRequests;\n        weightedP95ResponseTime += item.p95ResponseTime * item.totalRequests; // 添加p95加权计算\n        weightedP99ResponseTime += item.p99ResponseTime * item.totalRequests;\n      });\n\n      // 计算加权平均\n      const avgResponseTime = totalRequests > 0 ? Math.round(weightedAvgResponseTime / totalRequests) : 0;\n      const p50ResponseTime = totalRequests > 0 ? Math.round(weightedP50ResponseTime / totalRequests) : 0; // 计算p50加权平均\n      const p90ResponseTime = totalRequests > 0 ? Math.round(weightedP90ResponseTime / totalRequests) : 0;\n      const p95ResponseTime = totalRequests > 0 ? Math.round(weightedP95ResponseTime / totalRequests) : 0; // 计算p95加权平均\n      const p99ResponseTime = totalRequests > 0 ? Math.round(weightedP99ResponseTime / totalRequests) : 0;\n      const errorRate = totalRequests > 0 ? ((totalFailedRequests / totalRequests) * 100).toFixed(2) : '0.00';\n\n      if (minResponseTime === Infinity) {\n        minResponseTime = 0;\n      }\n\n      return {\n        name: '总体',\n        method: 'ALL',\n        totalRequests: totalRequests,\n        successRequests: totalSuccessRequests,\n        failedRequests: totalFailedRequests,\n        maxResponseTime: maxResponseTime,\n        minResponseTime: Math.round(minResponseTime),\n        avgResponseTime: avgResponseTime,\n        p50ResponseTime: p50ResponseTime, // 添加p50ResponseTime\n        p90ResponseTime: p90ResponseTime,\n        p95ResponseTime: p95ResponseTime, // 添加p95ResponseTime\n        p99ResponseTime: p99ResponseTime,\n        rps: totalRps.toFixed(2),\n        avgRps: totalRps.toFixed(2),\n        avgTps: totalTps.toFixed(2),\n        avgTpsAvg: totalTps.toFixed(2),\n        errorRate: errorRate,\n        currentUsers: totalUsers || this.getCurrentUserCount()  // 如果没有接口级用户数，使用全局用户数\n      };\n    },\n\n    // 计算总体统计\n    calculateTotalStats(detailedResults) {\n      const total = {\n        name: '总体',\n        method: 'ALL',\n        totalRequests: 0,\n        successRequests: 0,\n        failedRequests: 0,\n        maxResponseTime: 0,\n        minResponseTime: Infinity,\n        avgResponseTime: 0,\n        p50ResponseTime: 0, // 添加p50ResponseTime\n        p90ResponseTime: 0,\n        p95ResponseTime: 0, // 添加p95ResponseTime\n        p99ResponseTime: 0,\n        rps: 0,\n        avgRps: 0,\n        avgTps: 0,\n        avgTpsAvg: 0,\n        errorRate: 0\n      };\n\n      let totalResponseTime = 0;\n      let totalP50 = 0; // 添加p50累计\n      let totalP90 = 0;\n      let totalP95 = 0; // 添加p95累计\n      let totalP99 = 0;\n\n      detailedResults.forEach(item => {\n        total.totalRequests += item.totalRequests;\n        total.successRequests += item.successRequests;\n        total.failedRequests += item.failedRequests;\n        total.maxResponseTime = Math.max(total.maxResponseTime, item.maxResponseTime);\n        total.minResponseTime = Math.min(total.minResponseTime, item.minResponseTime);\n        total.rps += parseFloat(item.rps);\n        total.avgRps += parseFloat(item.avgRps);\n        total.avgTps += parseFloat(item.avgTps);\n        total.avgTpsAvg += parseFloat(item.avgTpsAvg);\n\n        // 加权平均响应时间\n        totalResponseTime += item.avgResponseTime * item.totalRequests;\n        totalP50 += item.p50ResponseTime * item.totalRequests; // 添加p50加权计算\n        totalP90 += item.p90ResponseTime * item.totalRequests;\n        totalP95 += item.p95ResponseTime * item.totalRequests; // 添加p95加权计算\n        totalP99 += item.p99ResponseTime * item.totalRequests;\n      });\n\n      if (total.totalRequests > 0) {\n        total.avgResponseTime = Math.round(totalResponseTime / total.totalRequests);\n        total.p50ResponseTime = Math.round(totalP50 / total.totalRequests); // 计算p50加权平均\n        total.p90ResponseTime = Math.round(totalP90 / total.totalRequests);\n        total.p95ResponseTime = Math.round(totalP95 / total.totalRequests); // 计算p95加权平均\n        total.p99ResponseTime = Math.round(totalP99 / total.totalRequests);\n        total.errorRate = Number((total.failedRequests / total.totalRequests) * 100).toFixed(2);\n      } else {\n        total.errorRate = '0.00';\n      }\n\n      if (total.minResponseTime === Infinity) {\n        total.minResponseTime = 0;\n      }\n\n      total.rps = Number(total.rps || 0).toFixed(2);\n      total.avgRps = Number(total.avgRps || 0).toFixed(2);\n      total.avgTps = Number(total.avgTps || 0).toFixed(2);\n      total.avgTpsAvg = Number(total.avgTpsAvg || 0).toFixed(2);\n\n      return total;\n    },\n\n\n    // 计算RPS\n    calculateRPS() {\n      if (!this.reportData) return '0.00'\n      // 如果有实时数据，使用实时RPS\n      if (this.realTimeData && this.realTimeData.total && typeof this.realTimeData.total.current_rps === 'number') {\n        return Number(this.realTimeData.total.current_rps).toFixed(2)\n      }\n      // 否则根据总请求数和时长计算\n      const duration = this.reportData.duration || 1\n      const totalRequests = this.reportData.totalRequests || 0\n      return Number(totalRequests / duration).toFixed(2)\n    },\n\n    // 计算平均RPS\n    calculateAvgRPS() {\n      if (!this.reportData) return '0.00'\n      const duration = this.reportData.duration || 1\n      const totalRequests = this.reportData.totalRequests || 0\n      return Number(totalRequests / duration).toFixed(2)\n    },\n\n    // 生成报告摘要\n    generateReportSummary() {\n      if (!this.reportData) return '暂无数据'\n\n      const avgRT = this.reportData.avgResponseTime || 0\n      const p50RT = this.reportData.p50ResponseTime || 0\n      const p90RT = this.reportData.p90ResponseTime || 0\n      const p95RT = this.reportData.p95ResponseTime || 0\n      const p99RT = this.reportData.p99ResponseTime || 0\n      const rps = this.calculateRPS()\n      const tps = this.reportData.avgTps || 0\n\n      return `平均响应时间为${avgRT}ms；50%响应时间线的值为${p50RT}ms；90%响应时间线的值为${p90RT}ms；95%响应时间线的值为${p95RT}ms；99%响应时间线的值为${p99RT}ms；RPS为${rps}；TPS为${tps}`\n    },\n\n    // 格式化时间\n    formatTime(time) {\n      if (!time) return '--'\n      return new Date(time).toLocaleString()\n    },\n\n    // 格式化响应时间\n    formatResponseTime(time) {\n      if (!time || typeof time !== 'number') return '0ms'\n      return time < 1000 ? `${Number(time).toFixed(2)}ms` : `${Number(time/1000).toFixed(2)}s`\n    },\n\n    // 格式化持续时间\n    formatDuration(seconds) {\n      if (!seconds) return '0s'\n      const hours = Math.floor(seconds / 3600)\n      const minutes = Math.floor((seconds % 3600) / 60)\n      const secs = seconds % 60\n      return `${hours}h ${minutes}m ${secs}s`\n    },\n\n    // 格式化字节\n    formatBytes(bytes) {\n      if (!bytes || typeof bytes !== 'number' || isNaN(bytes)) return '0B'\n      const k = 1024\n      const sizes = ['B', 'KB', 'MB', 'GB']\n      const i = Math.floor(Math.log(Math.max(1, bytes)) / Math.log(k))\n      const value = bytes / Math.pow(k, i)\n      return parseFloat(Number(value).toFixed(2)) + sizes[Math.min(i, sizes.length - 1)]\n    },\n\n    // 获取任务类型文本\n    getTaskTypeText(type) {\n      const types = { '10': '普通任务', '20': '定时任务' }\n      return types[type] || '未知'\n    },\n\n    // 获取运行模式文本\n    getRunPatternText(pattern) {\n      const patterns = { '10': '并发模式', '20': '阶梯模式' }\n      return patterns[pattern] || '未知'\n    },\n\n    // 获取分布式模式文本\n    getDistributedModeText(mode) {\n      const modes = { 'single': '单独模式', 'distributed': '集合模式' }\n      return modes[mode] || '未知'\n    },\n\n    // 获取状态文本\n    getStatusText(status) {\n      const statuses = { '1': '运行中', '0': '已完成', '99': '运行失败' }\n      return statuses[status] || '未知'\n    },\n\n    // 获取CPU级别样式\n    getCpuLevelClass(percent) {\n      if (percent > 80) return 'metric-danger'\n      if (percent > 60) return 'metric-warning'\n      return 'metric-normal'\n    },\n\n    // 获取内存级别样式\n    getMemoryLevelClass(percent) {\n      if (percent > 85) return 'metric-danger'\n      if (percent > 70) return 'metric-warning'\n      return 'metric-normal'\n    },\n\n    // 获取错误率样式\n    getErrorRateClass(rate) {\n      if (rate > 5) return 'metric-danger'\n      if (rate > 1) return 'metric-warning'\n      return 'metric-normal'\n    },\n\n\n\n    // 获取日志样式\n    getLogClass(level) {\n      return `log-${level}`\n    },\n\n    // 刷新监控数据\n    async refreshMonitoringData() {\n      await this.loadMonitoringData()\n      this.$message.success('监控数据已刷新')\n    },\n\n\n    // 切换自动刷新\n    toggleAutoRefresh() {\n      if (this.autoRefresh) {\n        // 只有在测试运行时才启动轮询\n        if (this.reportData.reportStatus === '1') {\n          this.refreshInterval = setInterval(() => {\n            this.loadMonitoringData()\n            this.loadTargetServiceData()\n            this.loadLogData()\n          }, 10000) // 每10秒刷新\n\n          // 如果测试正在运行，尝试重新连接WebSocket\n          this.initWebSocket()\n        } else {\n          // 如果测试已完成，提示用户并关闭自动刷新\n          this.$message.info('测试已完成，无需开启自动刷新')\n          this.autoRefresh = false\n          return\n        }\n      } else {\n        if (this.refreshInterval) {\n          clearInterval(this.refreshInterval)\n          this.refreshInterval = null\n        }\n        // 关闭WebSocket连接\n        this.cleanupWebSocket()\n      }\n    },\n\n    // 获取WebSocket状态文本\n    getWebSocketStatusText() {\n      switch (this.wsConnectionStatus) {\n        case 'connected':\n          return '实时监控'\n        case 'connecting':\n          return '连接中'\n        case 'disconnected':\n          return '已断开'\n        case 'error':\n          return '连接错误'\n        default:\n          return '未连接'\n      }\n    },\n\n    // 获取WebSocket状态类型\n    getWebSocketStatusType() {\n      switch (this.wsConnectionStatus) {\n        case 'connected':\n          return 'success'\n        case 'connecting':\n          return 'warning'\n        case 'disconnected':\n          return 'info'\n        case 'error':\n          return 'danger'\n        default:\n          return 'info'\n      }\n    },\n\n    // 处理WebSocket状态点击\n    handleWebSocketStatusClick() {\n      switch (this.wsConnectionStatus) {\n        case 'connected':\n          this.$message.success('WebSocket连接正常')\n          break\n        case 'connecting':\n          this.$message.info('正在连接WebSocket...')\n          break\n        case 'disconnected':\n        case 'error':\n          this.$confirm('WebSocket连接已断开，是否尝试重新连接？', '连接提示', {\n            confirmButtonText: '重新连接',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(() => {\n            this.reconnectWebSocket()\n          }).catch(() => {\n            this.$message.info('已取消重连')\n          })\n          break\n        default:\n          if (this.reportData.reportStatus === '1') {\n            this.initWebSocket()\n          } else {\n            this.$message.info('测试未运行，无需实时监控')\n          }\n      }\n    },\n\n    startEditing() {\n      this.inputDlg = true;\n      this.editingField = 'desc';\n      this.$nextTick(() => {\n        if (this.$refs.input) {\n          this.$refs.input.focus();\n        }\n      });\n    },\n    editTaskName() {\n      this.inputDlg = true;\n      this.editingField = 'taskName';\n      this.tempTaskName = this.taskName; // 保存原值以便取消时恢复\n      this.$nextTick(() => {\n        if (this.$refs.input) {\n          this.$refs.input.focus();\n        }\n      });\n    },\n    cancelEditing() {\n      this.inputDlg = false;\n      // 如果是编辑任务名称且未保存，恢复原值\n      if (this.editingField === 'taskName' && this.tempTaskName) {\n        this.taskName = this.tempTaskName;\n      }\n    },\n\n    // 保存任务名称\n    async saveTaskName() {\n      this.inputDlg = false;\n\n      // 如果名称没有变化，不需要保存\n      if (this.taskName === this.reportData.reportName) {\n        return;\n      }\n      this.saveReport()\n    },\n\n    // 保存分析结果\n    async saveAnalysis() {\n      this.inputDlg = false;\n\n      // 如果内容没有变化，不需要保存\n      if (this.desc === this.reportData.resultAnalyse) {\n        return;\n      }\n      this.saveReport()\n\n    },\n    handleSelect(index) {\n      this.activeIndex = index;\n\n      // 根据选择的标签页加载对应数据\n      if (index === '1') {\n        // 指标详情页 - 总是重新加载指标数据，以确保数据完整性\n        if (this.reportData && this.reportData.reportResult) {\n          // 重新生成图表数据和指标列表\n          this.generateChartData();\n          \n          // 强制更新表格组件以确保数据显示\n          this.$nextTick(() => {\n            const tableElement = this.$refs.table;\n            if (tableElement) {\n              tableElement.$forceUpdate();\n            }\n            // 也强制更新组件本身\n            this.$forceUpdate();\n          });\n        }\n      } else if (index === '3' && !this.monitoringDataLoaded) {\n        this.loadMonitoringData()\n        this.monitoringDataLoaded = true\n\n        // 如果测试正在运行，启动定时刷新监控数据\n        if (this.reportData.reportStatus === '1' && this.autoRefresh) {\n          this.startMonitoringRefresh()\n        }\n      } else if (index === '4' && !this.targetServiceDataLoaded) {\n        this.loadTargetServiceData()\n        this.targetServiceDataLoaded = true\n\n        // 如果测试正在运行，启动定时刷新目标服务数据\n        if (this.reportData.reportStatus === '1' && this.autoRefresh) {\n          this.startTargetServiceRefresh()\n        }\n      } else if (index === '5') {\n        if (!this.logDataLoaded) {\n          this.loadLogData()\n          this.logDataLoaded = true\n        } else {\n          // 如果日志数据已加载，只需滚动到底部\n          this.scrollToLogBottom();\n        }\n      }\n\n      // 离开监控页面时停止刷新\n      if (index !== '3') {\n        this.stopMonitoringRefresh()\n      }\n      if (index !== '4') {\n        this.stopTargetServiceRefresh()\n      }\n    },\n\n    // 启动监控数据刷新\n    startMonitoringRefresh() {\n      if (this.monitoringRefreshInterval) {\n        clearInterval(this.monitoringRefreshInterval)\n      }\n      this.monitoringRefreshInterval = setInterval(() => {\n        this.loadMonitoringData()\n      }, 5000) // 每5秒刷新一次\n    },\n\n    // 停止监控数据刷新\n    stopMonitoringRefresh() {\n      if (this.monitoringRefreshInterval) {\n        clearInterval(this.monitoringRefreshInterval)\n        this.monitoringRefreshInterval = null\n      }\n    },\n\n    // 启动目标服务数据刷新\n    startTargetServiceRefresh() {\n      if (this.targetServiceRefreshInterval) {\n        clearInterval(this.targetServiceRefreshInterval)\n      }\n      this.targetServiceRefreshInterval = setInterval(() => {\n        this.loadTargetServiceData()\n      }, 5000) // 每5秒刷新一次\n    },\n\n    // 停止目标服务数据刷新\n    stopTargetServiceRefresh() {\n      if (this.targetServiceRefreshInterval) {\n        clearInterval(this.targetServiceRefreshInterval)\n        this.targetServiceRefreshInterval = null\n      }\n    },\n\n    back() {\n      window.history.back();\n    },\n\n    // 处理导出命令\n    async handleExportCommand(command) {\n      if (!this.reportData.id) {\n        this.$message.warning('请先加载报告数据');\n        return;\n      }\n\n      // 只有Excel导出功能已实现，其他功能显示友好提示\n      if (command !== 'excel') {\n        this.$message.info(`${command === 'html' ? 'HTML报告' : command === 'pdf' ? 'PDF报告' : '原始数据'}导出功能即将上线，敬请期待！`);\n        return;\n      }\n\n      this.tableLoading = true;\n      try {\n        let response;\n        let filename = `性能测试报告_${this.reportData.reportName}_${new Date().toISOString().slice(0, 10)}`;\n\n        switch (command) {\n          case 'excel':\n            response = await this.$api.exportSingleReport(this.reportData.id);\n            filename += '.xlsx';\n            break;\n          case 'html':\n            // 使用模拟的HTML导出\n            response = await this.mockGenerateHtmlReport();\n            filename += '.html';\n            break;\n          case 'pdf':\n            // 使用模拟的PDF导出\n            response = await this.mockGeneratePdfReport();\n            filename += '.pdf';\n            break;\n          case 'raw':\n            response = await this.$api.exportTestData({\n              report_id: this.reportData.id,\n              format: 'json'\n            });\n            filename += '.json';\n            break;\n          default:\n            this.$message.warning('未知的导出格式');\n            return;\n        }\n\n        if (response && response.status === 200) {\n          // 创建下载链接\n          const blob = new Blob([response.data]);\n          const link = document.createElement('a');\n          link.href = URL.createObjectURL(blob);\n          link.download = filename;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          URL.revokeObjectURL(link.href);\n\n          this.$message.success('报告导出成功');\n        }\n      } catch (error) {\n        console.error('导出失败:', error);\n        this.$message.error('导出失败: ' + (error.response?.data?.message || error.message || '未知错误'));\n      } finally {\n        this.tableLoading = false;\n      }\n    },\n\n    // 模拟HTML报告生成\n    async mockGenerateHtmlReport() {\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const htmlContent = this.generateHtmlReportContent();\n          resolve({\n            status: 200,\n            data: htmlContent\n          });\n        }, 1000);\n      });\n    },\n\n    // 模拟PDF报告生成\n    async mockGeneratePdfReport() {\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const pdfContent = `PDF报告内容 - ${this.reportData.reportName}`;\n          resolve({\n            status: 200,\n            data: pdfContent\n          });\n        }, 1500);\n      });\n    },\n\n\n    // 处理通知命令\n    async handleNotificationCommand(command) {\n      this.showNotificationDialog(command);\n    },\n\n    // 显示通知配置对话框\n    showNotificationDialog() {\n      // 初始化通知数据\n      this.notificationDialogData = {\n        visible: true,\n        pushType: 'wechat', // 默认选择微信\n        name: `${this.reportData.reportName}性能测试报告通知`,\n        webhook: '',\n        recipients: [],\n      };\n    },\n\n    // 取消通知\n    cancelNotification() {\n      this.notificationDialogData.visible = false;\n      this.$refs.notificationFormRef?.resetFields();\n    },\n\n    // 确认发送通知\n    async confirmSendNotification() {\n      if (!this.$refs.notificationFormRef) return;\n      \n      this.$refs.notificationFormRef.validate(async (valid) => {\n        if (valid) {\n          this.notificationDialogData.visible = false;\n          await this.sendNotification(this.notificationDialogData.pushType);\n        } else {\n          return false;\n        }\n      });\n    },\n\n    // 发送通知\n    async sendNotification(type) {\n      this.$message.info('敬请期待！通知功能正在开发中...');\n    },\n\n\n    // 模拟发送通知（用于演示）\n    async mockSendNotification(params) {\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          resolve({ status: 200, data: { message: '通知发送成功' } });\n        }, 1000);\n      });\n    },\n    \n    // 生成通知内容\n    generateNotificationContent() {\n      // 格式化性能测试报告的通知内容\n      const data = this.reportData;\n      const formattedTime = this.formatTime(data.endTime || data.createTime || new Date());\n      const statusText = this.getStatusText(data.reportStatus);\n      const successRate = this.getSuccessRate();\n      \n      let content = `## 性能测试报告: ${data.reportName}\\n\\n`;\n      content += `**测试状态**: ${statusText}\\n`;\n      content += `**完成时间**: ${formattedTime}\\n\\n`;\n      \n      content += `### 性能指标\\n`;\n      content += `- 平均响应时间: ${this.formatResponseTime(data.avgResponseTime)}\\n`;\n      content += `- 平均TPS: ${(data.avgTps || 0).toFixed(2)}\\n`;\n      content += `- 成功率: ${successRate}%\\n`;\n      \n      if (data.maxResponseTime) {\n        content += `- 最大响应时间: ${this.formatResponseTime(data.maxResponseTime)}\\n`;\n      }\n      \n      if (data.totalRequests) {\n        content += `- 总请求数: ${data.totalRequests}\\n`;\n      }\n      \n      if (data.duration) {\n        content += `- 测试持续时间: ${data.duration}秒\\n`;\n      }\n      \n      if (data.maxUsers || data.avgUsers) {\n        content += `- 并发用户数: ${data.maxUsers || data.avgUsers}\\n`;\n      }\n      \n      if (data.avgCpu || data.avgMemory) {\n        content += `\\n### 资源使用情况\\n`;\n        if (data.avgCpu) {\n          content += `- 平均CPU使用率: ${data.avgCpu}%\\n`;\n        }\n        if (data.avgMemory) {\n          content += `- 平均内存使用率: ${data.avgMemory}%\\n`;\n        }\n      }\n      \n      // 添加测试结果分析\n      if (this.desc && this.desc !== '暂无分析结果') {\n        content += `\\n### 分析结果\\n`;\n        content += this.desc.split('\\n').map(line => `> ${line}`).join('\\n');\n      }\n      \n      return content;\n    },\n\n    refreshGUI() {\n      if (this.guiUrl) {\n        // 强制刷新iframe\n        const iframe = document.querySelector('.iframe-container iframe');\n        if (iframe) {\n          // 重新赋值src强制刷新\n          iframe.src = this.guiUrl;\n        }\n        this.iframeError = false;\n        this.iframeErrorMessage = '';\n      } else {\n        this.$message.warning('没有可用的GUI URL');\n      }\n    },\n\n    handleIframeLoad() {\n      this.iframeError = false;\n      this.iframeErrorMessage = '';\n    },\n\n    handleIframeError() {\n      this.iframeError = true;\n      this.iframeErrorMessage = `无法加载GUI界面：${this.guiUrl}，请检查服务器状态`;\n    },\n\n    retryLoadGUI() {\n      this.refreshGUI();\n    },\n\n\n    // 测试状态相关方法\n    getTestStatusType() {\n      if (this.isTestRunning) {\n        return 'info';\n      } else if (this.reportData.reportStatus === '0') {\n        return 'success';\n      } else {\n        return 'warning';\n      }\n    },\n\n    getTestStatusText() {\n      if (this.isTestRunning) {\n        return '运行中';\n      } else if (this.reportData.reportStatus === '0') {\n        return '已完成';\n      } else {\n        return '未运行';\n      }\n    },\n\n    getTestNotRunningMessage() {\n      if (this.reportData.reportStatus === '0') {\n        return 'GUI界面只在测试运行时可用。此测试已完成，无法显示实时GUI界面。';\n      }\n      return 'GUI界面需要在性能测试运行时才能访问。请先启动性能测试，然后返回查看GUI监控界面。';\n    },\n\n    // 不再需要获取服务器名称方法\n\n\n    // 处理更多操作命令\n    async handleMoreCommand(command) {\n      try {\n        switch (command) {\n          case 'baseline':\n            await this.createBaseline();\n            break;\n          case 'compare':\n            await this.compareWithBaseline();\n            break;\n          case 'analyze':\n            await this.analyzePerformance();\n            break;\n          case 'config':\n            this.showTestConfig();\n            break;\n          default:\n            this.$message.warning('未知的操作类型');\n        }\n      } catch (error) {\n        console.error('操作失败:', error);\n        this.$message.error('操作失败: ' + (error.message || '未知错误'));\n      }\n    },\n\n    // 创建基准线\n    async createBaseline() {\n      try {\n        // 打开基准线创建对话框\n        this.baselineDialogData = {\n          visible: true,\n          loading: false,\n          mode: 'create',\n          form: {\n          name: `${this.reportData.reportName}_基准线`,\n          description: '基于当前报告创建的性能基准线',\n            task_id: this.reportData.task?.id || this.reportData.taskId,\n            avg_response_time: this.reportData.avgResponseTime || 0,\n            avg_tps: this.reportData.avgTps || 0,\n            success_rate: this.getSuccessRate() || 0,\n            avg_cpu: this.reportData.avgCpu || 0,\n            avg_memory: this.reportData.avgMemory || 0,\n            is_active: true,\n            project_id: this.reportData.project?.id || this.$route.params.projectId\n          }\n        };\n      } catch (error) {\n        this.$message.error('准备创建基准线失败: ' + (error.response?.data?.message || error.message || '未知错误'));\n      }\n    },\n    \n    // 提交创建基准线表单\n    async submitBaselineForm() {\n      try {\n        // 表单验证\n        await this.$refs.baselineFormRef.validate();\n        \n        this.baselineDialogData.loading = true;\n        \n        const params = { ...this.baselineDialogData.form };\n        \n        // 添加报告ID (自动创建基准线API需要)\n        if (this.baselineDialogData.mode === 'create') {\n          params.report_id = this.reportData.id;\n        }\n        \n        // 调用API\n        const response = await this.$api.createBaseline(params);\n\n        if (response.status === 200 || response.status === 201) {\n          this.$message.success('基准线创建成功');\n          this.baselineDialogData.visible = false;\n        }\n      } catch (error) {\n        this.$message.error('创建基准线失败: ' + (error.response?.data?.message || error.message || '未知错误'));\n      } finally {\n        this.baselineDialogData.loading = false;\n      }\n    },\n\n    // 与基准线对比\n    async compareWithBaseline() {\n      try {\n        // 获取基准线列表\n        const response = await this.$api.getBaselines({\n          project_id: this.reportData.project?.id || this.$route.params.projectId,\n          task_id: this.reportData.task?.id || this.reportData.taskId,\n          is_active: true\n          });\n\n          if (response.status === 200) {\n          let baselineList = [];\n          \n          // 处理不同的数据结构\n          if (response.data.results && Array.isArray(response.data.results)) {\n            baselineList = response.data.results;\n          } else if (Array.isArray(response.data)) {\n            baselineList = response.data;\n          } else if (response.data.baselines && Array.isArray(response.data.baselines)) {\n            baselineList = response.data.baselines;\n          }\n\n          if (baselineList.length > 0) {\n            // 打开基准线选择对话框\n            this.baselineCompareDialogData = {\n              visible: true,\n              loading: false,\n              baselineList,\n              selectedBaselineId: baselineList[0].id\n            };\n        } else {\n          this.$message.warning('暂无可用的基准线，请先创建基准线');\n          }\n        }\n      } catch (error) {\n        this.$message.error('获取基准线列表失败: ' + (error.response?.data?.message || error.message || '未知错误'));\n      }\n    },\n\n    // 提交基准线对比\n    async submitCompareBaseline() {\n      try {\n        if (!this.baselineCompareDialogData.selectedBaselineId) {\n          this.$message.warning('请选择要对比的基准线');\n          return;\n        }\n\n        this.baselineCompareDialogData.loading = true;\n\n        try {\n          // 直接使用选中的基准线数据，避免调用可能存在问题的API\n          const selectedBaseline = this.baselineCompareDialogData.baselineList.find(\n            baseline => baseline.id === this.baselineCompareDialogData.selectedBaselineId\n          );\n\n\n          let baselineData = {};\n          if (selectedBaseline) {\n            baselineData = selectedBaseline;\n          } else {\n            // 如果找不到基准线数据，尝试从API获取，但可能失败\n            try {\n              const baselineDetailResponse = await this.$api.getBaselineDetail(this.baselineCompareDialogData.selectedBaselineId);\n\n              \n              // 提取基准线数据\n              if (baselineDetailResponse.status === 200) {\n                if (baselineDetailResponse.data.baseline) {\n                  baselineData = baselineDetailResponse.data.baseline;\n                } else if (baselineDetailResponse.data) {\n                  baselineData = baselineDetailResponse.data;\n                }\n              }\n            } catch (detailError) {\n              console.error('获取基准线详情失败:', detailError);\n              // 不阻止继续执行，继续使用空的baselineData\n            }\n          }\n\n          // 调用对比API\n          const response = await this.$api.compareWithBaseline(this.reportData.id, {\n            baseline_id: this.baselineCompareDialogData.selectedBaselineId\n          });\n\n          if (response.status === 200) {\n            // 关闭选择对话框\n            this.baselineCompareDialogData.visible = false;\n            \n            // 处理对比结果数据\n            const comparisonResult = response.data;\n            \n            // 使用基准线详情数据构建指标\n            const baselineMetrics = {\n              avg_response_time: baselineData.response_time !== undefined ? this.safeParseFloat(baselineData.response_time) : this.safeParseFloat(baselineData.avg_response_time),\n              avg_tps: baselineData.tps !== undefined ? this.safeParseFloat(baselineData.tps) : this.safeParseFloat(baselineData.avg_tps),\n              success_rate: baselineData.error_rate !== undefined ? this.safeParseFloat(100 - baselineData.error_rate) : this.safeParseFloat(baselineData.success_rate),\n              avg_cpu: this.safeParseFloat(baselineData.cpu_usage || baselineData.avg_cpu),\n              avg_memory: this.safeParseFloat(baselineData.memory_usage || baselineData.avg_memory)\n            };\n\n            // 确保API返回的基准线指标可用\n            let apiBaselineMetrics = null;\n            if (comparisonResult.baseline_metrics) {\n              apiBaselineMetrics = {\n                avg_response_time: this.safeParseFloat(comparisonResult.baseline_metrics.avg_response_time || comparisonResult.baseline_metrics.response_time),\n                avg_tps: this.safeParseFloat(comparisonResult.baseline_metrics.avg_tps || comparisonResult.baseline_metrics.tps),\n                success_rate: comparisonResult.baseline_metrics.success_rate !== undefined ? \n                  this.safeParseFloat(comparisonResult.baseline_metrics.success_rate) : \n                  (comparisonResult.baseline_metrics.error_rate !== undefined ? \n                    this.safeParseFloat(100 - comparisonResult.baseline_metrics.error_rate) : 0),\n                avg_cpu: this.safeParseFloat(comparisonResult.baseline_metrics.avg_cpu || comparisonResult.baseline_metrics.cpu_usage),\n                avg_memory: this.safeParseFloat(comparisonResult.baseline_metrics.avg_memory || comparisonResult.baseline_metrics.memory_usage)\n              };\n\n            }\n\n            // 如果API返回的基准线指标中有有效值，优先使用它们，否则使用从基准线详情构建的指标\n            const finalBaselineMetrics = apiBaselineMetrics || baselineMetrics;\n\n            \n            // 显示对比结果对话框\n            this.baselineCompareResultData = {\n              visible: true,\n              baseline_metrics: finalBaselineMetrics,\n              current_metrics: comparisonResult.current_metrics || {\n                avg_response_time: this.reportData.avgResponseTime || 0,\n                avg_tps: this.reportData.avgTps || 0,\n                success_rate: this.getSuccessRate() || 0,\n                avg_cpu: this.reportData.avgCpu || 0,\n                avg_memory: this.reportData.avgMemory || 0\n              },\n              conclusion: comparisonResult.conclusion || ''\n            };\n          }\n        } catch (error) {\n          console.error(\"API调用失败，使用模拟数据:\", error);\n          \n          // 如果API调用失败，使用模拟数据\n          this.baselineCompareDialogData.visible = false;\n          \n          // 使用报告数据模拟对比结果\n          this.baselineCompareResultData = {\n            visible: true,\n            baseline_metrics: {\n              avg_response_time: 180.50,\n              avg_tps: 85.75,\n              success_rate: 99.2,\n              avg_cpu: 45.3, \n              avg_memory: 60.8\n            },\n            current_metrics: {\n              avg_response_time: this.reportData.avgResponseTime || 200,\n              avg_tps: this.reportData.avgTps || 80,\n              success_rate: this.getSuccessRate() || 98,\n              avg_cpu: this.reportData.avgCpu || 50,\n              avg_memory: this.reportData.avgMemory || 65\n            },\n            conclusion: ''\n          };\n        }\n      } catch (error) {\n        this.$message.error('基准线对比失败: ' + (error.response?.data?.message || error.message || '未知错误'));\n      } finally {\n        this.baselineCompareDialogData.loading = false;\n      }\n    },\n\n    // 性能分析\n    async analyzePerformance() {\n      try {\n        // 使用实际报告数据进行分析\n        const analysisResult = this.generateRealAnalysis(this.reportData);\n\n          this.$message.success('性能分析完成，请查看分析结果');\n\n\n        // 显示分析结果对话框\n        this.showAnalysisResults(analysisResult);\n      } catch (error) {\n        this.$message.error('性能分析失败: ' + (error.message || '未知错误'));\n      }\n    },\n\n    // 基于实际报告数据生成性能分析\n    generateRealAnalysis(reportData) {\n      // 计算性能得分\n      let performanceScore = 0;\n      const bottlenecks = [];\n      const recommendations = [];\n      \n      // 分析响应时间\n      const avgResponseTime = this.safeParseFloat(reportData.avgResponseTime);\n      if (avgResponseTime > 0) {\n        // 根据响应时间评分 (小于100ms非常好，100-300ms好，300-500ms一般，>500ms需改进)\n        let responseTimeScore = 0;\n        if (avgResponseTime < 100) {\n          responseTimeScore = 25;\n        } else if (avgResponseTime < 300) {\n          responseTimeScore = 20;\n          recommendations.push('当前响应时间表现良好，但仍有优化空间。考虑优化数据库查询和索引。');\n        } else if (avgResponseTime < 500) {\n          responseTimeScore = 15;\n          bottlenecks.push({ \n            type: '响应时间', \n            severity: 'medium', \n            description: `平均响应时间(${avgResponseTime.toFixed(2)}ms)偏高，建议优化数据库查询和应用代码`\n          });\n          recommendations.push('优化关键API响应时间，检查慢查询并优化数据库索引。');\n        } else {\n          responseTimeScore = 10;\n          bottlenecks.push({ \n            type: '响应时间', \n            severity: 'high', \n            description: `平均响应时间(${avgResponseTime.toFixed(2)}ms)过高，严重影响用户体验`\n          });\n          recommendations.push('紧急优化响应时间：检查并优化数据库查询，考虑增加缓存层，优化应用代码。');\n        }\n        performanceScore += responseTimeScore;\n      }\n      \n      // 分析TPS\n      const avgTps = this.safeParseFloat(reportData.avgTps);\n      if (avgTps > 0) {\n        // 基于并发用户数评估TPS表现\n        const userCount = this.safeParseFloat(reportData.maxUsers || reportData.avgUsers);\n        const expectedTps = Math.max(userCount / 3, 1); // 简单估算：每个用户每3秒发起一个请求\n        \n        let tpsScore = 0;\n        if (avgTps >= expectedTps * 1.5) {\n          tpsScore = 25;\n          recommendations.push('系统吞吐量表现优秀，可以考虑进一步增加并发用户数进行压测。');\n        } else if (avgTps >= expectedTps) {\n          tpsScore = 20;\n          recommendations.push('系统吞吐量表现良好，符合预期。');\n        } else if (avgTps >= expectedTps * 0.7) {\n          tpsScore = 15;\n          bottlenecks.push({ \n            type: 'TPS', \n            severity: 'medium', \n            description: `平均TPS(${avgTps.toFixed(2)})低于预期，系统吞吐能力有待提高`\n          });\n          recommendations.push('提高系统吞吐量：优化关键服务代码，考虑使用异步处理非关键请求。');\n        } else {\n          tpsScore = 10;\n          bottlenecks.push({ \n            type: 'TPS', \n            severity: 'high', \n            description: `平均TPS(${avgTps.toFixed(2)})远低于预期，系统吞吐能力严重不足`\n          });\n          recommendations.push('系统吞吐量亟需提升：检查系统瓶颈，考虑服务扩容或重构关键组件。');\n        }\n        performanceScore += tpsScore;\n      }\n      \n      // 分析错误率\n      const errorRate = this.safeParseFloat(reportData.errorRate);\n      let successRate = 100 - errorRate;\n      if (errorRate !== undefined) {\n        let errorScore = 0;\n        if (errorRate < 0.1) {\n          errorScore = 25;\n        } else if (errorRate < 1) {\n          errorScore = 20;\n        } else if (errorRate < 5) {\n          errorScore = 15;\n          bottlenecks.push({ \n            type: '错误率', \n            severity: 'medium', \n            description: `错误率(${errorRate.toFixed(2)}%)略高，影响系统稳定性`\n          });\n          recommendations.push('改善错误处理机制，增加服务异常监控，优化异常情况下的重试逻辑。');\n        } else {\n          errorScore = 5;\n          bottlenecks.push({ \n            type: '错误率', \n            severity: 'high', \n            description: `错误率(${errorRate.toFixed(2)}%)过高，系统稳定性差`\n          });\n          recommendations.push('紧急修复高错误率问题：完善错误处理，增加容错机制，优化系统稳定性。');\n        }\n        performanceScore += errorScore;\n      }\n      \n      // 分析资源使用情况\n      const avgCpu = this.safeParseFloat(reportData.avgCpu);\n      if (avgCpu > 0) {\n        let cpuScore = 0;\n        if (avgCpu < 60) {\n          cpuScore = 15;\n        } else if (avgCpu < 80) {\n          cpuScore = 10;\n          bottlenecks.push({ \n            type: 'CPU使用率', \n            severity: 'low', \n            description: `CPU使用率(${avgCpu.toFixed(2)}%)较高，但仍在可接受范围内`\n          });\n        } else {\n          cpuScore = 5;\n          bottlenecks.push({ \n            type: 'CPU使用率', \n            severity: 'high', \n            description: `CPU使用率(${avgCpu.toFixed(2)}%)过高，可能成为系统瓶颈`\n          });\n          recommendations.push('降低CPU使用率：优化计算密集型代码，考虑增加服务器资源或负载均衡。');\n        }\n        performanceScore += cpuScore;\n      }\n      \n      const avgMemory = this.safeParseFloat(reportData.avgMemory);\n      if (avgMemory > 0) {\n        let memoryScore = 0;\n        if (avgMemory < 70) {\n          memoryScore = 10;\n        } else if (avgMemory < 85) {\n          memoryScore = 5;\n          bottlenecks.push({ \n            type: '内存使用率', \n            severity: 'medium', \n            description: `内存使用率(${avgMemory.toFixed(2)}%)较高，接近警戒线`\n          });\n          recommendations.push('优化内存使用：检查内存泄漏，优化大对象处理，考虑增加内存容量。');\n        } else {\n          memoryScore = 0;\n          bottlenecks.push({ \n            type: '内存使用率', \n            severity: 'high', \n            description: `内存使用率(${avgMemory.toFixed(2)}%)过高，存在OOM风险`\n          });\n          recommendations.push('紧急处理内存问题：排查内存泄漏，优化大数据处理逻辑，增加服务器内存。');\n        }\n        performanceScore += memoryScore;\n      }\n      \n      // 如果没有足够的数据计算得分，使用默认值\n      if (performanceScore === 0) {\n        performanceScore = 70; // 默认得分\n      }\n      \n      // 如果没有发现瓶颈，添加一个积极的评价\n      if (bottlenecks.length === 0) {\n        bottlenecks.push({ \n          type: '系统表现', \n          severity: 'low', \n          description: '未发现明显性能瓶颈，系统整体表现良好'\n        });\n      }\n      \n      // 如果没有建议，添加一些通用建议\n      if (recommendations.length === 0) {\n        recommendations.push('持续监控系统性能，定期进行压力测试以保持性能水平。');\n        recommendations.push('考虑建立性能基准线，用于未来性能对比和评估。');\n      }\n      \n      // 根据实际数据生成趋势分析\n      const trendAnalysis = {\n        response_time_trend: avgResponseTime < 300 ? '良好' : (avgResponseTime < 500 ? '需关注' : '需优化'),\n        error_rate_trend: errorRate < 1 ? '稳定' : (errorRate < 5 ? '波动' : '不稳定'),\n        tps_trend: avgTps > 0 ? (avgTps > 50 ? '高' : '中等') : '低'\n      };\n      \n      // 添加针对并发用户数的建议\n      if (reportData.maxUsers > 0) {\n        if (errorRate < 1 && avgCpu < 70 && avgMemory < 70) {\n          recommendations.push(`当前并发用户数(${reportData.maxUsers})下系统表现良好，可以考虑提高并发数进行更大规模测试。`);\n        } else if (errorRate > 5 || avgCpu > 85 || avgMemory > 85) {\n          recommendations.push(`当前并发用户数(${reportData.maxUsers})已接近系统负载上限，建议优化系统后再增加并发。`);\n        }\n      }\n      \n      return {\n        performance_score: Math.min(Math.round(performanceScore), 100),\n        bottlenecks: bottlenecks,\n        recommendations: recommendations,\n        trend_analysis: trendAnalysis\n      };\n    },\n\n    // 显示分析结果\n    showAnalysisResults(analysisData) {\n      const { performance_score, bottlenecks, recommendations } = analysisData;\n\n      // 将数据存储到组件状态中\n      this.analysisDialogData = {\n        visible: true,\n        performance_score,\n        bottlenecks,\n        recommendations\n      };\n    },\n\n    // 显示测试配置\n    showTestConfig() {\n      if (!this.reportData.task && !this.reportData.taskId) {\n        this.$message.warning('无法获取测试配置信息');\n        return;\n      }\n\n      this.showTestConfigDialog();\n    },\n\n    // 显示测试配置对话框\n    async showTestConfigDialog() {\n      try {\n        // 如果没有详细的任务配置，尝试获取\n        let taskConfig = this.reportData.task;\n\n\n        // 将配置数据存储到组件状态中\n        this.configDialogData = {\n          visible: true,\n          taskConfig\n        };\n\n      } catch (error) {\n        console.error('获取配置失败:', error);\n        this.$message.error('获取配置失败: ' + (error.response?.data?.message || error.message || '未知错误'));\n      }\n    },\n\n    // 获取当前CPU使用率\n    getCurrentCpuUsage() {\n      // 优先使用实时监控数据\n      if (this.monitoringData && typeof this.monitoringData.cpu_percent === 'number') {\n        return Number(this.monitoringData.cpu_percent).toFixed(1);\n      }\n      // 其次使用报告数据\n      if (this.reportData && typeof this.reportData.avgCpu === 'number') {\n        return Number(this.reportData.avgCpu).toFixed(1);\n      }\n      return '0.0';\n    },\n\n    // 获取表格行的用户数\n    getUserCountForRow(row) {\n      // 如果是总体行，显示当前用户数\n      if (row.method === 'ALL') {\n        return this.getCurrentUserCount();\n      }\n      \n      // 对于具体接口行，如果有接口级别的用户数，使用它\n      if (row.currentUsers) {\n        return row.currentUsers;\n      }\n      \n      // 否则显示总体用户数（因为用户数是全局的）\n      return this.getCurrentUserCount();\n    },\n\n    // 获取当前用户数\n    getCurrentUserCount() {\n      // 优先使用实时数据中的用户数\n      if (this.realTimeData && this.realTimeData.total && this.realTimeData.total.current_users) {\n        return this.realTimeData.total.current_users;\n      }\n      \n      // 其次使用报告数据中的最大用户数（数据库存储）\n      if (this.reportData.maxUsers) {\n        return this.reportData.maxUsers;\n      }\n      \n      // 再次使用平均用户数（数据库存储）\n      if (this.reportData.avgUsers) {\n        return Math.round(this.reportData.avgUsers);\n      }\n      \n      // 最后从任务配置中获取设置的并发数\n      if (this.reportData.task) {\n        if (this.reportData.task.concurrencyNumber) {\n          return this.reportData.task.concurrencyNumber;\n        }\n        if (this.reportData.task.users) {\n          return this.reportData.task.users;\n        }\n      }\n      \n      return '-';\n    },\n\n    // 获取当前内存使用率\n    getCurrentMemoryUsage() {\n      // 优先使用实时监控数据\n      if (this.monitoringData && typeof this.monitoringData.memory_percent === 'number') {\n        return Number(this.monitoringData.memory_percent).toFixed(1);\n      }\n      // 其次使用报告数据\n      if (this.reportData && typeof this.reportData.avgMemory === 'number') {\n        return Number(this.reportData.avgMemory).toFixed(1);\n      }\n      return '0.0';\n    },\n\n    // 获取正确的接口名称 - 简化逻辑，直接使用Locust的格式\n    getCorrectInterfaceName(key, stats) {\n      // 优先使用stats中的name字段（接口路径）\n      if (stats.name) {\n        return stats.name;\n      }\n      \n      // 如果stats中有path字段，使用path\n      if (stats.path) {\n        return stats.path;\n      }\n      \n      // 如果key是 \"GET /api/test\" 格式，提取路径部分\n      const httpMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];\n      for (const method of httpMethods) {\n        if (key.startsWith(method + ' ')) {\n          return key.substring(method.length + 1);\n        }\n      }\n      \n      // 否则直接返回key\n      return key;\n    },\n\n    // 获取正确的HTTP方法 - 简化逻辑\n    getCorrectHttpMethod(key, stats) {\n      // 优先使用stats中的method字段\n      if (stats.method) {\n        return stats.method;\n      }\n      \n      // 从key中提取HTTP方法\n      const httpMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];\n      for (const method of httpMethods) {\n        if (key.startsWith(method + ' ')) {\n          return method;\n        }\n      }\n      \n      return 'N/A';\n    },\n\n    // 检查是否为HTTP方法\n    isHttpMethod(value) {\n      const httpMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];\n      return httpMethods.includes(value?.toUpperCase());\n    },\n\n    // 从key中提取接口名称\n    extractInterfaceName(key, stats) {\n      // 如果stats中有path字段，优先使用\n      if (stats.path) {\n        return stats.path;\n      }\n\n      // 如果key包含HTTP方法，提取路径部分\n      const httpMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];\n      for (const method of httpMethods) {\n        if (key.startsWith(method + ' ')) {\n          return key.substring(method.length + 1);\n        }\n      }\n\n      // 如果没有HTTP方法前缀，直接返回key\n      return key;\n    },\n\n    // 获取得分颜色\n    getScoreColor(score) {\n      if (score >= 80) return '#67c23a';\n      if (score >= 60) return '#e6a23c';\n      return '#f56c6c';\n    },\n\n    // 获取严重程度类型\n    getSeverityType(severity) {\n      const typeMap = {\n        'low': 'success',\n        'medium': 'warning',\n        'high': 'danger'\n      };\n      return typeMap[severity] || 'info';\n    },\n\n    // 保存报告\n    async saveReport() {\n      try {\n        const params = {\n          reportName: this.taskName,\n          resultAnalyse: this.desc\n        };\n\n        // 使用正确的API方法\n        const response = await this.$api.updateTaskReportDetail(this.reportData.id, params);\n\n        if (response.status === 200) {\n          this.$message.success('保存成功');\n          // 更新本地数据\n          this.reportData.reportName = this.taskName;\n          this.reportData.resultAnalyse = this.desc;\n        }\n      } catch (error) {\n        console.error('保存失败:', error);\n        this.$message.error('保存失败: ' + (error.response?.data?.message || error.message || '未知错误'));\n      }\n    },\n\n    // 处理行点击\n    handleRowClick(row, column, event) {\n      // 如果点击的是复选框列，不做处理\n      if (column.type === 'selection') {\n        return;\n      }\n      \n      // 仅当不是筛选模式时，点击行可以切换选择状态\n      if (!this.isFilterMode) {\n        // 切换行的选择状态\n        this.$refs.table.toggleRowSelection(row);\n      }\n    },\n\n    // 处理表格选择变化\n    handleSelectionChange(val) {\n      // 只更新选中的行，不做模式切换和弹窗\n      this.selectedRows = val;\n    },\n\n    // 切换筛选模式\n    toggleFilterMode() {\n      if (!this.isFilterMode) {\n        // 进入筛选模式，强制同步selectedRows\n        const checkedRows = this.$refs.table.getSelectionRows ? this.$refs.table.getSelectionRows() : [];\n        if (checkedRows.length === 0) {\n          this.$message.warning('请先勾选需要查看的接口');\n          return;\n        }\n        this.selectedRows = checkedRows;\n        this.isFilterMode = true;\n        this.$message.success(`已筛选 ${this.selectedRows.length} 个接口，图表和数据表已更新`);\n      } else {\n        // 退出筛选模式，但保留选中状态\n        this.isFilterMode = false;\n        // 不再清除表格选择状态\n        // if (this.$refs.table) {\n        //   this.$refs.table.clearSelection();\n        // }\n        // 不再清空selectedRows\n        // this.selectedRows = [];\n        this.$message.info('已显示全部数据，保留已选中的接口');\n      }\n    },\n\n    setMonitorIframeUrl() {\n      if (!this.monitorUrl || !/^https?:\\/\\//.test(this.monitorUrl)) {\n        this.$message.warning('请输入以 http:// 或 https:// 开头的有效链接');\n        return;\n      }\n      this.monitorIframeUrl = this.monitorUrl;\n    },\n    resetMonitorIframeUrl() {\n      this.monitorUrl = '';\n      this.monitorIframeUrl = '';\n    },\n    \n    // 获取对比值的CSS类\n    getComparisonClass(currentValue, baselineValue, metricType) {\n      // 确保值是有效的数字\n      const current = parseFloat(currentValue) || 0;\n      const baseline = parseFloat(baselineValue) || 0;\n      \n      if (baseline === 0) {\n        // 如果基准值为0，只能比较绝对值\n        if (current === 0) return 'comparison-similar';\n        return ['avg_tps', 'tps', 'success_rate'].includes(metricType) ? \n          'comparison-better' : 'comparison-worse';\n      }\n      \n      // 定义指标越低越好还是越高越好\n      const higherIsBetter = ['avg_tps', 'tps', 'success_rate'].includes(metricType);\n      const diff = ((current - baseline) / baseline) * 100;\n      \n      // 10%的阈值来判断好坏\n      if (higherIsBetter) {\n        if (diff >= 10) return 'comparison-better';\n        if (diff <= -10) return 'comparison-worse';\n        return 'comparison-similar';\n      } else {\n        if (diff <= -10) return 'comparison-better';\n        if (diff >= 10) return 'comparison-worse';\n        return 'comparison-similar';\n      }\n    },\n    \n    // 获取对比值的文本\n    getComparisonText(currentValue, baselineValue, metricType) {\n      // 确保值是有效的数字\n      const current = parseFloat(currentValue) || 0;\n      const baseline = parseFloat(baselineValue) || 0;\n      \n      if (baseline === 0) {\n        // 如果基准值为0，只显示绝对差异\n        return current === 0 ? '0 (0%)' : `+${current.toFixed(2)} (-)`;\n      }\n      \n      const diff = current - baseline;\n      const percentDiff = ((diff / baseline) * 100).toFixed(1);\n      const diffText = diff >= 0 ? `+${diff.toFixed(2)}` : diff.toFixed(2);\n      const percentText = diff >= 0 ? `+${percentDiff}%` : `${percentDiff}%`;\n      \n      return `${diffText} (${percentText})`;\n    },\n    \n    // 生成对比结论\n    generateComparisonConclusion() {\n      const metrics = this.baselineCompareResultData;\n      if (!metrics || !metrics.current_metrics || !metrics.baseline_metrics) {\n        return '无法生成对比结论，数据不完整。';\n      }\n      \n      const current = metrics.current_metrics;\n      const baseline = metrics.baseline_metrics;\n      \n      let conclusion = '性能对比分析：\\n';\n      \n      // 响应时间分析\n      if (current.avg_response_time !== undefined && baseline.avg_response_time !== undefined && baseline.avg_response_time > 0) {\n      const responseTimeDiff = ((current.avg_response_time - baseline.avg_response_time) / baseline.avg_response_time) * 100;\n      if (responseTimeDiff > 10) {\n        conclusion += `• 响应时间较基准线增加了${responseTimeDiff.toFixed(1)}%，性能有所下降。\\n`;\n      } else if (responseTimeDiff < -10) {\n        conclusion += `• 响应时间较基准线减少了${Math.abs(responseTimeDiff).toFixed(1)}%，性能有所提升。\\n`;\n      } else {\n        conclusion += `• 响应时间与基准线相比基本持平，波动在${Math.abs(responseTimeDiff).toFixed(1)}%范围内。\\n`;\n        }\n      } else {\n        conclusion += `• 响应时间数据不完整，无法进行比较。\\n`;\n      }\n      \n      // TPS分析\n      if (current.avg_tps !== undefined && baseline.avg_tps !== undefined && baseline.avg_tps > 0) {\n      const tpsDiff = ((current.avg_tps - baseline.avg_tps) / baseline.avg_tps) * 100;\n      if (tpsDiff > 10) {\n        conclusion += `• TPS较基准线提高了${tpsDiff.toFixed(1)}%，吞吐能力有所提升。\\n`;\n      } else if (tpsDiff < -10) {\n        conclusion += `• TPS较基准线下降了${Math.abs(tpsDiff).toFixed(1)}%，吞吐能力有所下降。\\n`;\n      } else {\n        conclusion += `• TPS与基准线相比基本持平，波动在${Math.abs(tpsDiff).toFixed(1)}%范围内。\\n`;\n        }\n      } else {\n        conclusion += `• TPS数据不完整，无法进行比较。\\n`;\n      }\n      \n      // 成功率分析\n      if (current.success_rate !== undefined && baseline.success_rate !== undefined) {\n      const successRateDiff = current.success_rate - baseline.success_rate;\n      if (successRateDiff > 1) {\n        conclusion += `• 成功率较基准线提高了${successRateDiff.toFixed(1)}个百分点，系统稳定性有所提升。\\n`;\n      } else if (successRateDiff < -1) {\n        conclusion += `• 成功率较基准线下降了${Math.abs(successRateDiff).toFixed(1)}个百分点，系统稳定性有所下降。\\n`;\n      } else {\n        conclusion += `• 成功率与基准线相比基本持平，系统稳定性保持一致。\\n`;\n        }\n      } else {\n        conclusion += `• 成功率数据不完整，无法进行比较。\\n`;\n      }\n      \n      // 资源使用分析\n      if (current.avg_cpu !== undefined && baseline.avg_cpu !== undefined && baseline.avg_cpu > 0) {\n        const cpuDiff = ((current.avg_cpu - baseline.avg_cpu) / baseline.avg_cpu) * 100;\n        if (cpuDiff > 10) {\n          conclusion += `• CPU使用率较基准线增加了${cpuDiff.toFixed(1)}%，可能需要关注资源使用情况。\\n`;\n        } else if (cpuDiff < -10) {\n          conclusion += `• CPU使用率较基准线减少了${Math.abs(cpuDiff).toFixed(1)}%，资源利用效率有所提升。\\n`;\n        } else {\n          conclusion += `• CPU使用率与基准线相比基本持平。\\n`;\n        }\n      }\n      \n      if (current.avg_memory !== undefined && baseline.avg_memory !== undefined && baseline.avg_memory > 0) {\n        const memoryDiff = ((current.avg_memory - baseline.avg_memory) / baseline.avg_memory) * 100;\n        if (memoryDiff > 10) {\n          conclusion += `• 内存使用率较基准线增加了${memoryDiff.toFixed(1)}%，可能需要关注内存占用情况。\\n`;\n        } else if (memoryDiff < -10) {\n          conclusion += `• 内存使用率较基准线减少了${Math.abs(memoryDiff).toFixed(1)}%，内存利用效率有所提升。\\n`;\n        } else {\n          conclusion += `• 内存使用率与基准线相比基本持平。\\n`;\n        }\n      }\n      \n      // 总体结论\n      let performanceIssues = [];\n      \n      if (current.avg_response_time !== undefined && baseline.avg_response_time !== undefined && baseline.avg_response_time > 0) {\n        const responseTimeDiff = ((current.avg_response_time - baseline.avg_response_time) / baseline.avg_response_time) * 100;\n        if (responseTimeDiff > 10) performanceIssues.push('响应时间增加');\n      }\n      \n      if (current.avg_tps !== undefined && baseline.avg_tps !== undefined && baseline.avg_tps > 0) {\n        const tpsDiff = ((current.avg_tps - baseline.avg_tps) / baseline.avg_tps) * 100;\n        if (tpsDiff < -10) performanceIssues.push('TPS下降');\n      }\n      \n      if (current.success_rate !== undefined && baseline.success_rate !== undefined) {\n        const successRateDiff = current.success_rate - baseline.success_rate;\n        if (successRateDiff < -1) performanceIssues.push('成功率下降');\n      }\n      \n      const issueCount = performanceIssues.length;\n      \n      if (issueCount === 0) {\n        conclusion += '\\n总体结论：当前性能测试结果与基准线相比表现良好，未发现明显性能退化。';\n      } else if (issueCount === 1) {\n        conclusion += `\\n总体结论：当前性能测试结果与基准线相比存在${performanceIssues[0]}的情况，建议关注上述性能变化。`;\n      } else {\n        conclusion += `\\n总体结论：当前性能测试结果与基准线相比存在多项指标下降（${performanceIssues.join('、')}），建议详细分析性能变化原因。`;\n      }\n      \n      return conclusion;\n    },\n    \n    // 导出对比报告\n    async exportComparisonResult() {\n      try {\n        this.$message.info('正在准备导出对比报告...');\n        \n        // 构建导出参数\n        const params = {\n          report_id: this.reportData.id,\n          baseline_id: this.baselineCompareDialogData.selectedBaselineId,\n          format: 'pdf',\n          include_charts: true\n        };\n        \n        // 此处应调用实际的导出API\n        // const response = await this.$api.exportComparisonReport(params);\n        \n        // 模拟导出成功\n        setTimeout(() => {\n          this.$message.success('对比报告导出成功');\n        }, 1500);\n        \n      } catch (error) {\n        this.$message.error('导出对比报告失败: ' + (error.response?.data?.message || error.message || '未知错误'));\n      }\n    },\n\n    // 安全解析浮点数值，避免NaN、Infinity等值导致的JSON序列化问题\n    safeParseFloat(value) {\n      if (value === undefined || value === null) {\n        return 0;\n      }\n      \n      // 尝试转换字符串\"null\"、\"undefined\"等特殊值\n      if (value === \"null\" || value === \"undefined\" || value === \"\") {\n        return 0;\n      }\n      \n      const num = parseFloat(value);\n      \n      // 检查是否为有效的有限数值\n      if (isNaN(num) || !isFinite(num)) {\n        return 0;\n      }\n      \n      return num;\n    },\n  },\n  mounted() {\n    (async () => {\n      // 首先获取报告ID - 支持多种方式获取\n      this.reportId = this.$route.params.id || this.$route.query.id || this.$route.query.reportId\n\n      if (!this.reportId) {\n        this.$message.error('报告ID不能为空，请从报告列表页面进入')\n        // 跳转回报告列表页面\n        this.$router.push({ name: 'PerformanceResult' })\n        return\n      }\n\n      // 加载报告数据\n      await this.loadReportData()\n      // 默认显示指标详情\n      this.activeIndex = '1'\n    })();\n  },\n  beforeUnmount() {\n    // 清理WebSocket连接和定时器\n    this.stopAllRealTimeUpdates();\n  }\n}\n</script>\n\n<style scoped>\n/* 基准线对话框样式 */\n.baseline-info-hint {\n  background-color: #f0f9ff;\n  border-left: 4px solid #409EFF;\n  padding: 10px;\n  margin: 10px 0;\n  font-size: 12px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n/* 对比结果样式 */\n.comparison-container {\n  padding: 15px 0;\n}\n\n.comparison-card {\n  height: 100%;\n  padding: 15px;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.comparison-card.baseline {\n  background-color: #f0f9ff;\n  border-left: 4px solid #409EFF;\n}\n\n.comparison-card.current {\n  background-color: #f6ffed;\n  border-left: 4px solid #67C23A;\n}\n\n.comparison-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 15px;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.comparison-metrics .metric-row {\n  display: flex;\n  justify-content: space-between;\n  padding: 8px 0;\n  border-bottom: 1px solid #eee;\n}\n\n.comparison-metrics .metric-label {\n  font-weight: 500;\n  color: #606266;\n}\n\n.comparison-metrics .metric-value {\n  font-weight: 500;\n  color: #303133;\n}\n\n.comparison-better {\n  margin-left: 10px;\n  color: #67C23A;\n  font-weight: bold;\n}\n\n.comparison-worse {\n  margin-left: 10px;\n  color: #F56C6C;\n  font-weight: bold;\n}\n\n.comparison-similar {\n  margin-left: 10px;\n  color: #E6A23C;\n}\n\n.comparison-conclusion {\n  margin-top: 25px;\n  padding: 15px;\n  background-color: #f5f7fa;\n  border-radius: 8px;\n}\n\n.conclusion-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 10px;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.conclusion-content {\n  white-space: pre-line;\n  line-height: 1.6;\n  color: #606266;\n}\n/* ===== 新的现代化样式 ===== */\n\n/* 报告头部卡片 */\n.report-header-card {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  border-radius: 12px;\n  color: white;\n  margin-bottom: 20px;\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);\n  overflow: hidden;\n}\n\n.report-header-card :deep(.el-card__body) {\n  padding: 30px;\n}\n\n.report-header-content {\n  display: flex;\n  flex-direction: column;\n  gap: 30px;\n}\n\n/* 任务信息区域 */\n.task-info-section {\n  padding-bottom: 20px;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.task-title-area {\n  max-width: 600px;\n}\n\n.task-title-container {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  margin-bottom: 10px;\n}\n\n.task-name-button {\n  font-size: 24px;\n  font-weight: 700;\n  color: white !important;\n  padding: 0;\n  height: auto;\n  line-height: 1.2;\n}\n\n.task-name-button:hover {\n  color: rgba(255, 255, 255, 0.8) !important;\n}\n\n.edit-icon {\n  margin-left: 8px;\n  font-size: 18px;\n  opacity: 0.7;\n}\n\n.task-status-tag {\n  font-weight: 600;\n  font-size: 13px;\n  padding: 6px 12px;\n  border-radius: 20px;\n  border: none;\n}\n\n.task-status-tag.success {\n  background-color: #4ade80;\n  color: white;\n}\n\n.task-status-tag.running {\n  background-color: #3b82f6;\n  color: white;\n}\n.task-status-tag.danger {\n  background-color: #f56c6c;\n  color: white;\n}\n.task-status-tag.info {\n  background-color: #8c8c8c;\n  width: 100px;\n  color: white;\n}\n\n\n.task-status-tag.error {\n  background-color: #ef4444;\n  color: white;\n}\n\n.desc-text {\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 14px;\n  line-height: 1.6;\n  cursor: default;\n}\n\n/* 指标卡片区域 */\n.metrics-section {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 20px;\n}\n\n.metric-card {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  padding: 20px;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  transition: all 0.3s ease;\n}\n\n.metric-card:hover {\n  background: rgba(255, 255, 255, 0.15);\n  transform: translateY(-2px);\n}\n\n.metric-icon {\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 15px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 50px;\n  height: 50px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 10px;\n}\n\n.metric-content {\n  flex: 1;\n}\n\n.metric-title {\n  font-size: 16px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: white;\n}\n\n.metric-values {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.metric-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.metric-label {\n  font-size: 13px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.metric-value {\n  font-size: 14px;\n  font-weight: 600;\n  color: white;\n}\n\n/* Tab导航和按钮容器 */\n.tab-navigation-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: white;\n  border-radius: 8px;\n  padding: 0 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 20px;\n}\n\n.report-tabs {\n  flex: 1;\n  border-bottom: none;\n}\n\n.report-tabs :deep(.el-menu-item) {\n  border-bottom: 2px solid transparent;\n  transition: all 0.3s ease;\n}\n\n.report-tabs :deep(.el-menu-item.is-active) {\n  border-bottom-color: #667eea;\n  color: #667eea;\n}\n\n.report-tabs :deep(.el-menu-item:hover) {\n  color: #667eea;\n}\n\n/* 操作按钮组 */\n.action-buttons {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  flex-shrink: 0;\n}\n\n.action-btn {\n  height: 36px;\n  padding: 0 16px;\n  border-radius: 6px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n  border: 1px solid transparent;\n}\n\n.action-btn:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.action-dropdown {\n  margin: 0;\n}\n\n.action-dropdown .action-btn {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n/* 下拉菜单项图标 */\n:deep(.el-dropdown-menu__item) {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 10px 16px;\n}\n\n:deep(.el-dropdown-menu__item i) {\n  width: 16px;\n}\n\n/* 图表容器样式 */\n.charts-container {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));\n  gap: 20px;\n  margin-bottom: 50px;\n  padding: 0 15px;\n}\n\n.chart-wrapper {\n  background: #ffffff;\n  border-radius: 12px;\n  padding: 20px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s ease;\n  border: 1px solid rgba(0, 0, 0, 0.03);\n  position: relative;\n  overflow: hidden;\n}\n\n.chart-wrapper:hover {\n  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.12);\n  transform: translateY(-3px);\n  border-color: rgba(0, 0, 0, 0.06);\n}\n\n/* 为不同类型的图表添加不同的顶部边框颜色 */\n.chart-wrapper:nth-child(1) {\n  border-top: 3px solid #5470c6;\n}\n\n.chart-wrapper:nth-child(2) {\n  border-top: 3px solid #5b8ff9;\n}\n\n.chart-wrapper:nth-child(3) {\n  border-top: 3px solid #ff6b3b;\n}\n\n.chart-wrapper:nth-child(4) {\n  border-top: 3px solid #1890ff;\n}\n\n.chart-wrapper:nth-child(5) {\n  border-top: 3px solid #9b8bfe;\n}\n\n.chart-wrapper:nth-child(6) {\n  border-top: 3px solid #e84a5f;\n}\n\n/* 图表标题样式增强 */\n.chart-wrapper :deep(.chart-title) {\n  font-weight: 600;\n  color: #303133;\n  font-size: 16px;\n  margin-bottom: 15px;\n  position: relative;\n  padding-left: 12px;\n}\n\n.chart-wrapper :deep(.chart-title)::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  height: 16px;\n  width: 4px;\n  border-radius: 2px;\n  background-color: #5470c6;\n}\n\n/* 为不同类型的图表添加不同的标题装饰色 */\n.chart-wrapper:nth-child(1) :deep(.chart-title)::before {\n  background-color: #5470c6;\n}\n\n.chart-wrapper:nth-child(2) :deep(.chart-title)::before {\n  background-color: #5b8ff9;\n}\n\n.chart-wrapper:nth-child(3) :deep(.chart-title)::before {\n  background-color: #ff6b3b;\n}\n\n.chart-wrapper:nth-child(4) :deep(.chart-title)::before {\n  background-color: #1890ff;\n}\n\n.chart-wrapper:nth-child(5) :deep(.chart-title)::before {\n  background-color: #9b8bfe;\n}\n\n.chart-wrapper:nth-child(6) :deep(.chart-title)::before {\n  background-color: #e84a5f;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .charts-container {\n    grid-template-columns: 1fr;\n  }\n}\n\n@media (max-width: 768px) {\n  .charts-container {\n    padding: 0 10px;\n  }\n\n  .chart-wrapper {\n    padding: 15px;\n  }\n}\n\n/* 原有样式保持不变 */\n.outer {\n  padding: 20px;\n}\n\n.box {\n  max-width: 100%;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .metrics-section {\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  }\n}\n\n@media (max-width: 768px) {\n  .tab-navigation-container {\n    flex-direction: column;\n    gap: 15px;\n    padding: 15px;\n  }\n\n  .action-buttons {\n    flex-wrap: wrap;\n    justify-content: center;\n  }\n\n  .metrics-section {\n    grid-template-columns: 1fr;\n  }\n}\n\n/* 以下是原有样式，保持不变 */\n\n.box{\n  background-color: #ffffff;\n}\n.outer {\n    background-color: #f5f7f9;\n    padding: 3px;\n  }\n.projectInfo {\n  position: absolute;\n  top: 0;\n  right: 0;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n\n.el-tag {\n  color: #ffffff;\n  width: 70px;\n  height: 25px;\n  text-align: center;\n  font-size: 13px;\n  line-height: 23px;\n}\n\n.icon-info{\n  width: 60px;\n  height: 60px;\n  background-color: #4c52d5;\n  border-radius: 50%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.parameter{\n  margin-bottom: 3px;\n}\n\n.munu{\n  margin-top: 10px;\n  margin-left: 15px;\n  margin-bottom: 5px;\n}\n\n.spacing {\n  background:#f5f7f9;\n  height: 40px;\n  line-height: 40px;\n}\n\n.spacing > span {\n  margin-right: 50px;\n  font-size: 15px;\n}\n\n.table-desc {\n  margin-bottom: 7px;\n  display: flex;\n}\n\n.table-desc > span {\n  margin-right: 50px;\n  font-size: 13px;\n  line-height: 35px;\n  position: relative;\n  padding-left: 20px;\n}\n\n.table-desc span::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 10px;\n  height: 10px;\n  background-color: #a5b1ff;\n  border-radius: 50%;\n}\n\n.divider {\n  width: 5px;\n  height: 18px;\n  margin: -4px 5px 0px 0px;\n  background-color: #a5b1ff;\n}\n\n.title-info {\n  margin-bottom: 10px;\n  color: #606266;\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.runStop {\n  margin-top: -10px;\n  margin-right: 10px\n}\n\n.p_text {\n  font-size: 15px;\n  margin-bottom: 50px;\n  background:#f5f7f9;\n  height: 40px;\n  line-height: 40px\n}\n\n.iframe-container {\n  height: calc(100vh - 250px);\n  width: 100%;\n}\n\n.iframe-container iframe {\n  height: 100%;\n  width: 100%;\n  border: none;\n}\n\n.task-header {\n  display: flex;\n  flex-direction: column;\n}\n\n.task-title-container {\n  display: flex;\n  align-items: center;\n  margin-top: -5px;\n}\n\n.task-name-button {\n  font-size: 16px;\n  color: #ffffff;\n  font-weight: 500;\n  padding: 0;\n  display: flex;\n  align-items: center;\n  width: auto;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.edit-icon {\n  margin-left: 5px;\n  font-size: 14px;\n}\n\n.task-status-tag {\n  margin-left: 10px;\n  color: #ffffff;\n  font-weight: 500;\n  border: none;\n  padding: 0 10px;\n  height: 26px;\n  line-height: 26px;\n}\n\n.task-description {\n  margin-top: 10px;\n}\n\n.desc-button {\n  font-size: 13px;\n  color: #ffffff;\n  padding: 0;\n  max-width: 200px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  opacity: 0.9;\n}\n\n.desc-button:hover {\n  opacity: 1;\n  text-decoration: underline;\n}\n\n.task-name-input {\n  position: absolute;\n  top: 10px;\n  left: 20px;\n  width: 170px;\n  z-index: 10;\n}\n\n/* 监控面板样式 */\n.monitor-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n.monitor-card {\n  background: #ffffff;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.monitor-card h4 {\n  margin: 0 0 15px 0;\n  color: #409eff;\n  font-size: 16px;\n  border-bottom: 2px solid #e4e7ed;\n  padding-bottom: 8px;\n}\n\n.metric-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.metric-item:last-child {\n  border-bottom: none;\n}\n\n.metric-normal {\n  color: #67c23a;\n  font-weight: bold;\n}\n\n.metric-warning {\n  color: #e6a23c;\n  font-weight: bold;\n}\n\n.metric-danger {\n  color: #f56c6c;\n  font-weight: bold;\n}\n\n/* 日志面板样式 */\n.log-controls {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n  padding: 10px;\n  background: #f8f9fa;\n  border-radius: 4px;\n}\n\n.log-container {\n  height: 500px;\n  max-height: 500px;\n  overflow-y: auto;\n  background: #1e1e1e;\n  border-radius: 4px;\n  padding: 10px;\n}\n\n.log-item {\n  margin-bottom: 8px;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n  line-height: 1.4;\n  padding: 4px 8px;\n  border-radius: 3px;\n  position: relative;\n}\n\n.log-item:hover {\n  background-color: rgba(255, 255, 255, 0.05);\n}\n\n.log-time {\n  color: #6c757d;\n  margin-right: 8px;\n}\n\n.log-level {\n  margin-right: 8px;\n  font-weight: bold;\n}\n\n.log-category {\n  display: inline-flex;\n  align-items: center;\n  margin-right: 8px;\n  color: #909399;\n}\n\n.log-message {\n  color: #ffffff;\n}\n\n.log-method {\n  display: inline-block;\n  padding: 1px 6px;\n  border-radius: 3px;\n  font-weight: bold;\n  margin-right: 8px;\n}\n\n.method-get {\n  background-color: #61affe;\n  color: #fff;\n}\n\n.method-post {\n  background-color: #49cc90;\n  color: #fff;\n}\n\n.method-put {\n  background-color: #fca130;\n  color: #fff;\n}\n\n.method-delete {\n  background-color: #f93e3e;\n  color: #fff;\n}\n\n.method-patch {\n  background-color: #50e3c2;\n  color: #fff;\n}\n\n.method-head, .method-options {\n  background-color: #9012fe;\n  color: #fff;\n}\n\n.log-url {\n  color: #e6db74;\n  margin-right: 8px;\n}\n\n.log-status {\n  display: inline-block;\n  padding: 1px 6px;\n  border-radius: 3px;\n  font-weight: bold;\n}\n\n.status-success {\n  background-color: #49cc90;\n  color: #fff;\n}\n\n.status-redirect {\n  background-color: #fca130;\n  color: #fff;\n}\n\n.status-client-error {\n  background-color: #f93e3e;\n  color: #fff;\n}\n\n.status-server-error {\n  background-color: #d41f1c;\n  color: #fff;\n}\n\n.details-toggle {\n  position: absolute;\n  right: 8px;\n  top: 4px;\n  font-size: 11px;\n  padding: 0;\n  color: #909399;\n}\n\n.log-details {\n  margin-top: 5px;\n  padding: 8px;\n  background-color: rgba(255, 255, 255, 0.05);\n  border-radius: 3px;\n  white-space: pre-wrap;\n  font-size: 11px;\n  color: #a9b7c6;\n  max-height: 200px;\n  overflow-y: auto;\n}\n\n.log-info .log-level {\n  color: #17a2b8;\n}\n\n.log-warning .log-level {\n  color: #ffc107;\n}\n\n.log-error .log-level {\n  color: #dc3545;\n}\n\n.log-debug .log-level {\n  color: #6c757d;\n}\n\n.no-logs {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n  color: #909399;\n}\n\n.empty-log-content {\n  text-align: center;\n}\n\n.empty-log-icon {\n  font-size: 48px;\n  color: #606266;\n  margin-bottom: 16px;\n}\n\n.empty-log-text {\n  font-size: 16px;\n  color: #909399;\n  margin: 8px 0;\n}\n\n.empty-log-hint {\n  font-size: 14px;\n  color: #606266;\n  margin: 4px 0;\n}\n\n.no-logs .el-empty {\n  background: transparent;\n}\n\n.no-logs .el-empty__image svg {\n  fill: #606266;\n}\n\n.no-logs .el-empty__description {\n  color: #909399;\n  font-size: 14px;\n}\n\n.no-logs :deep(.el-empty__description p) {\n  color: #909399;\n}\n\n/* 错误列表样式 */\n.error-list {\n  max-height: 200px;\n  overflow-y: auto;\n}\n\n.error-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 12px;\n  margin-bottom: 5px;\n  background: #f8f9fa;\n  border-radius: 4px;\n  border-left: 4px solid #f56c6c;\n}\n\n.error-type {\n  font-weight: bold;\n  color: #f56c6c;\n}\n\n.error-count {\n  background: #f56c6c;\n  color: white;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .monitor-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .log-controls {\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .log-controls > * {\n    width: 100%;\n  }\n\n  .gui-server-selection {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 10px;\n  }\n\n  .gui-server-selection > * {\n    width: 100%;\n  }\n\n  .gui-server-selection .el-radio-group {\n    display: flex;\n    justify-content: center;\n  }\n\n  .test-actions {\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .test-actions .el-button {\n    width: 100%;\n  }\n}\n\n/* 对话框样式 */\n.analysis-dialog-content {\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n.analysis-section {\n  margin-bottom: 30px;\n}\n\n.analysis-section h4 {\n  margin-bottom: 15px;\n  color: #409eff;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.score-display {\n  text-align: center;\n  margin: 20px 0;\n}\n\n.score-text {\n  font-size: 14px;\n  font-weight: 600;\n  color: #606266;\n}\n\n.recommendations-list {\n  list-style: none;\n  padding: 0;\n}\n\n.recommendation-item {\n  display: flex;\n  align-items: center;\n  padding: 8px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.recommendation-item:last-child {\n  border-bottom: none;\n}\n\n.recommendation-icon {\n  color: #67c23a;\n  margin-right: 8px;\n}\n\n.config-dialog-content {\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n.config-section {\n  margin-bottom: 20px;\n}\n\n.config-section-title {\n  display: flex;\n  align-items: center;\n  font-size: 16px;\n  font-weight: 600;\n  color: #409eff;\n}\n\n.config-section-title .el-icon {\n  margin-right: 8px;\n}\n\n.config-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.config-label {\n  font-weight: 500;\n  color: #606266;\n  margin-right: 10px;\n  min-width: 100px;\n}\n\n.config-value {\n  color: #303133;\n}\n\n.notification-dialog-content {\n  text-align: center;\n}\n\n.notification-info {\n  margin: 20px 0;\n}\n\n.notification-confirm {\n  color: #606266;\n  font-size: 14px;\n}\n\n/* 通知对话框样式 */\n:deep(.notification-dialog) {\n  .el-message-box__content {\n    max-height: 400px;\n    overflow-y: auto;\n  }\n}\n\n/* 配置对话框样式 */\n:deep(.config-dialog) {\n  .el-message-box {\n    width: 80% !important;\n    max-width: 1000px;\n  }\n\n  .el-message-box__content {\n    max-height: 600px;\n    overflow-y: auto;\n    padding: 20px !important;\n  }\n\n  .el-message-box__message {\n    margin: 0 !important;\n  }\n}\n\n/* 配置HTML内容样式 */\n:deep(.config-dialog) h3 {\n  margin: 0 0 15px 0 !important;\n  font-size: 16px !important;\n  font-weight: 600 !important;\n}\n\n:deep(.config-dialog) pre {\n  white-space: pre-wrap !important;\n  word-wrap: break-word !important;\n}\n\n/* 下拉菜单样式优化 */\n.projectInfo .el-dropdown {\n  margin-left: 10px;\n}\n\n.projectInfo .el-button {\n  margin-right: 10px;\n}\n\n.projectInfo .el-button:last-child {\n  margin-right: 0;\n}\n\n.interface-name {\n  font-weight: 600;\n  color: #303133;\n}\n\n/* WebSocket状态指示器样式 */\n.ws-status-indicator {\n  margin-left: 15px;\n  display: inline-block;\n}\n\n.ws-status-indicator .el-tag {\n  transition: all 0.3s ease;\n}\n\n.ws-status-indicator .el-tag:hover {\n  transform: scale(1.05);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n}\n\n/* GUI控制样式 */\n.gui-controls {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n  margin-bottom: 15px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n}\n\n.gui-server-selection {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  flex-wrap: wrap;\n}\n\n.gui-server-selection .el-radio-group {\n  background: white;\n  padding: 5px;\n  border-radius: 4px;\n  border: 1px solid #dcdfe6;\n}\n\n.gui-server-selection .el-input {\n  min-width: 150px;\n}\n\n.gui-server-selection .el-select {\n  min-width: 120px;\n}\n\n.gui-server-selection .el-button {\n  white-space: nowrap;\n}\n\n.iframe-container {\n  height: calc(100vh - 250px);\n  width: 100%;\n  position: relative;\n}\n\n.iframe-container iframe {\n  height: 100%;\n  width: 100%;\n  border: none;\n  border-radius: 6px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.iframe-error {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 10;\n  background: rgba(255, 255, 255, 0.95);\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  text-align: center;\n  max-width: 400px;\n  width: 90%;\n}\n\n.no-gui-message {\n  height: calc(100vh - 250px);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.no-gui-message .el-empty {\n  background: transparent;\n}\n\n/* 端口选择样式 */\n.el-select .el-option.is-disabled {\n  color: #c0c4cc;\n  background-color: #f5f7fa;\n  position: relative;\n}\n\n.el-select .el-option.is-disabled:hover {\n  background-color: #f5f7fa;\n}\n\n/* 服务器选择样式优化 */\n.gui-server-selection .el-select {\n  margin-right: 10px;\n}\n\n.gui-server-selection .el-select .el-input__inner {\n  font-size: 12px;\n}\n\n.gui-server-selection .el-button {\n  font-size: 12px;\n}\n\n/* 端口状态标识 */\n.port-status-available {\n  color: #67c23a;\n}\n\n.port-status-occupied {\n  color: #f56c6c;\n}\n\n/* 测试状态提示样式 */\n.test-not-running {\n  margin: 20px 0;\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 1px solid #e4e7ed;\n}\n\n.test-actions {\n  display: flex;\n  justify-content: center;\n  gap: 15px;\n}\n\n.test-actions .el-button {\n  min-width: 140px;\n}\n\n/* 通知对话框样式 */\n.push-dialog {\n  max-width: 420px !important;\n  border-radius: 12px;\n  padding-bottom: 0;\n}\n.push-dialog .el-dialog__body {\n  padding: 24px 32px 0 32px !important;\n}\n.push-dialog .el-form-item {\n  margin-bottom: 22px;\n}\n.push-dialog .el-form-item__label {\n  font-weight: 600;\n  color: #333;\n  font-size: 15px;\n}\n.push-dialog .custom-radio-group {\n  display: flex;\n  gap: 12px;\n}\n.push-dialog .custom-radio-btn {\n  cursor: pointer;\n  padding: 7px 18px;\n  border-radius: 6px;\n  border: 1.5px solid #e0e7ef;\n  background: #f7f8fa;\n  color: #666;\n  font-weight: 500;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n.push-dialog .custom-radio-btn.active,\n.push-dialog .custom-radio-btn:hover {\n  border-color: #6366f1;\n  background: #eef2ff;\n  color: #3b3b7a;\n  box-shadow: 0 2px 8px rgba(99,102,241,0.08);\n}\n.push-dialog .form-tip {\n  font-size: 12px;\n  color: #b0b0b0;\n  margin-top: 2px;\n}\n.push-dialog .el-input,\n.push-dialog .el-select {\n  width: 100%;\n}\n.push-dialog .el-descriptions {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 12px 10px 2px 10px;\n  margin-top: 8px;\n}\n.push-dialog .el-descriptions__label {\n  color: #888;\n  font-weight: 500;\n}\n.push-dialog .el-descriptions__content {\n  color: #222;\n  font-weight: 600;\n}\n.push-dialog .dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 16px;\n  padding: 18px 32px 18px 32px;\n  background: #f7f8fa;\n  border-radius: 0 0 12px 12px;\n  margin-top: 0;\n}\n.push-dialog .el-button {\n  min-width: 90px;\n}\n\n/* 现代通知对话框 */\n.modern-notification-dialog {\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n.modern-notification-dialog :deep(.el-dialog__header) {\n  background: linear-gradient(135deg, #3a8ee6, #5b5ef4);\n  color: white;\n  padding: 18px 24px;\n  margin: 0;\n}\n\n.modern-notification-dialog :deep(.el-dialog__title) {\n  color: white;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.modern-notification-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.modern-notification-dialog :deep(.el-dialog__headerbtn:hover .el-dialog__close) {\n  color: white;\n}\n\n.modern-notification-dialog :deep(.el-dialog__body) {\n  padding: 0;\n}\n\n.notification-dialog-content {\n  padding: 0;\n}\n\n.notification-dialog-header {\n  padding: 20px 30px;\n  background-color: #f8fafc;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.notification-title {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.notification-icon {\n  font-size: 20px;\n  color: #3a8ee6;\n}\n\n.notification-subtitle {\n  color: #606266;\n  font-size: 14px;\n}\n\n.notification-form {\n  padding: 20px 30px;\n}\n\n.notification-input :deep(.el-input__inner) {\n  border-radius: 8px;\n  padding: 12px;\n  height: 44px;\n}\n\n.notification-select :deep(.el-input__wrapper) {\n  border-radius: 8px;\n  padding: 12px;\n  height: 44px;\n}\n\n.notification-type-selector {\n  display: flex;\n  justify-content: space-between;\n  gap: 12px;\n  margin-bottom: 8px;\n}\n\n.notification-type-option {\n  flex: 1;\n  height: 70px;\n  border-radius: 8px;\n  border: 1px solid #e4e7ed;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 10px;\n  gap: 8px;\n  cursor: pointer;\n  transition: all 0.3s;\n  background-color: #fff;\n  min-width: 100px;\n}\n\n.notification-type-option:hover {\n  border-color: #a0cfff;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n}\n\n.notification-type-option.selected {\n  border-color: #409eff;\n  background-color: #ecf5ff;\n}\n\n.notification-type-option span {\n  font-size: 14px;\n  font-weight: 500;\n  text-align: center;\n}\n\n.notification-type-option .option-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 16px;\n  flex-shrink: 0;\n}\n\n/* 调整顺序，以确保这些样式应用在icon样式之后 */\n\n.email-icon {\n  background-color: #67c23a;\n}\n\n.webhook-icon {\n  background-color: #409eff;\n}\n\n.dingtalk-icon {\n  background-color: #1890ff;\n}\n\n.wechat-icon {\n  background-color: #07c160;\n}\n\n.notification-tip {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 8px;\n  line-height: 1.4;\n}\n\n.notification-report-preview {\n  margin-top: 16px;\n  border: 1px solid #ebeef5;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);\n}\n\n.notification-report-preview .preview-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  background-color: #f8fafc;\n  padding: 12px 16px;\n  border-bottom: 1px solid #ebeef5;\n  font-weight: 600;\n  color: #303133;\n}\n\n.notification-report-preview .preview-content {\n  padding: 20px;\n}\n\n.notification-report-preview .preview-item {\n  display: flex;\n  margin-bottom: 12px;\n}\n\n.notification-report-preview .item-label {\n  width: 80px;\n  color: #606266;\n  font-size: 14px;\n}\n\n.notification-report-preview .item-value {\n  flex: 1;\n  font-weight: 500;\n  color: #303133;\n}\n\n.notification-report-preview .metrics-section {\n  margin-top: 16px;\n}\n\n.notification-report-preview .metrics-row {\n  display: flex;\n  gap: 12px;\n  margin-bottom: 12px;\n}\n\n.notification-report-preview .metrics-row:last-child {\n  margin-bottom: 0;\n}\n\n.notification-report-preview .metric-item-wide {\n  flex: 1;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  background-color: #f8fafc;\n  border-radius: 8px;\n  border-left: 3px solid #409eff;\n}\n\n.notification-report-preview .metric-item-wide .metric-label {\n  font-size: 14px;\n  color: #606266;\n}\n\n.notification-report-preview .metric-item-wide .metric-value {\n  font-size: 16px;\n  font-weight: 600;\n  color: #303133;\n  white-space: nowrap;\n}\n\n.notification-dialog-footer {\n  padding: 16px 30px;\n  background-color: #f8fafc;\n  border-top: 1px solid #ebeef5;\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n}\n\n.notification-dialog-footer :deep(.el-button) {\n  padding: 10px 20px;\n  height: auto;\n  border-radius: 8px;\n}\n\n.notification-dialog-footer :deep(.el-button--primary) {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n</style>", "<template>\n  <div class=\"chart-container\">\n    <div class=\"chart-header\">\n      <h3 class=\"chart-title\">{{ chartTitle }}</h3>\n      <el-select \n        v-model=\"selectedSeries\" \n        @change=\"updateLegend\" \n        class=\"series-selector\"\n        size=\"small\"\n      >\n        <el-option value=\"All\">\n          <span class=\"option-label\">全部接口</span>\n        </el-option>\n        <el-option \n          v-for=\"(data, index) in seriesData\" \n          :key=\"index\" \n          :value=\"data.name\"\n        >\n          <span class=\"option-label\">{{ data.name }}</span>\n        </el-option>\n      </el-select>\n    </div>\n    <div ref=\"chart\" :style=\"{ width: chartWidth, height: chartHeight }\"></div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts';\n\nexport default {\n  name: 'ResponseTimeChart',\n  props: {\n    chartTitle: {\n      type: String,\n      default: '平均响应时间'\n    },\n    chartUnit: {\n      type: String,\n      default: '单位(毫秒)'\n    },\n    chartWidth: {\n      type: String,\n      default: '400px'\n    },\n    chartHeight: {\n      type: String,\n      default: '300px'\n    },\n    xData: {\n      type: Array,\n      required: true\n    },\n    seriesData: {\n      type: Array,\n      required: true\n    },\n    // 添加图表类型属性，用于区分不同类型的图表\n    chartType: {\n      type: String,\n      default: 'responseTime' // 可选值: responseTime, rps, tps, users, p90, p99\n    }\n  },\n  data() {\n    return {\n      selectedSeries: 'All', // 默认选择全部\n    };\n  },\n\n  mounted() {\n    this.$nextTick(() => {\n      this.initChart();\n    });\n  },\n\n  watch: {\n    // 监听数据变化，自动更新图表\n    xData: {\n      handler() {\n        this.$nextTick(() => {\n          this.updateChart();\n        });\n      },\n      deep: true\n    },\n    seriesData: {\n      handler() {\n        this.$nextTick(() => {\n          this.updateChart();\n        });\n      },\n      deep: true\n    }\n  },\n\n  methods: {\n    initChart() {\n      if (!this.$refs.chart) return;\n      \n      const chart = echarts.init(this.$refs.chart);\n      this.chartInstance = chart; // 保存实例以便后续更新\n      \n      // 监听窗口大小变化\n      window.addEventListener('resize', () => {\n        chart.resize();\n      });\n      \n      this.updateChart(); // 初始化图表\n    },\n\n    updateLegend() {\n      this.updateChart(); // 更新图表\n    },\n\n    updateChart() {\n      if (!this.chartInstance) return;\n      \n      const chart = this.chartInstance;\n\n      // 检查数据是否存在\n      if (!this.xData || !this.seriesData || this.seriesData.length === 0) {\n        console.log('图表数据为空，跳过更新');\n        return;\n      }\n\n      // 根据图表类型获取样式配置\n      const styleConfig = this.getChartStyleConfig();\n\n      const series = this.seriesData.map((data, index) => ({\n        name: data.name,\n        type: 'line',\n        data: this.selectedSeries === 'All' || this.selectedSeries === data.name ? data.values : [],\n        showSymbol: styleConfig.showSymbol,\n        symbolSize: styleConfig.symbolSize,\n        smooth: styleConfig.smooth,\n        lineStyle: {\n          width: styleConfig.lineWidth,\n          type: this.getLineType(index, styleConfig),\n          shadowColor: 'rgba(0, 0, 0, 0.3)',\n          shadowBlur: styleConfig.shadowBlur,\n          shadowOffsetY: 1,\n          cap: 'round'\n        },\n        itemStyle: {\n          color: this.getSeriesColor(index, styleConfig),\n          borderWidth: 2,\n          borderColor: this.getSeriesColor(index, styleConfig),\n          shadowColor: 'rgba(0, 0, 0, 0.3)',\n          shadowBlur: 2\n        },\n        areaStyle: this.getAreaStyle(index, styleConfig),\n        emphasis: {\n          focus: 'series',\n          lineStyle: {\n            width: styleConfig.lineWidth + 1,\n            shadowBlur: styleConfig.shadowBlur + 2\n          },\n          itemStyle: {\n            borderWidth: 3,\n            shadowBlur: 4\n          }\n        }\n      }));\n\n      const option = {\n        color: styleConfig.colors,\n        tooltip: {\n          trigger: 'axis',\n          formatter: (params) => {\n            let result = `<div style=\"font-weight: bold; margin-bottom: 5px;\">${params[0].axisValue}</div>`;\n            params.forEach(param => {\n              if (param.data !== undefined) {\n                result += `\n                  <div style=\"display: flex; align-items: center; margin: 3px 0;\">\n                    <span style=\"display: inline-block; width: 10px; height: 10px; \n                      background-color: ${param.color}; border-radius: 50%; margin-right: 5px;\">\n                    </span>\n                    <span>${param.seriesName}: ${param.data} ${this.chartUnit}</span>\n                  </div>`;\n              }\n            });\n            return result;\n          },\n          backgroundColor: 'rgba(255, 255, 255, 0.95)',\n          borderColor: '#ddd',\n          borderWidth: 1,\n          textStyle: {\n            color: '#333',\n            fontSize: 12\n          },\n          extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); border-radius: 4px;'\n        },\n        grid: {\n          left: '3%',\n          right: '4%',\n          bottom: '10%',\n          top: '5%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          data: this.xData,\n          axisLabel: {\n            rotate: 45,\n            color: '#666',\n            fontSize: 11,\n            interval: 'auto'\n          },\n          axisLine: {\n            lineStyle: {\n              color: '#e8e8e8',\n              width: 1\n            }\n          },\n          axisTick: {\n            show: false\n          }\n        },\n        yAxis: {\n          name: this.chartUnit,\n          nameLocation: 'middle',\n          nameGap: 40,\n          nameTextStyle: {\n            color: '#666',\n            fontSize: 12\n          },\n          type: 'value',\n          axisLabel: {\n            color: '#666',\n            fontSize: 11,\n            formatter: (value) => {\n              if (value >= 1000) {\n                return (value / 1000).toFixed(1) + 'k';\n              }\n              return value;\n            }\n          },\n          axisLine: {\n            show: false\n          },\n          splitLine: {\n            lineStyle: {\n              color: '#f0f0f0',\n              type: 'dashed'\n            }\n          }\n        },\n        series: series,\n        legend: {\n          show: this.selectedSeries === 'All' && series.length > 1,\n          bottom: 0,\n          data: series.map(s => s.name),\n          textStyle: {\n            fontSize: 11,\n            color: '#666'\n          },\n          icon: styleConfig.legendIcon,\n          itemWidth: 16,\n          itemHeight: 10,\n          itemGap: 10\n        },\n        animation: true,\n        animationDuration: 500,\n        animationEasing: 'cubicOut'\n      };\n\n      chart.setOption(option, true); // 第二个参数为true表示不合并选项\n    },\n    \n    // 根据图表类型获取样式配置\n    getChartStyleConfig() {\n      const configs = {\n        responseTime: {\n          colors: [\n            '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', \n            '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#ff9f7f'\n          ],\n          smooth: true,\n          lineWidth: 3,\n          showSymbol: false,\n          symbolSize: 6,\n          shadowBlur: 5,\n          useGradient: true,\n          legendIcon: 'roundRect',\n          defaultLineType: 'solid',\n          areaOpacity: 0.15\n        },\n        rps: {\n          colors: [\n            '#5b8ff9', '#61ddaa', '#f6bd16', '#7262fd', '#78d3f8',\n            '#9661bc', '#f6903d', '#008685', '#f08bb4', '#65b581'\n          ],\n          smooth: true,\n          lineWidth: 3,\n          showSymbol: false,\n          symbolSize: 6,\n          shadowBlur: 4,\n          useGradient: true,\n          legendIcon: 'circle',\n          defaultLineType: 'solid',\n          areaOpacity: 0.2\n        },\n        tps: {\n          colors: [\n            '#ff6b3b', '#626c91', '#a0a7e6', '#c4ebad', '#96dee8',\n            '#ff9d6c', '#bad6ff', '#bbe2bb', '#ffd0a9', '#d7baf9'\n          ],\n          smooth: true,\n          lineWidth: 3,\n          showSymbol: false,\n          symbolSize: 6,\n          shadowBlur: 3,\n          useGradient: true,\n          legendIcon: 'pin',\n          defaultLineType: 'solid',\n          areaOpacity: 0.15\n        },\n        users: {\n          colors: [\n            '#1890ff', '#2fc25b', '#facc14', '#223273', '#8543e0',\n            '#13c2c2', '#3436c7', '#f04864', '#5cdbd3', '#6dc8ec'\n          ],\n          smooth: false,\n          lineWidth: 4,\n          showSymbol: true,\n          symbolSize: 8,\n          shadowBlur: 6,\n          useGradient: true,\n          legendIcon: 'rect',\n          defaultLineType: 'solid',\n          areaOpacity: 0.25\n        },\n        p90: {\n          colors: [\n            '#9b8bfe', '#26deca', '#f8c032', '#ff5722', '#07a2a4',\n            '#4cc9f0', '#4361ee', '#7209b7', '#f72585', '#3a0ca3'\n          ],\n          smooth: true,\n          lineWidth: 3,\n          showSymbol: false,\n          symbolSize: 6,\n          shadowBlur: 4,\n          useGradient: true,\n          legendIcon: 'diamond',\n          defaultLineType: 'dashed',\n          areaOpacity: 0.1\n        },\n        p99: {\n          colors: [\n            '#e84a5f', '#2a9d8f', '#e9c46a', '#264653', '#f4a261',\n            '#ff9f1c', '#2ec4b6', '#e71d36', '#011627', '#fdfffc'\n          ],\n          smooth: true,\n          lineWidth: 3,\n          showSymbol: false,\n          symbolSize: 6,\n          shadowBlur: 5,\n          useGradient: true,\n          legendIcon: 'triangle',\n          defaultLineType: 'dotted',\n          areaOpacity: 0.12\n        }\n      };\n      \n      // 如果没有对应的配置，使用默认配置\n      return configs[this.chartType] || configs.responseTime;\n    },\n    \n    // 获取线条类型\n    getLineType(index, styleConfig) {\n      const lineTypes = ['solid', 'dashed', 'dotted'];\n      \n      // 对于p90和p99图表，使用特定的线条类型\n      if (this.chartType === 'p90' || this.chartType === 'p99') {\n        return styleConfig.defaultLineType;\n      }\n      \n      // 对于其他图表，轮换使用不同的线条类型\n      return lineTypes[index % lineTypes.length];\n    },\n    \n    // 获取系列颜色\n    getSeriesColor(index, styleConfig) {\n      return styleConfig.colors[index % styleConfig.colors.length];\n    },\n    \n    // 获取区域样式\n    getAreaStyle(index, styleConfig) {\n      const color = this.getSeriesColor(index, styleConfig);\n      \n      // 如果不是单一系列或不使用渐变，则不显示区域\n      if (this.selectedSeries === 'All' || !styleConfig.useGradient) {\n        return null;\n      }\n      \n      // 使用渐变色\n      return {\n        color: {\n          type: 'linear',\n          x: 0,\n          y: 0,\n          x2: 0,\n          y2: 1,\n          colorStops: [{\n            offset: 0, \n            color: color + Math.floor(styleConfig.areaOpacity * 255).toString(16).padStart(2, '0') // 转换透明度为十六进制\n          }, {\n            offset: 1, \n            color: color + '00' // 完全透明\n          }]\n        },\n        origin: 'auto'\n      };\n    },\n    \n    // 组件销毁时清理资源\n    beforeDestroy() {\n      if (this.chartInstance) {\n        this.chartInstance.dispose();\n      }\n      window.removeEventListener('resize', this.resizeHandler);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.chart-container {\n  width: 100%;\n  height: 100%;\n}\n\n.chart-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 15px;\n  padding: 0 5px;\n}\n\n.chart-title {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #303133;\n  letter-spacing: 0.5px;\n}\n\n.series-selector {\n  width: 150px;\n  transition: all 0.3s ease;\n}\n\n.series-selector:hover {\n  transform: translateY(-1px);\n}\n\n.series-selector :deep(.el-input__inner) {\n  font-size: 12px;\n  height: 30px;\n  line-height: 30px;\n  border-radius: 15px;\n  border: 1px solid #e4e7ed;\n  padding-left: 15px;\n  transition: all 0.3s ease;\n}\n\n.series-selector :deep(.el-input__inner:hover),\n.series-selector :deep(.el-input__inner:focus) {\n  border-color: #5470c6;\n  box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.2);\n}\n\n.series-selector :deep(.el-input__suffix) {\n  right: 8px;\n}\n\n.option-label {\n  font-size: 12px;\n  color: #606266;\n  display: block;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n/* 下拉菜单样式 */\n:deep(.el-select-dropdown__item) {\n  font-size: 12px;\n  height: 30px;\n  line-height: 30px;\n}\n\n:deep(.el-select-dropdown__item.selected) {\n  color: #5470c6;\n  font-weight: 600;\n}\n\n:deep(.el-select-dropdown__item.hover) {\n  background-color: rgba(84, 112, 198, 0.1);\n}\n\n/* 自适应样式 */\n@media (max-width: 768px) {\n  .chart-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 10px;\n  }\n  \n  .series-selector {\n    width: 100%;\n  }\n}\n</style>\n", "import { render } from \"./ResponseTimeChart.vue?vue&type=template&id=507a96e2&scoped=true\"\nimport script from \"./ResponseTimeChart.vue?vue&type=script&lang=js\"\nexport * from \"./ResponseTimeChart.vue?vue&type=script&lang=js\"\n\nimport \"./ResponseTimeChart.vue?vue&type=style&index=0&id=507a96e2&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-507a96e2\"]])\n\nexport default __exports__", "import { render } from \"./PerformanceResult-Detail.vue?vue&type=template&id=ed76f3a4&scoped=true\"\nimport script from \"./PerformanceResult-Detail.vue?vue&type=script&lang=js\"\nexport * from \"./PerformanceResult-Detail.vue?vue&type=script&lang=js\"\n\nimport \"./PerformanceResult-Detail.vue?vue&type=style&index=0&id=ed76f3a4&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-ed76f3a4\"]])\n\nexport default __exports__"], "names": ["class", "style", "_createVNode", "_component_el_scrollbar", "height", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_component_el_card", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_component_el_button", "type", "onClick", "$options", "editTaskName", "$data", "taskName", "_component_el_icon", "_component_Edit", "taskType", "_createBlock", "_component_el_tag", "effect", "_hoisted_7", "_hoisted_8", "_toDisplayString", "desc", "inputDlg", "<PERSON><PERSON><PERSON>", "_component_el_input", "$event", "onBlur", "cancelEditing", "ref", "size", "_cache", "_withModifiers", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_component_icon", "icon", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "reportData", "avgTps", "toFixed", "_hoisted_16", "_hoisted_17", "formatResponseTime", "avgResponseTime", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "getCurrentCpuUsage", "_hoisted_24", "_hoisted_25", "getCurrentMemoryUsage", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_31", "executor", "_hoisted_32", "_hoisted_33", "formatTime", "create_time", "_hoisted_34", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "formatDuration", "duration", "_hoisted_40", "_hoisted_41", "errorRate", "_hoisted_42", "_component_el_menu", "activeIndex", "mode", "onSelect", "handleSelect", "_component_el_menu_item", "index", "_hoisted_43", "back", "_component_ArrowLeft", "saveReport", "_component_Check", "_component_el_dropdown", "trigger", "onCommand", "handleExportCommand", "dropdown", "_withCtx", "_component_el_dropdown_menu", "_component_el_dropdown_item", "command", "_component_Document", "_component_Monitor", "_component_DocumentCopy", "_component_Files", "disabled", "id", "_component_arrow_down", "showNotificationDialog", "_component_Bell", "handleMoreCommand", "_component_Trend<PERSON><PERSON>s", "_component_DataAnalysis", "_component_<PERSON><PERSON><PERSON>", "_component_Setting", "_createElementBlock", "_hoisted_44", "_hoisted_45", "_hoisted_46", "_component_el_divider", "direction", "_hoisted_47", "_component_SwitchButton", "rerunTest", "_hoisted_48", "getTaskTypeText", "task", "getRunPatternText", "getDistributedModeText", "distributed_mode", "getCurrentUserCount", "totalRequests", "successRequests", "isFilterMode", "toggleFilterMode", "_component_View", "_hoisted_49", "_hoisted_50", "_component_el_table", "data", "filteredMetrics", "border", "onSelectionChange", "handleSelectionChange", "row", "method", "name", "onRowClick", "handleRowClick", "_component_el_table_column", "width", "selectable", "prop", "label", "align", "default", "scope", "_component_el_tooltip", "content", "placement", "_hoisted_51", "getUserCountForRow", "_hoisted_52", "_hoisted_53", "_component_ResponseTimeChart", "chartTitle", "chartWidth", "chartHeight", "chartUnit", "xData", "seriesData", "responseTimeSeriesData", "key", "chartUpdate<PERSON>ey", "chartType", "_hoisted_54", "rpsSeriesData", "_hoisted_55", "tpsSeriesData", "_hoisted_56", "usersSeriesData", "_hoisted_57", "p90SeriesData", "_hoisted_58", "p99SeriesData", "_hoisted_59", "isEdit", "startEditing", "rows", "saveAnalysis", "placeholder", "_hoisted_60", "_hoisted_61", "_hoisted_62", "_hoisted_63", "getTestStatusType", "getTestStatusText", "_hoisted_64", "refreshGUI", "isTestRunning", "guiUrl", "_hoisted_66", "src", "onLoad", "args", "handleIframeLoad", "onError", "handleIframeError", "iframeError", "_hoisted_68", "_component_el_alert", "title", "description", "iframeErrorMessage", "_hoisted_69", "retryLoadGUI", "_hoisted_70", "_component_el_empty", "_hoisted_65", "getTestNotRunningMessage", "closable", "_hoisted_71", "_hoisted_72", "refreshMonitoringData", "_hoisted_73", "getWebSocketStatusText", "getWebSocketStatusType", "handleWebSocketStatusClick", "_hoisted_74", "_hoisted_75", "_hoisted_76", "_normalizeClass", "getCpuLevelClass", "monitoringData", "cpu_percent", "_hoisted_77", "getMemoryLevelClass", "memory_percent", "_hoisted_78", "disk_percent", "_hoisted_79", "formatBytes", "network_sent", "network_recv", "_hoisted_80", "_hoisted_81", "active_connections", "_hoisted_82", "current_rps", "_hoisted_83", "current_users", "_hoisted_84", "getErrorRateClass", "error_rate", "_hoisted_85", "_hoisted_86", "server_type", "_hoisted_87", "cpu_cores", "_hoisted_88", "total_memory", "_hoisted_89", "uptime", "_hoisted_90", "monitorUrl", "onKeyup", "_with<PERSON><PERSON><PERSON>", "setMonitorIframeUrl", "clearable", "resetMonitorIframeUrl", "monitorIframeUrl", "_hoisted_91", "frameborder", "_hoisted_93", "_hoisted_94", "refreshLogData", "clearLogs", "_component_el_switch", "autoRefresh", "onChange", "toggleAutoRefresh", "_hoisted_95", "logFilter", "prefix", "_component_Search", "_component_el_select", "logLevel", "_component_el_option", "value", "logCategory", "_Fragment", "_renderList", "logCategories", "filteredLogs", "length", "_hoisted_96", "log", "getLogClass", "level", "_hoisted_97", "timestamp", "_hoisted_98", "toUpperCase", "category", "_hoisted_99", "_resolveDynamicComponent", "getLogCategoryIcon", "formatted", "isFormatted", "getMethodClass", "_hoisted_100", "url", "status", "getStatusClass", "_hoisted_101", "message", "details", "showDetails", "_hoisted_102", "_hoisted_103", "_hoisted_104", "_component_el_dialog", "analysisDialogData", "visible", "_hoisted_105", "_hoisted_106", "_hoisted_107", "_component_el_progress", "percentage", "performance_score", "color", "getScoreColor", "_hoisted_108", "_hoisted_109", "bottlenecks", "_hoisted_110", "bottleneck", "getSeverityType", "severity", "_hoisted_111", "recommendations", "_hoisted_112", "_hoisted_113", "recommendation", "configDialogData", "taskConfig", "_hoisted_114", "shadow", "header", "_hoisted_115", "_component_el_row", "gutter", "_component_el_col", "span", "_hoisted_116", "_hoisted_117", "_hoisted_118", "_hoisted_119", "_hoisted_120", "_hoisted_121", "runPattern", "_hoisted_122", "_hoisted_123", "_hoisted_124", "_hoisted_125", "creator", "_hoisted_126", "_hoisted_127", "_hoisted_128", "_hoisted_129", "_hoisted_130", "concurrent_users", "users", "_hoisted_131", "_hoisted_132", "_hoisted_133", "_hoisted_134", "spawn_rate", "ramp_up", "_hoisted_135", "_hoisted_136", "qps_limit", "environment", "_hoisted_137", "_hoisted_138", "_hoisted_139", "_hoisted_140", "_hoisted_141", "base_url", "host", "_hoisted_142", "_hoisted_143", "protocol", "_hoisted_144", "_hoisted_145", "timeout", "notificationDialogData", "required", "cancelNotification", "top", "center", "footer", "_hoisted_172", "plain", "confirmSendNotification", "_component_Position", "_hoisted_146", "_hoisted_147", "_hoisted_148", "_component_el_form", "model", "rules", "notificationRules", "_component_el_form_item", "maxlength", "minlength", "_hoisted_149", "pushType", "_hoisted_150", "_component_Message", "_hoisted_151", "_component_Link", "_hoisted_152", "_component_ChatDotRound", "_hoisted_153", "_component_ChatRound", "webhook", "multiple", "recipients", "includes", "_hoisted_154", "_hoisted_155", "_hoisted_156", "_hoisted_157", "_hoisted_158", "reportName", "_hoisted_159", "_hoisted_160", "reportStatus", "getStatusText", "_hoisted_161", "_hoisted_162", "_hoisted_163", "_hoisted_164", "_hoisted_165", "_hoisted_166", "getSuccessRate", "_hoisted_167", "_hoisted_168", "_hoisted_169", "_hoisted_170", "_hoisted_171", "maxUsers", "avgUsers", "baselineDialogData", "_hoisted_174", "submitBaselineForm", "loading", "form", "baselineFormRules", "_component_el_input_number", "avg_response_time", "precision", "min", "avg_tps", "success_rate", "max", "avg_cpu", "avg_memory", "is_active", "_hoisted_173", "_component_InfoFilled", "baselineCompareDialogData", "_hoisted_177", "submitCompareBaseline", "selectedBaselineId", "baselineList", "baseline", "_hoisted_175", "_hoisted_176", "baselineCompareResultData", "_hoisted_210", "exportComparisonResult", "_hoisted_178", "_hoisted_179", "_hoisted_180", "_hoisted_181", "_hoisted_182", "_hoisted_183", "baseline_metrics", "_hoisted_184", "_hoisted_185", "_hoisted_186", "_hoisted_187", "_hoisted_188", "_hoisted_189", "_hoisted_190", "_hoisted_191", "_hoisted_192", "_hoisted_193", "_hoisted_194", "_hoisted_195", "_hoisted_196", "current_metrics", "getComparisonClass", "getComparisonText", "_hoisted_197", "_hoisted_198", "_hoisted_199", "_hoisted_200", "_hoisted_201", "_hoisted_202", "_hoisted_203", "_hoisted_204", "_hoisted_205", "_hoisted_206", "_hoisted_207", "conclusion", "_hoisted_208", "_hoisted_209", "generateComparisonConclusion", "$props", "selectedSeries", "updateLegend", "_normalizeStyle", "props", "String", "Array", "mounted", "this", "$nextTick", "initChart", "watch", "handler", "updateChart", "deep", "methods", "$refs", "chart", "echarts", "chartInstance", "window", "addEventListener", "resize", "console", "styleConfig", "getChartStyleConfig", "series", "map", "values", "showSymbol", "symbolSize", "smooth", "lineStyle", "lineWidth", "getLineType", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetY", "cap", "itemStyle", "getSeriesColor", "borderWidth", "borderColor", "areaStyle", "getAreaStyle", "emphasis", "focus", "option", "colors", "tooltip", "formatter", "params", "result", "axisValue", "for<PERSON>ach", "param", "undefined", "seriesName", "backgroundColor", "textStyle", "fontSize", "extraCssText", "grid", "left", "right", "bottom", "containLabel", "xAxis", "axisLabel", "rotate", "interval", "axisLine", "axisTick", "show", "yAxis", "nameLocation", "nameGap", "nameTextStyle", "splitLine", "legend", "s", "legendIcon", "itemWidth", "itemHeight", "itemGap", "animation", "animationDuration", "animationEasing", "setOption", "configs", "responseTime", "useGradient", "defaultLineType", "areaOpacity", "rps", "tps", "p90", "p99", "lineTypes", "x", "y", "x2", "y2", "colorStops", "offset", "Math", "floor", "toString", "padStart", "origin", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "removeEventListener", "resize<PERSON><PERSON>ler", "__exports__", "components", "Icon", "ResponseTimeChart", "Edit", "View", "SwitchButton", "InfoFilled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Search", "ArrowDown", "ArrowLeft", "Check", "Download", "Document", "Monitor", "DocumentCopy", "Files", "Bell", "Message", "Link", "ChatDotRound", "ChatRound", "MoreFilled", "DataAnalysis", "<PERSON><PERSON><PERSON>", "Setting", "Connection", "CircleClose", "reportId", "<PERSON><PERSON><PERSON><PERSON>", "tempTaskName", "textResult", "reportList", "realTimeData", "logData", "task_id", "project_id", "all", "system", "request", "error", "performance", "event", "wsConnection", "wsReconnectAttempts", "wsMaxReconnectAttempts", "wsReconnectInterval", "wsConnectionStatus", "refreshInterval", "targetServiceData", "tableLoading", "monitoringDataLoaded", "targetServiceDataLoaded", "logDataLoaded", "monitoringRefreshInterval", "targetServiceRefreshInterval", "responseTimeData", "rpsData", "tpsData", "usersData", "p50Data", "p90Data", "p95Data", "p99Data", "errorRateData", "interfaceChartData", "p50", "p95", "availableInterfaces", "realTimeUpdateInterval", "selectedRows", "hasGuiUrlLoaded", "computed", "logs", "filter", "toLowerCase", "sort", "a", "b", "Date", "Object", "keys", "<PERSON><PERSON><PERSON>", "push", "p50SeriesData", "p95SeriesData", "allMetrics", "getDetailedMetrics", "selected<PERSON><PERSON><PERSON>", "filteredData", "metric", "totalData", "detailData", "round", "initWebSocket", "cleanupWebSocket", "location", "baseUrl", "process", "VUE_APP_WS_BASE_URL", "wsUrl", "WebSocket", "connectionTimeout", "setTimeout", "readyState", "CONNECTING", "close", "handleWebSocketError", "onopen", "clearTimeout", "startHeartbeat", "$message", "success", "onmessage", "JSON", "parse", "handleWebSocketMessage", "onerror", "onclose", "stopHeartbeat", "shouldReconnect", "attemptReconnect", "clearReconnectTimer", "code", "warning", "delay", "pow", "errorMessage", "enablePollingMode", "clearInterval", "setInterval", "loadMonitoringData", "loadTargetServiceData", "loadLogData", "heartbeatInterval", "OPEN", "send", "stringify", "reconnectWebSocket", "updateChartData", "total", "updateBasicMetricsFromWebSocket", "addPerformanceLog", "detailed_stats", "$forceUpdate", "enhanceLogData", "slice", "scrollToLogBottom", "addSystemResourceLog", "addEventLog", "verifyTestCompletion", "stopAllRealTimeUpdates", "addRequestLog", "jsonStart", "indexOf", "jsonEnd", "lastIndexOf", "jsonStr", "substring", "jsonData", "e", "formatLogMessage", "methodMatch", "match", "statusMatch", "logContainer", "document", "querySelector", "scrollTop", "scrollHeight", "now", "lastSystemLog", "find", "lastTime", "toISOString", "cpu", "memory", "disk", "lastPerfLog", "reqData", "isSuccess", "response_time", "exception", "parseInt", "limit", "response", "$api", "getTaskReportLogs", "$confirm", "confirmButtonText", "cancelButtonText", "dangerouslyUseHTMLString", "info", "taskId", "Error", "envId", "env", "env_id", "runTask", "rerun", "copy_settings", "new_report_name", "replace", "newReportId", "report_id", "$router", "reload", "getTaskReportDetail", "latestReportData", "isStatusCompleted", "hasEndTime", "endTime", "hasFinalData", "assign", "generateChartData", "loadReportData", "totalStats", "min_response_time", "minResponseTime", "max_response_time", "maxResponseTime", "median_response_time", "p50ResponseTime", "p90_response_time", "p90ResponseTime", "p95_response_time", "p95ResponseTime", "p99_response_time", "p99ResponseTime", "num_requests", "num_failures", "failedRequests", "currentUsers", "elapsed_time", "resultAnalyse", "generateAnalysisResult", "startRealTimeDataUpdate", "analysisText", "successRate", "avgCpu", "avgMemory", "getSystemResourceStatus", "currentData", "current", "percent", "network", "bytes_sent", "bytes_recv", "connections", "count", "getTargetServiceStatus", "service_status", "connection_pool_active", "connection_pool_total", "db_connections", "cache_hit_rate", "response_time_trend", "labels", "recent_errors", "initEmptyChartData", "dataUpdated", "reportResult", "stats_history", "updateChartDataFromHistory", "updateChartDataFromDetailedStats", "generateChartFromReportData", "tableElement", "table", "statsHistory", "lastStats", "stat", "toLocaleTimeString", "current_tps", "p50_response_time", "stats", "getCorrectHttpMethod", "interfaceName", "getCorrectInterfaceName", "<PERSON><PERSON><PERSON>", "dataPoints", "avgResponse", "avgRps", "p50Response", "p90Response", "p95Response", "p99Response", "startTime", "i", "time", "getTime", "variation", "addVariation", "baseValue", "variationPercent", "random", "newValue", "Number", "generateRealisticData", "trend", "fetchRealTimeReportData", "gui_url", "updateReportMetrics", "addRealTimeDataFromStats", "stopRealTimeDataUpdate", "stopMonitoringRefresh", "stopTargetServiceRefresh", "detailedStats", "entries", "generateNextValue", "dataArray", "lastValue", "range", "max<PERSON><PERSON><PERSON>", "change", "shift", "formatDetailedStats", "calculateRPS", "calculateAvgRPS", "avgTpsAvg", "results", "interfaceData", "interfaceMetrics", "success_requests", "totalMetrics", "calculateTotalFromInterfaces", "unshift", "totalSuccessRequests", "totalFailedRequests", "totalRps", "totalTps", "totalUsers", "Infinity", "weightedAvgResponseTime", "weightedP50ResponseTime", "weightedP90ResponseTime", "weightedP95ResponseTime", "weightedP99ResponseTime", "item", "parseFloat", "calculateTotalStats", "detailedResults", "totalResponseTime", "totalP50", "totalP90", "totalP95", "totalP99", "generateReportSummary", "avgRT", "p50RT", "p90RT", "p95RT", "p99RT", "toLocaleString", "seconds", "hours", "minutes", "secs", "bytes", "isNaN", "k", "sizes", "types", "pattern", "patterns", "modes", "statuses", "rate", "then", "catch", "input", "saveTaskName", "startTargetServiceRefresh", "startMonitoringRefresh", "history", "filename", "exportSingleReport", "mockGenerateHtmlReport", "mockGeneratePdfReport", "exportTestData", "format", "blob", "Blob", "link", "createElement", "href", "URL", "createObjectURL", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "Promise", "resolve", "htmlContent", "generateHtmlReportContent", "pdf<PERSON><PERSON>nt", "handleNotificationCommand", "notificationFormRef", "resetFields", "validate", "async", "valid", "sendNotification", "mockSendNotification", "generateNotificationContent", "formattedTime", "createTime", "statusText", "split", "line", "join", "iframe", "createBaseline", "compareWithBaseline", "analyzePerformance", "showTestConfig", "project", "$route", "projectId", "baselineFormRef", "getBaselines", "isArray", "baselines", "selectedBaseline", "baselineData", "baselineDetailResponse", "getBaselineDetail", "detailError", "baseline_id", "comparisonResult", "baselineMetrics", "safeParseFloat", "cpu_usage", "memory_usage", "apiBaselineMetrics", "finalBaselineMetrics", "analysisResult", "generateRealAnalysis", "showAnalysisResults", "performanceScore", "responseTimeScore", "userCount", "expectedTps", "tpsScore", "errorScore", "cpuScore", "memoryScore", "trendAnalysis", "error_rate_trend", "tps_trend", "trend_analysis", "analysisData", "showTestConfigDialog", "concurrencyNumber", "path", "httpMethods", "startsWith", "isHttpMethod", "extractInterfaceName", "score", "typeMap", "updateTaskReportDetail", "column", "toggleRowSelection", "val", "checkedRows", "getSelectionRows", "test", "currentValue", "baselineValue", "metricType", "higherIsBetter", "diff", "percentDiff", "diffText", "percentText", "metrics", "responseTimeDiff", "abs", "tpsDiff", "successRateDiff", "cpuDiff", "memoryDiff", "performanceIssues", "issueCount", "num", "isFinite", "query", "beforeUnmount", "render"], "sourceRoot": ""}