(function(){"use strict";var e={31552:function(e,t,r){r.d(t,{$:function(){return u}});r(44114);var a=r(50788),n=r.n(a),s=r(39325),o=r(51219),p=r(84130),c=r.n(p);const i="http://119.29.101.15:5001";n().defaults.baseURL=i,n().defaults.timeout=8e4,n().defaults.validateStatus=function(e){return!0};const u={baseURL:i};n().defaults.withCredentials=!0,n().interceptors.request.use(e=>(c().start(),e)),n().interceptors.response.use(e=>(c().done(),e)),n().interceptors.request.use(e=>{const t=e.url,r=/^\/records\/\d+(?:\/report)?\/?/;return r.test(t)||"/users/login/"===t||"/users/user/"===t||(e.headers.Authorization="Bearer "+window.sessionStorage.getItem("token")),e}),n().interceptors.response.use(function(e){return 200===e.status||201===e.status||204===e.status||(401!==e.status||"/users/login/"===e.config.url||e.config.url.match(/^\/records\/\d+(?:\/report)?\/?/)?400===e.status||401===e.status?(0,o.nk)({message:e.data.message,type:"warning",duration:1e3}):500===e.status?(0,o.nk)({message:"服务异常，请联系开发人员处理！",type:"error",duration:1e3}):404===e.status||(0,o.nk)({message:e.data,type:"warning",duration:1e3}):(window.localStorage.removeItem("token"),console.log(e.config.url),s.A.push({name:"login"}),(0,o.nk)({message:"您未登录,请先进行登录!",type:"warning",duration:1e3}))),e}),t.A={uploadApi:{url:n().defaults.baseURL+"/upload/"},login(e){return n().post("/users/login/",e)},getAllUsers(e,t){return n().get(e,{params:{project:t}})},getExcludeUsers(e){return n().get("/users/user/exclude_project/",{params:{project:e}})},addExcludeUser(e){return n().post("/users/user/add_exclude/",e)},createUser(e){return n().post("/users/user/",e)},updateUser(e,t){return n().patch(`/users/user/${e}/`,t)},deleteUser(e){return n()["delete"](`/users/user/${e}/`)},getProjects(){return n().get("/projects/")},getProject(e){return n().get(`/projects/${e}/`)},delProject(e){return n()["delete"](`/projects/${e}/`)},createProjects(e){return n().post("/projects/",e)},updateProjects(e,t){return n().patch(`/projects/${e}/`,t)},getInterfaces(e,t,r,a,s,o,p){return n().get("/interfaces/",{params:{project:e,type:t,page:r,size:a,name:s,method:o,url:p}})},delInterface(e){return n()["delete"](`/interfaces/${e}/`)},createInterface(e){return n().post("/interfaces/",e)},updateInterface(e,t){return n().patch(`/interfaces/${e}/`,t)},getTreeNode(e){return n().get("/treeNode/",{params:e})},deleteTreeNode(e){return n()["delete"](`/treeNode/${e}/`)},createTreeNode(e){return n().post("/treeNode/",e)},updateTreeNode(e,t){return n().patch(`/treeNode/${e}/`,t)},getNewInterfaces(e,t,r,a,s){return n().get("/newinterfaces/",{params:{treenode_id:e,project:t,name:r,creator:s,status:a}})},getNewInterface(e){return n().get(`/newinterfaces/${e}/`)},deleteNewInterface(e){return n()["delete"](`/newinterfaces/${e}/`)},deleteAllNewInterfaces(e){return n().post("/newinterfaces/delete_batch/",e)},createNewInterface(e){return n().post("/newinterfaces/",e)},updateNewInterface(e,t){return n().patch(`/newinterfaces/${e}/`,t)},runNewCase(e){return n().post("/newinterfaces/run/",e)},getHooks(e,t,r){return n().get("/wxPush/",{params:{project_id:e,page:t,size:r}})},deleteHook(e){return n()["delete"](`/wxPush/${e}/`)},createHook(e){return n().post("/wxPush/",e)},updateHook(e,t){return n().patch(`/wxPush/${e}/`,t)},getTestScenes(e){return n().get("/test_scenes/",{params:e})},getSceneInfo(e){return n().get(`/test_scenes/${e}/`)},deleteTestScene(e){return n()["delete"](`/test_scenes/${e}/`)},createTestScene(e){return n().post("/test_scenes/",e)},updateTestScene(e,t){return n().patch(`/test_scenes/${e}/`,t)},updateSceneDataOrder(e){return n().put("/test_scene_steps/order/",e)},getSceneData(e){return n().get("/test_scene_steps/",{params:{scene:e}})},addSceneData(e){return n().post("/test_scene_steps/",e)},deleteSceneData(e){return n()["delete"](`/test_scene_steps/${e}/`)},getTestSteps(e){return n().get("/test_steps/",{params:e})},getTestStepInfo(e){return n().get(`/test_steps/${e}/`)},deleteTestStep(e){return n()["delete"](`/test_steps/${e}/`)},createTestStep(e){return n().post("/test_steps/",e)},updateTestStep(e,t){return n().patch(`/test_steps/${e}/`,t)},getTestPlans(e,t){return n().get("/test_plans/",{params:{project:e,name:t}})},deleteTestPlan(e){return n()["delete"](`/test_plans/${e}/`)},createTestPlan(e){return n().post("/test_plans/",e)},createTestPlanScene(e,t){return n().post(`/test_plans/${e}/add_new_scenes/`,t)},deleteTestPlanScene(e,t){return n().post(`/test_plans/${e}/remove_new_scene/`,t)},updateTestPlan(e,t){return n().patch(`/test_plans/${e}/`,t)},getTestEnvs(e){return n().get("/test_envs/",{params:{project:e}})},getEnvInfo(e,t){return n().get(`/test_envs/${e}/`,{params:{project:t}})},deleteTestEnv(e){return n()["delete"](`/test_envs/${e}/`)},createTestEnv(e){return n().post("/test_envs/",e)},updateTestEnv(e,t){return n().patch(`/test_envs/${e}/`,t)},getCrons(e){return n().get("/crontab_tasks/",{params:{project:e}})},deleteCron(e){return n()["delete"](`/crontab_tasks/${e}/`)},createCron(e){return n().post("/crontab_tasks/",e)},updateCron(e,t){return n().patch(`/crontab_tasks/${e}/`,t)},getTestRecord(e){return n().get("/records/",{params:e})},getRecordInfo(e){return n().get(`/records/${e}/`)},getTestReport(e){return n().get(`/records/${e}/report/`)},getBugs(e){return n().get("/bugs/",{params:e})},createBugs(e){return n().post("/bugs/",e)},updateBug(e,t){return n().patch(`/bugs/${e}/`,t)},deleteBug(e){return n()["delete"](`/bugs/${e}/`)},getBugLogs(e){return n().get("/blogs/",{params:e})},runTest(e){return n().post("/runTest/",e)},runCase(e){return n().post("/test_steps/run/",e)},runScene(e,t){return n().post(`/test_scenes/${e}/run/`,t)},runCases(e,t){return n().post(`/TestCase/${e}/run/`,t)},runPlan(e,t){return n().post(`/test_plans/${e}/run/`,t)},uploadFile(e){return n().post("/upload/",e)},getFiles(){return n().get("/upload/")},deleteFile(e){return n()["delete"](`/upload/${e}/`)},getTestCase(e,t,r,a){return n().get("/TestCase/",{params:{project_id:e,page:t,name:r,creator:a}})},getTestCase_(e){return n().get("/TestCase/",{params:e})},delTestCase(e){return n()["delete"](`/TestCase/${e}/`)},createTestCase(e){return n().post("/TestCase/",e)},updateTestCase(e,t){return n().patch(`/TestCase/${e}/`,t)},detailTestCase(e){return n().patch(`/TestCase/${e}/`)},getTestCaseStep(e){return n().get("/TestCase_Setp/",{params:{case:e}})},createsTestCaseStep(e){return n().post("/TestCase_Setp/batch_create/",e)},updateTestCaseStep(e,t){return n().patch(`/TestCase_Setp/${e}/`,t)},createTestCaseStep(e){return n().post("/TestCase_Setp/",e)},delTestCaseStep(e){return n()["delete"](`/TestCase_Setp/${e}/delete_node`)},updateCaseStepOrder(e){return n().put("/TestCase_Setp/order/",e)},createStepControll(e){return n().post("/StepControll/",e)},copyStepControll(e){return n().post("/StepControll/copyStep/",e)},delStepControll(e){return n()["delete"](`/StepControll/${e}/`)},updateStepControll(e,t){return n().patch(`/StepControll/${e}/`,t)},updatesStepControll(e){return n().put("/StepControll/batch_updateStep/",e)},getYApiImport(e){return n().post("/yapi/",e)},getCurlImport(e){return n().post("/curl/",e)},getPostmanImport(e){return n().post("/postman/",e,{headers:{"Content-Type":"multipart/form-data"}})},getApipostImport(e){return n().post("/apipost/",e,{headers:{"Content-Type":"multipart/form-data"}})},getSwaggerImport(e){return e instanceof FormData?n().post("/swagger/file/",e,{headers:{"Content-Type":"multipart/form-data"}}):n().post("/swagger/url/",e)},getJsFetchImport(e){return n().post("/jsfetch/",e)},getProjectBoard(e){return n().post("/ProjectBoard/",e)},getMock(e){return n().get(`/mock/${e}/`)},createMock(e){return n().post("/mock/",e)},updateMock(e,t){return n().patch(`/mock/${e}/`,t)},createDetail(e){return n().post("/mock_detail/",e)},updateDetail(e,t){return n().patch(`/mock_detail/${e}/`,t)},delDetail(e){return n()["delete"](`/mock_detail/${e}/`)},getServer(e){return n().get(`/server/${e}/`)},getServers(e,t){return n().get("/server/",{params:{project_id:e,page:t}})},createServer(e){return n().post("/server/",e)},updateServer(e,t){return n().patch(`/server/${e}/`,t)},delServer(e){return n()["delete"](`/server/${e}/`)},getPresetting(e){return n().get("/presetting/",{params:e})},createPresetting(e){return n().post("/presetting/",e)},updatePresetting(e,t){return n().patch(`/presetting/${e}/`,t)},setPresetting(e){return n().post("/presetting/save_presetting/",e)},delPresetting(e){return n()["delete"](`/presetting/${e}/`)},getPerformanceTask(e,t,r){return n().get("/performanceTask/",{params:{project_id:e,page:t,taskName:r}})},getPerformanceTasks(e){return n().get("/performanceTask/",{params:{...e,no_page:!0}})},createPerformanceTask(e){return n().post("/performanceTask/",e)},updatePerformanceTask(e,t){return n().patch(`/performanceTask/${e}/`,t)},delPerformanceTask(e){return n()["delete"](`/performanceTask/${e}/`)},runTask(e,t){return n().post(`/performanceTask/${e}/run/`,t)},runPerformanceTestOptimized(e,t){return n().post(`/performanceTask/${e}/run_optimized/`,t)},stopPerformanceTest(e){return n().post(`/performanceTask/${e}/stop/`)},getTaskReports(e){return n().get("/taskReport/",{params:e})},getTaskReportDetail(e){return n().get(`/taskReport/${e}/`)},getTaskReportLogs(e,t){return n().get(`/taskReport/${e}/logs/`,{params:t})},updateTaskReportDetail(e,t){return n().patch(`/taskReport/${e}/`,t)},getTaskReport(e){return n().get("/taskReport/statistics/",{params:e})},taskReport(e,t){const r={params:e};return t&&(r.responseType=t),n().get("/taskReport/export_all/",r)},delTaskReport(e){return n()["delete"](`/taskReport/${e}/`)},getTargetServiceStatus(e){return n().get(`/taskReport/${e}/target_service_status/`)},getSystemResourceStatus(){return n().get("/systemResource/current_status/")},compareTaskPerformance(e){return n().post("/taskReport/compare_task_performance/",e)},generateComparisonReport(e){return n().post("/taskReport/generate_comparison_report/",e)},getPerformanceDashboard(e){return n().get("/taskReport/performance_dashboard/",{params:e})},analyzePerformanceReport(e,t){return n().post(`/taskReport/${e}/analyze_performance/`,t)},generateReportHtml(e,t){return n().post(`/taskReport/${e}/generate_report_html/`,t)},getReportTemplates(){return n().get("/taskReport/report_templates/")},exportSingleReport(e){return n().get(`/taskReport/${e}/export/`,{responseType:"blob"})},getBaselineDetail(e){return n().get(`/taskReport/${e}/get_baseline/`)},compareWithBaseline(e,t){return n().post(`/taskReport/${e}/compare_with_baseline/`,t)},autoCreateBaseline(e,t){return n().post(`/taskReport/${e}/auto_create_baseline/`,t)},getBaselineStatistics(e){return n().get("/taskReport/baseline_statistics/",{params:e})},getAlertStatus(e){return n().get("/taskReport/alert_status/",{params:e})},addAlertRule(e){return n().post("/taskReport/add_alert_rule/",e)},startAlertMonitoring(){return n().post("/taskReport/start_alert_monitoring/")},stopAlertMonitoring(){return n().post("/taskReport/stop_alert_monitoring/")},acknowledgeAlert(e){return n().post("/taskReport/acknowledge_alert/",e)},getAlertRules(e){return n().get("/taskReport/alert_rules/",{params:e})},getAlertHistory(e){return n().get("/taskReport/alert_history/",{params:e})},updateAlertRule(e,t){return n().patch(`/taskReport/${e}/update_alert_rule/`,t)},deleteAlertRule(e){return n()["delete"](`/taskReport/${e}/delete_alert_rule/`)},getBaselines(e){return n().get("/taskReport/list_baselines/",{params:e})},createBaseline(e){return n().post("/taskReport/create_baseline/",e)},updateBaseline(e,t){return n().patch(`/taskReport/${e}/update_baseline/`,t)},deleteBaseline(e){return n()["delete"](`/taskReport/${e}/delete_baseline/`)},createWorkflow(e){return n().post("/taskReport/create_workflow/",e)},getWorkflows(e){return n().get("/taskReport/list_workflows/",{params:e})},getWorkflowDetail(e){return n().get(`/taskReport/${e}/get_workflow/`)},addWorkflowStep(e,t){return n().post(`/taskReport/${e}/add_workflow_step/`,t)},addWorkflowTrigger(e,t){return n().post(`/taskReport/${e}/add_workflow_trigger/`,t)},executeWorkflow(e,t){return n().post(`/taskReport/${e}/execute_workflow/`,t)},stopWorkflow(e){return n().post(`/taskReport/${e}/stop_workflow/`)},getWorkflowExecutionHistory(e){return n().get("/taskReport/execution_history/",{params:e})},getWorkflowStatistics(e){return n().get("/taskReport/workflow_statistics/",{params:e})},getWorkflowTemplates(){return n().get("/taskReport/workflow_templates/")},exportTestData(e){return n().post("/taskReport/export_data/",e,{responseType:"blob"})},importTestData(e){return n().post("/taskReport/import_data/",e,{headers:{"Content-Type":"multipart/form-data"}})},getExportTemplate(e){return n().get("/taskReport/export_template/",{params:e})},getSystemResourceHistory(e){return n().get("/systemResource/history/",{params:e})},getProcessStatus(e){return n().get("/systemResource/process_status/",{params:e})},testServerConnection(e){return n().post(`/server/${e}/test_connection/`)},getServerSystemInfo(e){return n().post(`/server/${e}/get_system_info/`)},getClusterStatus(e){return n().get("/server/cluster_status/",{params:e})},stopPerformanceTask(e){return n().post(`/performanceTask/${e}/stop/`)},getDistributedTestStatus(e){return n().get(`/performanceTask/${e}/distributed_status/`)},stopDistributedTest(e){return n().post(`/performanceTask/${e}/stop_distributed_test/`)},testProtocolConnection(e){return n().post("/performanceTask/test_protocol/",e)},runProtocolTest(e,t){return n().post(`/performanceTask/${e}/run_protocol_test/`,t)},getProtocolTestStatus(e){return n().get(`/performanceTask/${e}/protocol_test_status/`)},getTaskScenes(e,t){return n().get("/taskScence/",{params:{task:e,name:t}})},getTaskScene(e){return n().get(`/taskScence/${e}/`)},createTaskScene(e){return n().post("/taskScence/",e)},updateTaskScene(e,t){return n().patch(`/taskScence/${e}/`,t)},deleteTaskScene(e){return n()["delete"](`/taskScence/${e}/`)},exportTaskScene(){return n().get("/taskScence/export/")},createSceneStep(e){return n().post("/taskScenceStep/",e)},getSceneStep(e){return n().get("/taskScenceStep/",{params:{type:"api",scence:e}})},updateSceneStep(e,t){return n().patch(`/taskScenceStep/${e}/`,t)},batchUpdateSceneStep(e){return n().post("/taskScenceStep/batchSaveSetp/",e)},batchSaveApiStep(e){return n().post("/taskScenceStep/batchSaveApiSetp/",e)},deleteSceneStep(e){return n()["delete"](`/taskScenceStep/${e}/`)},sendReportNotification(e){return n().post("/taskReport/send_notification/",e)},getNotificationSettings(e){return n().get("/taskReport/notification_settings/",{params:e})},updateNotificationSettings(e){return n().put("/taskReport/notification_settings/",e)},testNotificationConnection(e){return n().post("/taskReport/test_notification/",e)},getNotificationHistory(e){return n().get("/taskReport/notification_history/",{params:e})},getTaskSceneStep(e){return n().get("/performanceScenceStep/",{params:{scence:e}})},createTaskSceneStep(e){return n().post("/performanceScenceStep/",e)},updateTaskSceneStep(e,t){return n().patch(`/performanceScenceStep/${e}/`,t)},deleteTaskSceneStep(e,t){return n()["delete"](`/performanceScenceStep/${e}/`,{params:{scence:t}})},batchTaskSceneStep(e){return n().post("/performanceScenceStep/batchTaskScenceStep/",e)},debugScenario(e){return n().post("/scenarioDebug/debug_scenario/",e)},testServerConnections(e,t){return n().post(`/server/${t}/test_connection/`,e)},getPerformanceServers(e){return n().get("/server/",{params:e})},checkPortAvailability(e){return n().post("/server/check_port/",e)},getServerStatus(e){return n().get(`/server/${e}/status/`)},getServersForExecution(e){return n().get("/server/",{params:e})}}},39325:function(e,t,r){var a=r(81387),n=r(84130),s=r.n(n),o=r(55129);const p=[{path:"/",name:"home",component:()=>Promise.all([r.e(103),r.e(264),r.e(594)]).then(r.bind(r,22082)),redirect:"/project",children:[{path:"/project",name:"project",component:()=>r.e(83).then(r.bind(r,29083)),meta:{name:"项目首页"}},{path:"/testenv",name:"testenv",component:()=>Promise.all([r.e(629),r.e(484)]).then(r.bind(r,67247)),meta:{name:"测试环境"}},{path:"/crontab",name:"crontab",component:()=>r.e(403).then(r.bind(r,57403)),meta:{name:"定时任务"}},{path:"/report/:id",name:"report",component:()=>Promise.all([r.e(629),r.e(542)]).then(r.bind(r,51821)),meta:{name:"测试报告"}},{path:"/bugs",name:"bug",component:()=>Promise.all([r.e(629),r.e(903)]).then(r.bind(r,62278)),meta:{name:"bug管理"}},{path:"/records",name:"records",component:()=>Promise.all([r.e(830),r.e(589)]).then(r.bind(r,56589)),meta:{name:"测试报表"}},{path:"/users",name:"user",component:()=>r.e(911).then(r.bind(r,32911)),meta:{name:"用户管理"}},{path:"/caseManage",name:"caseManage",component:()=>r.e(173).then(r.bind(r,41173)),meta:{name:"用例管理"}},{path:"/reportPush",name:"push",component:()=>r.e(323).then(r.bind(r,39323)),meta:{name:"报告通知"}},{path:"/PerformanceTask",name:"Performance",component:()=>r.e(315).then(r.bind(r,34315)),meta:{name:"性能任务"}},{path:"/maskMgrDetail",name:"maskMgrDetail",component:()=>Promise.all([r.e(629),r.e(704),r.e(682),r.e(491),r.e(520)]).then(r.bind(r,68774)),meta:{name:"任务管理详情"}},{path:"/PerformanceResult",name:"PerformanceResult",component:()=>Promise.all([r.e(830),r.e(838)]).then(r.bind(r,71838)),meta:{name:"性能结果"}},{path:"/PerformanceResult-Detail/:id",name:"PerformanceResult-Detail",component:()=>Promise.all([r.e(103),r.e(296)]).then(r.bind(r,84296)),meta:{name:"性能结果详情"}},{path:"/performance/comparison",name:"TaskComparison",component:()=>r.e(233).then(r.bind(r,98233)),meta:{name:"任务对比"}},{path:"/PerformanceMonitor/:taskId?",name:"PerformanceMonitor",component:()=>r.e(441).then(r.bind(r,72441)),meta:{name:"性能实时监控"}},{path:"/PerformanceExecutionOptimized",name:"PerformanceExecutionOptimized",component:()=>r.e(519).then(r.bind(r,63519)),meta:{name:"性能测试执行"}},{path:"/server",name:"server",component:()=>r.e(569).then(r.bind(r,65950)),meta:{name:"机器管理"}},{path:"/terminal",name:"terminal",component:()=>r.e(334).then(r.bind(r,49334)),meta:{name:"终端"}},{path:"/makeSet",name:"makeSet",component:()=>Promise.all([r.e(682),r.e(57)]).then(r.bind(r,70682)),meta:{name:"预设置"}},{path:"/PerformanceAlert",name:"PerformanceAlert",component:()=>r.e(619).then(r.bind(r,32619)),meta:{name:"性能告警"}},{path:"/PerformanceBaseline",name:"PerformanceBaseline",component:()=>r.e(34).then(r.bind(r,29034)),meta:{name:"基准线管理"}},{path:"/new-interface",name:"interfaceNew",component:()=>Promise.all([r.e(629),r.e(103),r.e(704),r.e(579)]).then(r.bind(r,99128)),meta:{name:"接口管理"}},{path:"/BlindTest",name:"BlindTest",component:()=>r.e(719).then(r.bind(r,23719)),meta:{name:"接口盲测"}},{path:"/TestCase",name:"TestCase",component:()=>Promise.all([r.e(629),r.e(238)]).then(r.bind(r,72773)),meta:{name:"用例管理"}},{path:"/TestCaseDetail",name:"TestCaseDetail",component:()=>Promise.all([r.e(629),r.e(704),r.e(491),r.e(628)]).then(r.bind(r,46050)),meta:{name:"用例详情"}},{path:"/new-testplan",name:"new-testplan",component:()=>Promise.all([r.e(629),r.e(103),r.e(61)]).then(r.bind(r,90568)),meta:{name:"测试计划"}},{path:"/test-report-detail",name:"test-report-detail",component:()=>r.e(815).then(r.bind(r,80815)),meta:{name:"测试报告详情"}}]},{path:"/login",name:"login",component:()=>Promise.all([r.e(103),r.e(264),r.e(594)]).then(r.bind(r,92598))},{path:"/allProject",name:"allProject",component:()=>Promise.all([r.e(103),r.e(926)]).then(r.bind(r,78926))},{path:"/404",name:"404",component:()=>Promise.all([r.e(264),r.e(627)]).then(r.bind(r,4627))},{path:"/:catchAll(.*)",redirect:"/404"},{path:"/PushEport/:id",name:"PushrEport",component:()=>Promise.all([r.e(629),r.e(542)]).then(r.bind(r,51821)),meta:{name:"推送的测试报告查看"}}],c=(0,a.aE)({history:(0,a.Bt)(),routes:p});c.beforeEach((e,t,r)=>{s().start(),e.meta.name&&o.A.commit("addTags",{name:e.meta.name,path:e.path});const a=window.sessionStorage.getItem("token");"createUser"===e.name||"login"===e.name||"PushrEport"===e.name||a?r():r({name:"login"})}),c.afterEach(()=>{s().done()}),t.A=c},55129:function(e,t,r){r(44114),r(18111),r(22489),r(20116);var a=r(60782),n=r(31552);t.A=(0,a.y$)({state:{tags:[],envId:null,interfaces:[],testScents:[],testPlans:[],testEnvs:[],cronTabs:[],Users:[]},getters:{interfaces1(e){return e.interfaces.result.filter(e=>"1"===e.type)},interfaces2(e){return e.interfaces.result.filter(e=>"2"===e.type)}},mutations:{addTags(e,t){const r=e.tags.find(e=>e.path===t.path);r||e.tags.push(t)},delTags(e,t){e.tags=e.tags.filter(e=>e.path!==t)},selectPro(e,t){e.pro=t},CaseInfo(e,t){e.case=t},clearCaseInfo(e){e.case=null},servers(e,t){e.server=t},clearServers(e){e.server=null},checkedTask(e,t){e.perfTask=t},clearTask(e){e.perfTask=null},selectEnv(e,t){e.envId=t},selectEnvInfo(e,t){e.envInfo=t},clearEnvId(e){e.envId=null},updateInterfaces(e,t){e.interfaces=t},updateTestScents(e,t){e.testScents=t},updateTestPlans(e,t){e.testPlans=t},updateTestEnvs(e,t){e.testEnvs=t},updatecronTabs(e,t){e.cronTabs=t},updateUser(e,t){e.Users=t}},actions:{async getAllEnvs(e){const t=await n.A.getTestEnvs(e.state.pro.id);200===t.status&&e.commit("updateTestEnvs",t.data)},async getAllPlan(e){const t=await n.A.getTestPlans(e.state.pro.id);200===t.status&&e.commit("updateTestPlans",t.data)},async getAllUser(e){const t=await n.A.getAllUsers("/users/user/",e.state.pro.id);200===t.status&&e.commit("updateUser",t.data.result)}},modules:{}})},83746:function(e,t,r){var a=r(45130),n=r(56768);function s(e,t,r,a,s,o){const p=(0,n.g2)("router-view"),c=(0,n.g2)("el-config-provider");return(0,n.uX)(),(0,n.Wv)(c,{locale:a.locale},{default:(0,n.k6)(()=>[(0,n.bF)(p)]),_:1},8,["locale"])}var o=r(97722),p=r(55186),c={components:{[o.H6.name]:o.H6},setup(){return{locale:p.A}},created(){window.addEventListener("beforeunload",()=>{sessionStorage.setItem("messageStore",JSON.stringify(this.$store.state))});const e=sessionStorage.getItem("messageStore");e&&this.$store.replaceState(Object.assign(this.$store.state,JSON.parse(e)))}},i=r(71241);const u=(0,i.A)(c,[["render",s]]);var l=u,d=r(39325),m=r(55129),g=r(53390),f=(r(4188),r(57477)),h=e=>{e.use(g.A,{locale:p.A,size:"default"});for(const[t,r]of Object.entries(f))e.component(t,r)},b=r(31552);function S(e,t){var r={"M+":t.getMonth()+1,"d+":t.getDate(),"h+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds(),"q+":Math.floor((t.getMonth()+3)/3),S:t.getMilliseconds()};for(var a in/(y+)/.test(e)&&(e=e.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length))),r)new RegExp("("+a+")").test(e)&&(e=e.replace(RegExp.$1,1==RegExp.$1.length?r[a]:("00"+r[a]).substr((""+r[a]).length)));return e}function k(){const e=new Date,t=e.getFullYear(),r=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0"),n=String(e.getHours()).padStart(2,"0"),s=String(e.getMinutes()).padStart(2,"0"),o=String(e.getSeconds()).padStart(2,"0"),p=String(e.getMilliseconds()).padStart(3,"0"),c=-e.getTimezoneOffset(),i=String(Math.floor(c/60)).padStart(2,"0"),u=String(c%60).padStart(2,"0"),l=c>=0?`+${i}:${u}`:`-${i}:${u}`,d=`${t}-${r}-${a}T${n}:${s}:${o}.${p}${l}`;return d}var T={rTime(e){return S("yyyy-MM-dd hh:mm:ss",new Date(e))},rDate(e){return S("yyyy-MM-dd",new Date(e))},newTime(){return k()}},_=(r(44114),r(18111),r(7588),r(91006)),v={chart1(e,t,r){const a=_.Ts(e);let n=[];t.forEach(e=>{n.push(t[0])});const s=["#7b8b83","#28a745","#ffc107","#dc3545","#409EFF","#4de1cb"],o={grid:{top:"3%",left:"20%",bottom:"3%"},xAxis:{show:!1},yAxis:[{show:!0,data:r,inverse:!0,axisLine:{show:!1},splitLine:{show:!1},axisTick:{show:!1},axisLabel:{color:"#000000",fontWeight:"bold"}},{show:!1,inverse:!0,data:t,axisLabel:{textStyle:{fontSize:12,color:"#00aa00"}},axisTick:{show:!1},axisLine:{show:!1}}],series:[{type:"bar",yAxisIndex:0,data:t,barCategoryGap:50,barWidth:12,itemStyle:{normal:{barBorderRadius:6,color:function(e){const t=s.length;return s[e.dataIndex%t]}}}},{type:"bar",yAxisIndex:1,barCategoryGap:50,data:n,barWidth:16,itemStyle:{normal:{color:"none",borderColor:"#00c1de",borderWidth:2,barBorderRadius:6}},label:{normal:{show:!0,position:"right",formatter:"{b}条",color:"#000000"}}}]};return a.setOption(o),a},chart2(e,t){const r=_.Ts(e),a={color:["#28a745","#ffc107","#dc3545","#409EFF","#4de1cb"],tooltip:{trigger:"item",formatter:"{d}%【{c}条】",backgroundColor:"rgba(250, 250, 250, 0.6)",borderColor:"#00aa7f",textStyle:{color:"#000",fontSize:"16",fontWeight:"bold"}},legend:{orient:"vertical",right:30,bottom:5},series:[{type:"pie",radius:["50%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"16",fontWeight:"bold",color:"#00aa7f"}},labelLine:{show:!1},data:t}]};return r.setOption(a),r},chart3(e,t,r){const a=_.Ts(e);let n={title:{text:"通过率(%)",top:10,textStyle:{fontSize:14,color:"#00aa7f"}},grid:{top:50,bottom:10,left:20,right:20,containLabel:!0},tooltip:{trigger:"item",formatter:"{b} <br/> 通过率 ： {c}%",axisPointer:{lineStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(255,255,255,0)"},{offset:.5,color:"rgba(255,255,255,1)"},{offset:1,color:"rgba(255,255,255,0)"}],global:!1}}}},xAxis:[{type:"category",boundaryGap:!1,show:!0,axisLabel:{show:!1},axisLine:{lineStyle:{color:"#00aa7f"}},axisTick:{show:!1},data:r}],yAxis:[{show:!0,boundaryGap:!1,type:"value",axisLabel:{textStyle:{color:"#00aa7f"}},nameTextStyle:{color:"#fff",fontSize:12,lineHeight:40},splitLine:{lineStyle:{color:"#eef5f0"}},axisLine:{show:!0,lineStyle:{color:"#00aa7f"}},axisTick:{show:!0}}],series:[{name:"通过率",type:"line",smooth:!0,showSymbol:!0,symbolSize:8,zlevel:3,itemStyle:{color:"#19a3df",borderColor:"#a3c8d8"},lineStyle:{width:2,color:"#19a3df"},areaStyle:{color:new _.fA.W4(0,0,0,1,[{offset:0,color:"rgba(102, 208, 192, 0.8)"},{offset:.5,color:"rgba(157, 241, 241, 0.4)"},{offset:1,color:"rgba(0, 170, 127, 0.1)"}],!1)},data:t}]};return a.setOption(n),a},chart4(e,t,r){const a=_.Ts(e),n={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},tooltips:{trigger:"item",formatter:"{b}<br><b>用例数:{c}</b>",backgroundColor:"rgba(250, 250, 250, 0.6)",borderColor:"#00aa7f",textStyle:{color:"#424242",fontSize:"16"}},grid:{top:"15%",right:"3%",left:"3%",bottom:"3%"},xAxis:[{type:"category",data:r,axisLine:{lineStyle:{color:"#FFFFFF"}},axisLabel:{show:!1},axisTick:{show:!1}}],yAxis:[{axisLabel:{show:!1},axisTick:{show:!1},axisLine:{show:!1,lineStyle:{color:"rgba(0, 0, 0, 0.6)"}},splitLine:{lineStyle:{color:"rgba(255,255,255,0.12)"}}}],series:[{type:"bar",data:t,barWidth:"14px",itemStyle:{normal:{color:new _.fA.W4(0,0,0,1,[{offset:0,color:"rgba(0, 170, 127, 1.0)"},{offset:1,color:"rgba(169, 255, 205, 1.0)"}],!1)}},label:{normal:{show:!0,lineHeight:20,formatter:"{c}",position:"top",textStyle:{color:"#000000",fontSize:12}}}}]};a.setOption(n)}};const w=(0,a.Ef)(l);w.config.globalProperties.$api=b.A,w.config.globalProperties.$tools=T,w.config.globalProperties.$chart=v,h(w),w.use(m.A).use(d.A).mount("#app")}},t={};function r(a){var n=t[a];if(void 0!==n)return n.exports;var s=t[a]={id:a,loaded:!1,exports:{}};return e[a].call(s.exports,s,s.exports,r),s.loaded=!0,s.exports}r.m=e,function(){r.amdD=function(){throw new Error("define cannot be used indirect")}}(),function(){var e=[];r.O=function(t,a,n,s){if(!a){var o=1/0;for(u=0;u<e.length;u++){a=e[u][0],n=e[u][1],s=e[u][2];for(var p=!0,c=0;c<a.length;c++)(!1&s||o>=s)&&Object.keys(r.O).every(function(e){return r.O[e](a[c])})?a.splice(c--,1):(p=!1,s<o&&(o=s));if(p){e.splice(u--,1);var i=n();void 0!==i&&(t=i)}}return t}s=s||0;for(var u=e.length;u>0&&e[u-1][2]>s;u--)e[u]=e[u-1];e[u]=[a,n,s]}}(),function(){r.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return r.d(t,{a:t}),t}}(),function(){r.d=function(e,t){for(var a in t)r.o(t,a)&&!r.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}}(),function(){r.f={},r.e=function(e){return Promise.all(Object.keys(r.f).reduce(function(t,a){return r.f[a](e,t),t},[]))}}(),function(){r.u=function(e){return"js/"+(594===e?"about":e)+"."+{34:"cc22f467",61:"901e73d3",83:"9f48fcc9",103:"68fc1f3e",173:"7d533eeb",233:"76b1a057",238:"a4774b2a",264:"82cfd3a2",296:"59b2f0de",315:"dc71d75f",323:"00e52339",334:"8a7fa3d5",403:"711cb974",441:"d3f63c75",484:"ef522f0e",491:"19e3847b",519:"9d9bad22",520:"74183081",542:"f8adc9e4",569:"16f0327a",579:"9ae806a6",589:"cef650d2",594:"694707a0",619:"945114e0",627:"9a1b4e2e",628:"9aad9703",629:"2247f8c4",682:"93174899",704:"ba8a4384",719:"174a2baa",815:"3404655f",830:"4983047b",838:"b033fe3e",903:"f2638eca",911:"92175734",926:"8c4a395d"}[e]+".js"}}(),function(){r.miniCssF=function(e){return"css/"+(594===e?"about":e)+"."+{34:"56da1ca7",57:"dcf43fb6",61:"f6135b0f",83:"54579180",173:"61e144ef",233:"55cd552d",238:"2d5969ba",296:"b7c549ad",315:"389faf4e",323:"e026c19a",334:"f9fc9ff9",403:"6fd293ed",441:"00c0f980",484:"3d12ca4d",519:"2c33953d",520:"6b0c9ba6",542:"875127f7",569:"99e86671",579:"43290ce8",589:"62139b72",594:"3fa65682",619:"72cc6df9",627:"b9fd6dd5",628:"cc1585e8",719:"ed798533",838:"e76cf7a8",903:"c72c8302",911:"624879c0",926:"18927151"}[e]+".css"}}(),function(){r.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){var e={},t="frontend-web:";r.l=function(a,n,s,o){if(e[a])e[a].push(n);else{var p,c;if(void 0!==s)for(var i=document.getElementsByTagName("script"),u=0;u<i.length;u++){var l=i[u];if(l.getAttribute("src")==a||l.getAttribute("data-webpack")==t+s){p=l;break}}p||(c=!0,p=document.createElement("script"),p.charset="utf-8",p.timeout=120,r.nc&&p.setAttribute("nonce",r.nc),p.setAttribute("data-webpack",t+s),p.src=a),e[a]=[n];var d=function(t,r){p.onerror=p.onload=null,clearTimeout(m);var n=e[a];if(delete e[a],p.parentNode&&p.parentNode.removeChild(p),n&&n.forEach(function(e){return e(r)}),t)return t(r)},m=setTimeout(d.bind(null,void 0,{type:"timeout",target:p}),12e4);p.onerror=d.bind(null,p.onerror),p.onload=d.bind(null,p.onload),c&&document.head.appendChild(p)}}}(),function(){r.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){r.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){r.p="/"}(),function(){if("undefined"!==typeof document){var e=function(e,t,a,n,s){var o=document.createElement("link");o.rel="stylesheet",o.type="text/css",r.nc&&(o.nonce=r.nc);var p=function(r){if(o.onerror=o.onload=null,"load"===r.type)n();else{var a=r&&r.type,p=r&&r.target&&r.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+a+": "+p+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=a,c.request=p,o.parentNode&&o.parentNode.removeChild(o),s(c)}};return o.onerror=o.onload=p,o.href=t,a?a.parentNode.insertBefore(o,a.nextSibling):document.head.appendChild(o),o},t=function(e,t){for(var r=document.getElementsByTagName("link"),a=0;a<r.length;a++){var n=r[a],s=n.getAttribute("data-href")||n.getAttribute("href");if("stylesheet"===n.rel&&(s===e||s===t))return n}var o=document.getElementsByTagName("style");for(a=0;a<o.length;a++){n=o[a],s=n.getAttribute("data-href");if(s===e||s===t)return n}},a=function(a){return new Promise(function(n,s){var o=r.miniCssF(a),p=r.p+o;if(t(o,p))return n();e(a,p,null,n,s)})},n={524:0};r.f.miniCss=function(e,t){var r={34:1,57:1,61:1,83:1,173:1,233:1,238:1,296:1,315:1,323:1,334:1,403:1,441:1,484:1,519:1,520:1,542:1,569:1,579:1,589:1,594:1,619:1,627:1,628:1,719:1,838:1,903:1,911:1,926:1};n[e]?t.push(n[e]):0!==n[e]&&r[e]&&t.push(n[e]=a(e).then(function(){n[e]=0},function(t){throw delete n[e],t}))}}}(),function(){var e={524:0};r.f.j=function(t,a){var n=r.o(e,t)?e[t]:void 0;if(0!==n)if(n)a.push(n[2]);else if(57!=t){var s=new Promise(function(r,a){n=e[t]=[r,a]});a.push(n[2]=s);var o=r.p+r.u(t),p=new Error,c=function(a){if(r.o(e,t)&&(n=e[t],0!==n&&(e[t]=void 0),n)){var s=a&&("load"===a.type?"missing":a.type),o=a&&a.target&&a.target.src;p.message="Loading chunk "+t+" failed.\n("+s+": "+o+")",p.name="ChunkLoadError",p.type=s,p.request=o,n[1](p)}};r.l(o,c,"chunk-"+t,t)}else e[t]=0},r.O.j=function(t){return 0===e[t]};var t=function(t,a){var n,s,o=a[0],p=a[1],c=a[2],i=0;if(o.some(function(t){return 0!==e[t]})){for(n in p)r.o(p,n)&&(r.m[n]=p[n]);if(c)var u=c(r)}for(t&&t(a);i<o.length;i++)s=o[i],r.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return r.O(u)},a=self["webpackChunkfrontend_web"]=self["webpackChunkfrontend_web"]||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))}();var a=r.O(void 0,[504],function(){return r(83746)});a=r.O(a)})();
//# sourceMappingURL=app.042adfa8.js.map